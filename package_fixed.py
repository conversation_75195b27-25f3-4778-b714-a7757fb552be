# -*- coding: utf-8 -*-
"""
🔧 修复版打包程序
功能：强制确保img和配置文件被正确打包
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def package_with_manual_copy():
    """打包并手动复制缺失文件"""
    print("🔧 修复版打包程序")
    print("=" * 50)
    
    project_root = Path(__file__).parent
    
    # 1. 检查必要文件
    print("🔍 检查必要文件...")
    
    img_dir = project_root / "img"
    config_file = project_root / "app_config.json"
    
    if not img_dir.exists():
        print("❌ img目录不存在")
        return False
    
    if not config_file.exists():
        print("❌ app_config.json不存在")
        return False
    
    png_files = list(img_dir.glob("*.png"))
    print(f"✅ img目录存在，包含 {len(png_files)} 个图片文件")
    print(f"✅ 配置文件存在: {config_file}")
    
    # 2. 清理旧文件
    print("\n🗑️ 清理旧文件...")
    build_dir = project_root / "build"
    dist_dir = project_root / "dist"
    
    if build_dir.exists():
        shutil.rmtree(build_dir)
        print("  ✅ 已清理build目录")
    
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
        print("  ✅ 已清理dist目录")
    
    # 3. 安装PyInstaller
    print("\n📦 检查PyInstaller...")
    try:
        import PyInstaller
        print("  ✅ PyInstaller已安装")
    except ImportError:
        print("  📥 正在安装PyInstaller...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("  ✅ PyInstaller安装完成")
    
    # 4. 基础打包（不包含数据文件）
    print("\n🔨 第一步：基础打包...")
    
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=ins雷电中控",
        "--clean",
        "main.py"
    ]
    
    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode != 0:
            print("❌ 基础打包失败")
            print(f"错误信息: {result.stderr}")
            return False
        
        print("✅ 基础打包完成")
        
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return False
    
    # 5. 手动复制必要文件
    print("\n📁 第二步：手动复制必要文件...")
    
    dist_dir = project_root / "dist"
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    # 复制img目录
    dist_img = dist_dir / "img"
    try:
        shutil.copytree(img_dir, dist_img)
        print(f"✅ 已复制img目录: {dist_img}")
        
        # 验证复制结果
        copied_png = list(dist_img.glob("*.png"))
        print(f"  📸 复制了 {len(copied_png)} 个图片文件")
        
    except Exception as e:
        print(f"❌ 复制img目录失败: {e}")
        return False
    
    # 复制配置文件
    dist_config = dist_dir / "app_config.json"
    try:
        shutil.copy2(config_file, dist_config)
        print(f"✅ 已复制配置文件: {dist_config}")
        
    except Exception as e:
        print(f"❌ 复制配置文件失败: {e}")
        return False
    
    # 复制其他目录（如果存在）
    other_dirs = ["config", "data", "logs", "screenshots"]
    for dir_name in other_dirs:
        src_dir = project_root / dir_name
        if src_dir.exists():
            dst_dir = dist_dir / dir_name
            try:
                if src_dir.is_dir():
                    shutil.copytree(src_dir, dst_dir, dirs_exist_ok=True)
                    print(f"✅ 已复制目录: {dir_name}")
            except Exception as e:
                print(f"⚠️  复制目录 {dir_name} 失败: {e}")
    
    # 6. 验证最终结果
    print("\n🔍 验证最终结果...")
    
    exe_file = dist_dir / "ins雷电中控.exe"
    if not exe_file.exists():
        print("❌ exe文件不存在")
        return False
    
    size_mb = exe_file.stat().st_size / (1024 * 1024)
    print(f"✅ exe文件存在: {exe_file} ({size_mb:.1f}MB)")
    
    # 验证img目录
    if dist_img.exists():
        png_count = len(list(dist_img.glob("*.png")))
        print(f"✅ img目录存在，包含 {png_count} 个图片")
    else:
        print("❌ img目录仍然缺失")
        return False
    
    # 验证配置文件
    if dist_config.exists():
        print("✅ 配置文件存在")
    else:
        print("❌ 配置文件仍然缺失")
        return False
    
    # 显示最终目录结构
    print(f"\n📁 最终dist目录结构:")
    for item in sorted(dist_dir.iterdir()):
        if item.is_file():
            size_mb = item.stat().st_size / (1024 * 1024)
            print(f"  📄 {item.name} ({size_mb:.1f}MB)")
        else:
            sub_count = len(list(item.iterdir())) if item.is_dir() else 0
            print(f"  📁 {item.name}/ ({sub_count}个文件)")
    
    return True

def main():
    """主函数"""
    try:
        success = package_with_manual_copy()
        
        if success:
            print("\n🎉 修复版打包成功！")
            print("💡 所有必要文件都已正确包含")
            print("💡 图色识别功能应该正常工作")
            print("💡 输出文件: dist/ins雷电中控.exe")
        else:
            print("\n💥 修复版打包失败")
            print("💡 请检查错误信息")
        
        input("\n按Enter键退出...")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
