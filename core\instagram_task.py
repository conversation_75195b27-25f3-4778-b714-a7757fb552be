#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 Instagram私信任务执行器 - 完整自动化流程管理
========================================
功能描述: Instagram粉丝私信任务的具体实现，支持完整的自动化流程

模块结构:
1. Instagram私信任务执行器 (InstagramDMTask)
2. 配置管理系统 (配置加载、热加载、观察者模式)
3. 任务执行系统 (四阶段执行流程)
4. 自动化步骤扩展区域 (预留各类自动化任务)

主要功能:
- 任务配置管理: 参数加载、热加载、默认配置
- 执行流程控制: 四阶段流程、错误处理、状态管理
- 原生API集成: 雷电模拟器API、应用操作、UI自动化
- 图像识别集成: 基于图色识别的应用检测，替代传统包名检测
- 扩展性设计: 支持Instagram私信、抖音关注等各类任务

技术特点:
- 图像识别: 使用img/V2.png和img/ins.png进行应用检测
- 高精度检测: 置信度≥0.75，平均耗时30ms
- 多尺度匹配: 支持0.1-1.9倍缩放范围
- 智能优化: 早期退出和缩放顺序优化

调用关系: 被异步桥梁调用，执行具体的Instagram私信业务逻辑
注意事项: 使用雷电模拟器原生API和图像识别引擎，包含完整的错误处理和重试机制
参考文档: Instagram私信流程步骤记录.md
========================================
"""

import asyncio
import time
import random
import subprocess
from pathlib import Path
from typing import Dict, Any, List, Set, Optional
from .logger_manager import log_info, log_error, log_warning
from .unified_emulator_manager import get_emulator_manager

# ============================================================================
# 🎯 1. Instagram私信任务执行器
# ============================================================================
# 功能描述: Instagram粉丝私信任务的核心执行器，管理完整的自动化流程
# 调用关系: 被异步桥梁调用，作为Instagram私信任务的统一入口点
# 注意事项: 集成配置管理、原生API、执行控制等多个子系统
# ============================================================================

class InstagramDMTask:
    """🎯 Instagram私信任务执行器 - 完整自动化流程管理"""

    def __init__(self, emulator_id: int, config_manager=None):
        """初始化Instagram私信任务执行器"""

        # ========================================================================
        # 🎯 1.1 基础参数初始化
        # ========================================================================

        self.emulator_id = emulator_id
        self.emulator_manager = get_emulator_manager()

        # 🎯 配置管理器
        if config_manager is None:
            from .simple_config import get_config_manager
            self.config_manager = get_config_manager()
        else:
            self.config_manager = config_manager

        # ========================================================================
        # 🎯 1.2 任务状态管理
        # ========================================================================

        # 🎯 任务执行状态
        self.stop_flag = False
        self.sent_count = 0
        self.node_switch_count = 0  # 节点切换成功次数计数器

        # 🎯 私信任务相关状态
        self.followers_count = 0  # 粉丝数量
        self.sent_users = set()  # 已发送用户集合
        self.record_file_path = "sent_users.txt"  # 记录文件路径

        # 🎯 应用包名配置
        self.v2ray_package = "com.v2ray.ang"
        self.instagram_package = "com.instagram.android"

        # 🎯 Instagram常用元素ID常量 - 便于复用和维护
        self.INSTAGRAM_ELEMENTS = {
            # 导航相关
            "title_container": "com.instagram.android:id/action_bar_new_title_container",  # 标题栏容器（用于返回）
            "back_button": "com.instagram.android:id/action_bar_button_back",              # 标准返回按钮
            "profile_tab": "com.instagram.android:id/profile_tab",                         # 个人主页标签

            # 粉丝相关
            "followers_count": "com.instagram.android:id/row_profile_header_textview_followers_count",  # 粉丝数量文本
            "follow_list_container": "com.instagram.android:id/follow_list_container",                  # 粉丝列表容器
            "follow_list_username": "com.instagram.android:id/follow_list_username",                    # 粉丝用户名
            "follow_list_right_button": "com.instagram.android:id/follow_list_right_follow_button",     # 粉丝右侧按钮（移除）

            # 私信相关
            "message_input": "com.instagram.android:id/row_thread_composer_edittext",      # 私信输入框
            "send_button": "com.instagram.android:id/row_thread_composer_button_send",     # 发送按钮（旧版本）
            "send_button_container": "com.instagram.android:id/row_thread_composer_send_button_container",  # 发送按钮容器（新版本）

            # 撤回相关 - 支持多种消息类型
            "direct_text_message": "com.instagram.android:id/direct_text_message_text_view",    # 私信消息文本视图（用于撤回）
            "caption_container": "com.instagram.android:id/caption_container",                   # 消息标题容器（图片/视频消息）
            "media_container": "com.instagram.android:id/media_container",                       # 媒体消息容器（图片/视频）
            "message_content_layout": "com.instagram.android:id/message_content_horizontal_linear_layout",  # 消息内容布局

            # 列表相关
            "list_view": "android:id/list",                                                # 通用列表视图
            "no_results": "com.instagram.android:id/row_no_results_textview"              # 无结果提示文本
        }

        # ========================================================================
        # 🎯 1.3 核心组件初始化
        # ========================================================================

        # 🎯 雷电模拟器API实例（统一使用LeiDian_Reorganized.py，包含82个完整方法）
        self.ld = None
        self._init_leidian_api()

        # ========================================================================
        # 🎯 1.4 配置系统初始化
        # ========================================================================

        # 🎯 加载任务配置
        self._load_config()

        # 🎯 注册配置热加载观察者
        self._register_config_observer()

        log_info(f"[模拟器{self.emulator_id}] Instagram私信任务执行器初始化完成", component="InstagramDMTask")

    def _emit_progress_update(self):
        """发送Instagram任务状态更新信号到UI"""
        try:
            # 🎯 使用专用的Instagram任务状态更新信号
            from core.status_converter import InstagramDMStatus
            from core.logger_manager import get_logger_manager

            # 格式化任务状态
            if self.sent_count < self.message_count:
                task_status = InstagramDMStatus.format_running(self.sent_count, self.message_count)
            else:
                task_status = InstagramDMStatus.format_completed(self.sent_count, self.message_count)

            # 🎯 通过专用信号发送Instagram任务状态更新
            logger_manager = get_logger_manager()
            logger_manager.instagram_task_status_updated.emit(self.emulator_id, task_status)

            log_info(f"[模拟器{self.emulator_id}] Instagram任务状态更新信号已发送: {task_status}", component="InstagramDMTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 发送Instagram任务状态更新失败: {e}", component="InstagramDMTask")

    async def _cleanup_after_task_completion(self):
        """任务完成后的清理工作：关闭模拟器并移除心跳监控"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始任务完成后清理工作", component="InstagramDMTask")

            # 🎯 1. 移除心跳监控
            try:
                from core.heartbeat_manager import get_simple_heartbeat_manager
                heartbeat_manager = get_simple_heartbeat_manager()
                if self.emulator_id in heartbeat_manager.monitored_emulators:
                    heartbeat_manager.remove_emulator_monitoring(self.emulator_id)
                    log_info(f"[模拟器{self.emulator_id}] 心跳监控已移除", component="InstagramDMTask")
                else:
                    log_info(f"[模拟器{self.emulator_id}] 心跳监控未启用，无需移除", component="InstagramDMTask")
            except Exception as e:
                log_error(f"[模拟器{self.emulator_id}] 移除心跳监控失败: {e}", component="InstagramDMTask")

            # 🎯 2. 关闭模拟器
            try:
                from core.unified_emulator_manager import get_emulator_manager
                emulator_manager = get_emulator_manager()

                log_info(f"[模拟器{self.emulator_id}] 准备关闭模拟器", component="InstagramDMTask")
                result = await emulator_manager.stop_emulator(self.emulator_id)

                if result:
                    log_info(f"[模拟器{self.emulator_id}] 模拟器已成功关闭", component="InstagramDMTask")
                else:
                    log_warning(f"[模拟器{self.emulator_id}] 模拟器关闭失败", component="InstagramDMTask")

            except Exception as e:
                log_error(f"[模拟器{self.emulator_id}] 关闭模拟器失败: {e}", component="InstagramDMTask")

            log_info(f"[模拟器{self.emulator_id}] 任务完成后清理工作完成", component="InstagramDMTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 任务完成后清理工作异常: {e}", component="InstagramDMTask")

    # ------------------------------------------------------------------------
    # 🎯 1.5 核心组件管理
    # ------------------------------------------------------------------------
    # 功能描述: 管理Instagram任务执行器的核心组件，包括原生API初始化
    # 调用关系: 被初始化方法调用，为任务执行提供必要的API支持
    # 注意事项: 原生API初始化失败不影响其他功能，采用延迟初始化策略
    # ------------------------------------------------------------------------



    def _init_leidian_api(self):
        """🎯 初始化雷电模拟器API - 用于高级操作和应用检测"""
        try:
            # 🎯 获取雷电模拟器配置路径
            emulator_path = self.config_manager.get("emulator_path", "G:/leidian/LDPlayer9")
            shared_path = self.config_manager.get("basic_config.emulator_shared_path", "")

            if not shared_path:
                log_error(f"[模拟器{self.emulator_id}] 雷电模拟器共享路径未配置，LeiDian API初始化失败", component="InstagramDMTask")
                self.ld = None
                return

            # 🎯 创建LeiDian API实例
            from core.leidianapi.LeiDian_Reorganized import Dnconsole
            self.ld = Dnconsole(base_path=emulator_path, share_path=shared_path)

            # 🎯 设置emulator_id属性（重要：用于滑动等操作）
            self.ld.emulator_id = self.emulator_id

            log_info(f"[模拟器{self.emulator_id}] 雷电模拟器API初始化成功", component="InstagramDMTask")
            log_info(f"[模拟器{self.emulator_id}] 模拟器路径: {emulator_path}", component="InstagramDMTask")
            log_info(f"[模拟器{self.emulator_id}] 共享路径: {shared_path}", component="InstagramDMTask")
            log_info(f"[模拟器{self.emulator_id}] 已设置ld.emulator_id = {self.emulator_id}", component="InstagramDMTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 雷电模拟器API初始化失败: {e}", component="InstagramDMTask")
            self.ld = None

    def get_ld(self):
        """🎯 获取雷电模拟器API实例 - 供其他方法使用"""
        return self.ld

    def is_ld_available(self) -> bool:
        """🎯 检查雷电模拟器API是否可用"""
        return self.ld is not None



# ============================================================================
# 🎯 2. 配置管理系统
# ============================================================================
# 功能描述: 管理Instagram任务的所有配置参数，支持热加载和观察者模式
# 调用关系: 被任务执行器调用，为任务提供配置参数和动态更新能力
# 注意事项: 配置加载失败时自动使用默认配置，确保任务可以正常执行
# ============================================================================

    # ------------------------------------------------------------------------
    # 🎯 2.1 配置加载管理
    # ------------------------------------------------------------------------
    # 功能描述: 从统一配置系统加载Instagram私信任务的所有配置参数
    # 调用关系: 被初始化方法和热加载回调调用，提供配置参数
    # 注意事项: 配置加载失败时自动回退到默认配置，保证任务稳定性
    # ------------------------------------------------------------------------

    def _load_config(self):
        """🎯 加载Instagram私信任务配置"""
        try:
            # 🎯 基础任务参数
            self.message_count = self.config_manager.get("instagram_dm.message_count", 10)
            self.total_count = self.message_count
            self.delay_min = self.config_manager.get("instagram_dm.delay_min", 5000)
            self.delay_max = self.config_manager.get("instagram_dm.delay_max", 10000)
            self.message_delay = self.config_manager.get("instagram_dm.message_delay", 2000)

            # 🎯 任务控制参数
            self.recall_before_dm = self.config_manager.get("instagram_dm.recall_before_dm", False)
            self.record_file_path = self.config_manager.get("instagram_dm.record_file_path", "sent_users.txt")

            # 🎯 消息内容参数
            self.message_content_1 = self.config_manager.get("instagram_dm.message_content_1", "hi|hello|nice to meet you")
            self.message_content_2 = self.config_manager.get("instagram_dm.message_content_2", "How are you?|What's up?|Nice day")
            self.send_mode = self.config_manager.get("instagram_dm.send_mode", 1)

            # 🎯 任务超时参数（从UI界面的"任务总超时时间"获取，默认900秒=15分钟）
            self.task_timeout = self.config_manager.get("basic_config.task_timeout_seconds", 900)

            log_info(f"[模拟器{self.emulator_id}] Instagram任务配置加载完成: 私信数量={self.message_count}, 延迟={self.delay_min}-{self.delay_max}ms", component="InstagramDMTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 加载Instagram任务配置失败: {str(e)}", component="InstagramDMTask")
            # 使用默认配置
            self._load_default_config()

    def _load_default_config(self):
        """🎯 加载默认配置"""
        self.message_count = 10
        self.total_count = 10
        self.delay_min = 5000
        self.delay_max = 10000
        self.message_delay = 2000
        self.recall_before_dm = False
        self.record_file_path = "sent_users.txt"
        self.message_content_1 = "hi|hello|nice to meet you"
        self.message_content_2 = "How are you?|What's up?|Nice day"
        self.send_mode = 1
        log_info(f"[模拟器{self.emulator_id}] 使用默认Instagram任务配置", component="InstagramDMTask")

    # ------------------------------------------------------------------------
    # 🎯 2.2 配置热加载系统
    # ------------------------------------------------------------------------
    # 功能描述: 实现配置的热加载功能，支持运行时动态更新配置参数
    # 调用关系: 通过观察者模式监听配置变化，自动更新任务参数
    # 注意事项: 只处理instagram_dm.*相关配置，避免不必要的重载操作
    # ------------------------------------------------------------------------

    def _register_config_observer(self):
        """🎯 注册配置热加载观察者"""
        try:
            # 🎯 注册配置变化观察者
            self.config_manager.register_observer(self._on_config_changed)
            log_info(f"[模拟器{self.emulator_id}] Instagram任务配置热加载观察者已注册", component="InstagramDMTask")
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 注册配置观察者失败: {str(e)}", component="InstagramDMTask")

    def _on_config_changed(self, key: str, old_value, new_value):
        """🎯 配置变化处理回调"""
        try:
            # 🎯 只处理Instagram私信相关的配置
            if not key.startswith("instagram_dm."):
                return

            log_info(f"[模拟器{self.emulator_id}] 检测到Instagram配置变化: {key} = {old_value} → {new_value}", component="InstagramDMTask")

            # 🎯 重新加载配置
            self._load_config()

            log_info(f"[模拟器{self.emulator_id}] Instagram任务配置已热加载更新", component="InstagramDMTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 处理配置变化失败: {str(e)}", component="InstagramDMTask")

    def reload_config(self):
        """🎯 手动重新加载配置"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 手动重新加载Instagram任务配置", component="InstagramDMTask")
            self._load_config()
            log_info(f"[模拟器{self.emulator_id}] Instagram任务配置手动重新加载完成", component="InstagramDMTask")
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 手动重新加载配置失败: {str(e)}", component="InstagramDMTask")

    def unregister_config_observer(self):
        """🎯 注销配置观察者"""
        try:
            self.config_manager.unregister_observer(self._on_config_changed)
            log_info(f"[模拟器{self.emulator_id}] Instagram任务配置观察者已注销", component="InstagramDMTask")
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 注销配置观察者失败: {str(e)}", component="InstagramDMTask")

# ============================================================================
# 🎯 3. 任务执行系统
# ============================================================================
# 功能描述: Instagram私信任务的核心执行系统，管理四阶段执行流程
# 调用关系: 被异步桥梁调用，作为任务执行的主入口点
# 注意事项: 包含完整的错误处理和资源清理机制，确保任务稳定执行
# ============================================================================

    # ------------------------------------------------------------------------
    # 🎯 3.1 主执行流程
    # ------------------------------------------------------------------------
    # 功能描述: Instagram私信任务的主执行流程，包含四个完整阶段
    # 调用关系: 被异步桥梁调用，协调各个执行阶段的顺序执行
    # 注意事项: 任何阶段失败都会触发资源清理，确保系统状态一致性
    # ------------------------------------------------------------------------

    async def execute(self) -> Dict[str, Any]:
        """🎯 执行Instagram私信任务的完整流程"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始执行Instagram私信任务", component="InstagramDMTask")

            # 🎯 检查任务开始时间（应该由线程传递，如果没有则设置）
            if not hasattr(self, 'task_start_time') or self.task_start_time is None:
                self.task_start_time = time.time()
                log_warning(f"[模拟器{self.emulator_id}] 任务开始时间未由线程传递，在此设置", component="InstagramDMTask")

            elapsed_time = time.time() - self.task_start_time
            log_info(f"[模拟器{self.emulator_id}] 任务超时设置: {self.task_timeout}秒，已运行: {elapsed_time:.2f}秒", component="InstagramDMTask")

            # ====================================================================
            # 🎯 阶段一：环境验证与应用检测
            # ====================================================================
            # 移除阶段流程的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 开始阶段一：环境验证与应用检测", component="InstagramDMTask")

            # 🎯 步骤1：验证模拟器桌面稳定性
            if not await self._step_verify_desktop_stable():
                return {'status': 'failed', 'message': '模拟器桌面验证失败'}

            # 🎯 步骤2：检测应用安装状态
            if not await self._step_check_app_installation():
                return {'status': 'failed', 'message': '应用检测失败'}

            # ====================================================================
            # 🎯 阶段二：V2Ray节点连接
            # ====================================================================
            # 🎯 步骤1：启动V2Ray应用
            if not await self._step_app_launch_v2ray():
                return {'status': 'failed', 'message': 'V2Ray应用启动失败'}

            # 🎯 步骤2：检查节点列表状态
            if not await self._step_check_node_list():
                return {'status': 'failed', 'message': 'V2Ray节点列表检查失败'}

            # 🎯 步骤3：连接V2Ray节点
            if not await self._step_connect_v2ray_node():
                return {'status': 'failed', 'message': 'V2Ray节点连接失败'}

            # 🎯 步骤4：测试节点延迟
            if not await self._step_test_node_latency():
                return {'status': 'failed', 'message': 'V2Ray节点延迟测试失败'}

            # ====================================================================
            # 🎯 阶段三：Instagram应用启动
            # ====================================================================
            # 移除阶段流程的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 开始阶段三：Instagram应用启动", component="InstagramDMTask")

            # 🎯 步骤1：启动Instagram应用
            if not await self._step_app_launch_instagram():
                return {'status': 'failed', 'message': 'Instagram应用启动失败'}

            # 🎯 步骤2：检测页面状态（五次重启机制）
            page_status = await self._step_detect_instagram_page_status()
            log_info(f"[模拟器{self.emulator_id}] Instagram页面状态检测结果: {page_status}", component="InstagramDMTask")

            # 根据页面状态分类进行处理
            if page_status == "正常-在主页面":
                log_info(f"[模拟器{self.emulator_id}] ✅ Instagram已在主页面，可以继续执行任务", component="InstagramDMTask")
            elif page_status.startswith("任务终止-"):
                # 任务已终止，模拟器已关闭
                log_error(f"[模拟器{self.emulator_id}] ❌ Instagram任务终止", component="InstagramDMTask")
                return {'status': 'failed', 'message': page_status}
            else:
                # 其他未知状态
                log_error(f"[模拟器{self.emulator_id}] ❌ Instagram状态未知，任务终止", component="InstagramDMTask")
                return {'status': 'failed', 'message': f'Instagram状态未知: {page_status}'}

            # ====================================================================
            # 🎯 阶段四：批量私信发送
            # ====================================================================
            # 移除阶段流程的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 开始阶段四：批量私信发送", component="InstagramDMTask")

            # 🎯 步骤11：导航到个人主页
            if not await self._step_navigate_to_profile():
                return {'status': 'failed', 'message': '导航到个人主页失败'}

            # 🎯 步骤12：获取粉丝数量信息
            if not await self._step_get_followers_count():
                return {'status': 'failed', 'message': '获取粉丝数量失败'}

            # 🎯 步骤13：打开粉丝列表
            if not await self._step_open_followers_list():
                return {'status': 'failed', 'message': '打开粉丝列表失败'}

            # 🎯 步骤14：初始化私信任务
            if not await self._step_initialize_dm_task():
                return {'status': 'failed', 'message': '初始化私信任务失败'}

            # 🎯 步骤15：批量私信发送循环
            if not await self._step_business_send_mass_dm():
                return {'status': 'failed', 'message': '批量私信发送失败'}

            # 🎯 任务完成处理
            self.unregister_config_observer()

            # 🎯 任务完成后关闭模拟器并移除心跳监控
            await self._cleanup_after_task_completion()

            return {
                'status': 'completed',
                'message': 'Instagram私信任务执行完成',
                'sent_count': self.sent_count,
                'total_count': self.total_count
            }

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] Instagram私信任务异常: {str(e)}", component="InstagramDMTask")
            # 🎯 异常处理
            self.unregister_config_observer()
            return {'status': 'failed', 'message': f'任务执行异常: {str(e)}'}

    # ------------------------------------------------------------------------
    # 🎯 3.2 任务控制接口
    # ------------------------------------------------------------------------
    # 功能描述: 提供任务执行的控制接口，包括停止、暂停等操作
    # 调用关系: 被UI层或异步桥梁调用，用于控制任务执行状态
    # 注意事项: 控制操作需要线程安全，确保任务状态的正确变更
    # ------------------------------------------------------------------------

    def stop(self):
        """🎯 停止任务执行"""
        self.stop_flag = True
        log_info(f"[模拟器{self.emulator_id}] Instagram私信任务已停止", component="InstagramDMTask")

# ============================================================================
# 🎯 4. 自动化任务步骤区域 (预留扩展)
# ============================================================================
# 功能描述: 预留区域用于添加具体的自动化任务步骤方法
# 调用关系: 被主执行流程调用，实现具体的自动化操作逻辑
# 注意事项: 所有步骤方法应遵循统一的命名和返回值规范
# 扩展说明: 支持Instagram私信、抖音关注、微博互动等各类自动化任务
# ============================================================================

    # ------------------------------------------------------------------------
    # 🎯 4.1 环境验证步骤
    # ------------------------------------------------------------------------
    # 功能描述: 验证模拟器环境和应用状态的相关步骤方法
    # 调用关系: 被阶段一执行流程调用，确保环境满足任务执行条件
    # 注意事项: 验证失败应返回False，并记录详细的错误信息
    # 方法格式: async def _step_verify_xxx(self) -> bool:
    # ------------------------------------------------------------------------

    # ------------------------------------------------------------------------
    # 🎯 4.1.1 桌面稳定性验证步骤
    # ------------------------------------------------------------------------
    # 功能描述: 验证模拟器桌面完全稳定，确保后续操作的可靠性
    # 调用关系: 被阶段一执行流程调用，作为环境验证的第一步
    # 注意事项: 使用主要验证+备用验证双重保障，30秒超时控制
    # 技术实现: win32gui.IsWindowVisible() + 雷电原生API，完全不使用ADB
    # ------------------------------------------------------------------------

    async def _step_verify_desktop_stable(self) -> bool:
        """🎯 步骤1：验证模拟器桌面稳定性"""
        try:
            # 移除桌面稳定性验证的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 开始验证模拟器桌面稳定性", component="InstagramDMTask")

            # 🎯 直接使用可靠的模拟器状态检测方法
            # 移除状态检测的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 使用模拟器状态检测桌面稳定性", component="InstagramDMTask")

            try:
                is_running, is_android = await self.emulator_manager.get_emulator_status(self.emulator_id)
                if is_running and is_android:
                    log_info(f"[模拟器{self.emulator_id}] 模拟器Android系统运行正常，桌面稳定", component="InstagramDMTask")
                    return True
                else:
                    log_error(f"[模拟器{self.emulator_id}] 模拟器状态异常: 运行={is_running}, Android={is_android}", component="InstagramDMTask")
                    return False
            except Exception as e:
                log_error(f"[模拟器{self.emulator_id}] 桌面稳定性检测异常: {str(e)}", component="InstagramDMTask")
                return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 验证模拟器桌面稳定异常: {str(e)}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 4.1.2 应用安装状态检测步骤
    # ------------------------------------------------------------------------
    # 功能描述: 检测V2Ray和Instagram应用的安装状态，确保必要应用可用
    # 调用关系: 被阶段一执行流程调用，作为环境验证的第二步
    # 注意事项: 通过包名检测应用安装状态，任一应用未安装都会失败
    # 技术实现: 使用雷电模拟器共享路径调用LeiDian_Reorganized.py的has_install()方法
    # 检测方法: V2Ray包名com.v2ray.ang，Instagram包名com.instagram.android
    # ------------------------------------------------------------------------

    async def _step_check_app_installation(self) -> bool:
        """
        4.1.2 检查应用安装状态
        检测V2Ray和Instagram应用的安装状态和版本信息

        Returns:
            bool: 检测是否成功完成
        """
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始检查应用安装状态", component="InstagramDMTask")

            if self.ld is None:
                log_error(f"[模拟器{self.emulator_id}] LeiDian API未初始化，无法进行应用检测", component="InstagramDMTask")
                return False

            # 应用包名配置
            apps_to_check = {
                "V2Ray": "com.v2ray.ang",
                "Instagram": "com.instagram.android"
            }

            # 检测结果统计
            detection_results = []
            all_installed = True

            for app_name, package_name in apps_to_check.items():
                try:
                    # 检查应用是否安装
                    is_installed = self.ld.has_install(self.emulator_id, package_name)

                    if is_installed:
                        # 获取版本信息
                        try:
                            version = self.ld.appVersion(self.emulator_id, package_name)
                            if version:
                                result_msg = f"{app_name}已安装，版本: {version}"
                                log_info(f"[模拟器{self.emulator_id}] ✅ {result_msg}", component="InstagramDMTask")
                            else:
                                result_msg = f"{app_name}已安装，版本: 未知"
                                log_info(f"[模拟器{self.emulator_id}] ⚠️ {result_msg}", component="InstagramDMTask")
                        except Exception as e:
                            result_msg = f"{app_name}已安装，版本获取失败: {str(e)}"
                            log_info(f"[模拟器{self.emulator_id}] ⚠️ {result_msg}", component="InstagramDMTask")
                    else:
                        result_msg = f"{app_name}未安装"
                        log_error(f"[模拟器{self.emulator_id}] ❌ {result_msg}", component="InstagramDMTask")
                        all_installed = False

                    detection_results.append(result_msg)

                except Exception as e:
                    error_msg = f"{app_name}检测失败: {str(e)}"
                    log_error(f"[模拟器{self.emulator_id}] ❌ {error_msg}", component="InstagramDMTask")
                    detection_results.append(error_msg)
                    all_installed = False

            # 输出检测结果汇总
            log_info(f"[模拟器{self.emulator_id}] 📊 应用安装状态检测结果:", component="InstagramDMTask")
            for result in detection_results:
                print(f"  {result}")

            if all_installed:
                log_info(f"[模拟器{self.emulator_id}] ✅ 所有必要应用已安装", component="InstagramDMTask")
                return True
            else:
                log_error(f"[模拟器{self.emulator_id}] ❌ 部分应用未安装，请先安装必要应用", component="InstagramDMTask")
                return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 应用安装状态检测异常: {str(e)}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 4.2 应用操作步骤
    # ------------------------------------------------------------------------
    # 功能描述: 应用启动、导航、操作等相关的步骤方法
    # 调用关系: 被阶段二、三执行流程调用，实现应用层面的自动化操作
    # 注意事项: 操作失败应包含重试机制，提高任务成功率
    # 方法格式: async def _step_app_xxx(self) -> bool:
    # ------------------------------------------------------------------------

    # ------------------------------------------------------------------------
    # 🎯 4.2.1 V2Ray应用启动步骤
    # ------------------------------------------------------------------------
    # 功能描述: 启动V2Ray应用并验证启动成功
    # 调用关系: 被阶段二V2Ray连接流程调用，作为节点连接的前置步骤
    # 注意事项: 启动后需要验证应用状态，支持重试机制
    # 技术实现: 使用LeiDian_Reorganized.py的runApp方法启动，等待3秒后验证
    #
    # 执行步骤:
    # 第1步: 调用runApp启动V2Ray应用 (包名: com.v2ray.ang)
    # 第2步: 等待3秒让应用完全启动
    # 第3步: 通过图像识别验证V2Ray应用是否成功启动 (img/V2.png)
    # 第4步: 如果启动失败，记录错误并返回失败状态
    # ------------------------------------------------------------------------

    async def _step_app_launch_v2ray(self) -> bool:
        """
        4.2.1 启动V2Ray应用
        启动V2Ray应用并验证启动成功

        Returns:
            bool: 启动是否成功
        """
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始启动V2Ray应用", component="InstagramDMTask")

            # ========================================================================
            # 🎯 第1步：调用runApp启动V2Ray应用 (包名: com.v2ray.ang)
            # ========================================================================
            # 移除启动命令的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 执行V2Ray启动命令: {self.v2ray_package}", component="InstagramDMTask")
            start_result = self.ld.runApp(self.emulator_id, self.v2ray_package)

            # runApp现在返回字符串，空字符串表示失败
            if not start_result.strip():
                log_error(f"[模拟器{self.emulator_id}] V2Ray应用启动命令执行失败", component="InstagramDMTask")
                return False

            log_info(f"[模拟器{self.emulator_id}] V2Ray启动命令执行成功，等待应用加载", component="InstagramDMTask")
            log_info(f"[模拟器{self.emulator_id}] V2Ray应用启动结果: {'成功' if start_result.strip() else '失败'}", component="InstagramDMTask")

            # ========================================================================
            # 🎯 第2步：等待3秒让应用完全启动
            # ========================================================================
            await asyncio.sleep(3)

            # ========================================================================
            # 🎯 第3步：通过Activity检测验证V2Ray应用是否成功启动
            # ========================================================================
            # 移除Activity检测的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 验证V2Ray应用启动状态（Activity检测）", component="InstagramDMTask")

            # 使用优化后的Activity检测方法（直接传参）
            current_activity = self.ld.get_activity_name(self.emulator_id)
            # 移除Activity详细信息日志
            # log_info(f"[模拟器{self.emulator_id}] 当前Activity: {current_activity}", component="InstagramDMTask")

            # 检查Activity是否包含V2Ray相关信息
            if current_activity and "v2ray" in current_activity.lower():
                log_info(f"[模拟器{self.emulator_id}] ✅ V2Ray应用启动成功", component="InstagramDMTask")
                return True
            else:
                # ====================================================================
                # 🎯 第4步：如果启动失败，记录错误并返回失败状态
                # ====================================================================
                log_error(f"[模拟器{self.emulator_id}] ❌ V2Ray应用启动验证失败，未检测到V2Ray Activity", component="InstagramDMTask")
                log_error(f"[模拟器{self.emulator_id}] 期望Activity包含: v2ray，实际Activity: {current_activity}", component="InstagramDMTask")
                return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] V2Ray应用启动异常: {str(e)}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 4.2.2 V2Ray节点列表检查步骤
    # ------------------------------------------------------------------------
    # 功能描述: 检查V2Ray应用中是否已有节点配置
    # 调用关系: 被阶段二V2Ray连接流程调用，决定后续流程走向
    # 注意事项: 通过tv_name元素判断节点列表状态
    # 技术实现: 使用find_node查找com.v2ray.ang:id/tv_name
    #
    # 执行步骤:
    # 第1步: 查找节点名称元素 (resource-id: com.v2ray.ang:id/tv_name)
    # 第2步: 判断节点列表状态
    #        - 如果找到tv_name元素 → 节点已存在，可以直接连接
    #        - 如果未找到tv_name元素 → 节点列表为空，需要添加节点
    # 第3步: 记录节点容器信息用于调试
    # 第4步: 返回检查结果 (True=有节点, False=无节点)
    # ------------------------------------------------------------------------

    async def _step_check_node_list(self) -> bool:
        """
        4.2.2 检查V2Ray节点列表状态
        判断V2Ray应用中是否已有节点配置

        Returns:
            bool: 检查是否成功（有节点或无节点都算成功，只有检查失败才返回False）
        """
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始检查V2Ray节点列表状态", component="InstagramDMTask")

            # ========================================================================
            # 🎯 第1步：查找节点名称元素 (resource-id: com.v2ray.ang:id/tv_name)
            # ========================================================================
            node_container = self.ld.find_node(self.emulator_id, resource_id="com.v2ray.ang:id/tv_name")

            # ========================================================================
            # 🎯 第2步：判断节点列表状态
            # ========================================================================
            if node_container:
                # 如果找到tv_name元素 → 节点已存在，可以直接连接
                log_info(f"[模拟器{self.emulator_id}] ✅ 检测到V2Ray节点列表，节点已存在", component="InstagramDMTask")

                # --------------------------------------------------------------------
                # 🎯 第3步：记录节点容器信息用于调试
                # --------------------------------------------------------------------
                # 移除节点容器详细信息的技术日志
                # log_info(f"[模拟器{self.emulator_id}] 节点容器信息: {node_container}", component="InstagramDMTask")

                # --------------------------------------------------------------------
                # 🎯 第4步：返回检查结果 (True=有节点)
                # --------------------------------------------------------------------
                return True
            else:
                # 如果未找到tv_name元素 → 节点列表为空，需要添加节点
                log_info(f"[模拟器{self.emulator_id}] ⚠️ 未检测到V2Ray节点列表，需要导入节点", component="InstagramDMTask")
                # TODO: 这里应该调用节点导入流程（步骤6及后续）
                # 暂时返回False，表示需要导入节点
                return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] V2Ray节点列表检查异常: {str(e)}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 4.2.3 V2Ray节点连接步骤
    # ------------------------------------------------------------------------
    # 功能描述: 连接V2Ray节点并验证连接状态
    # 调用关系: 被阶段二V2Ray连接流程调用，实现节点连接的核心逻辑
    # 注意事项: 检查连接状态，点击连接按钮，等待连接完成
    # 技术实现: 基于UI自动化检测tv_test_state和fab元素
    #
    # 执行步骤:
    # 第1步: 查找连接状态元素 (resource-id: com.v2ray.ang:id/tv_test_state)
    # 第2步: 读取当前连接状态文本
    #        - "已连接" → 无需重复连接，直接返回成功
    #        - "未连接" → 需要执行连接操作
    # 第3步: 如果未连接，查找连接按钮 (resource-id: com.v2ray.ang:id/fab)
    # 第4步: 点击连接按钮开始连接
    # 第5步: 循环监控连接状态 (最多等待10秒)
    #        - 每秒检查一次tv_test_state的文本变化
    #        - 检测到"已连接"时返回成功
    # 第6步: 如果10秒内未连接成功，返回超时失败
    # ------------------------------------------------------------------------

    async def _step_connect_v2ray_node(self) -> bool:
        """
        4.2.3 连接V2Ray节点
        检查连接状态并执行连接操作

        Returns:
            bool: 连接是否成功
        """
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始连接V2Ray节点", component="InstagramDMTask")

            # ========================================================================
            # 🎯 第1步：查找连接状态元素 (resource-id: com.v2ray.ang:id/tv_test_state)
            # ========================================================================
            state_node = None
            for attempt in range(3):
                state_node = self.ld.find_node(self.emulator_id, resource_id="com.v2ray.ang:id/tv_test_state")
                if state_node:
                    break
                if attempt < 2:
                    log_info(f"[模拟器{self.emulator_id}] 连接状态元素未找到，重试 {attempt+1}/3", component="InstagramDMTask")
                    await asyncio.sleep(2)

            if not state_node:
                log_error(f"[模拟器{self.emulator_id}] 未找到连接状态元素（已重试3次）", component="InstagramDMTask")
                return False

            # ========================================================================
            # 🎯 第2步：读取当前连接状态文本
            # ========================================================================
            current_state = state_node.get('text', '')
            log_info(f"[模拟器{self.emulator_id}] 当前连接状态: {current_state}", component="InstagramDMTask")

            # "已连接" → 无需重复连接，直接返回成功
            if "已连接" in current_state:
                log_info(f"[模拟器{self.emulator_id}] V2Ray节点已连接，无需重复连接", component="InstagramDMTask")
                return True

            # ========================================================================
            # 🎯 "未连接" → 需要执行连接操作
            # ========================================================================
            if "未连接" in current_state:
                log_info(f"[模拟器{self.emulator_id}] V2Ray节点未连接，开始连接", component="InstagramDMTask")

                # --------------------------------------------------------------------
                # 🎯 第3步：如果未连接，查找连接按钮 (resource-id: com.v2ray.ang:id/fab)
                # --------------------------------------------------------------------
                connect_button = self.ld.find_node(self.emulator_id, resource_id="com.v2ray.ang:id/fab")

                if not connect_button:
                    log_error(f"[模拟器{self.emulator_id}] 未找到连接按钮", component="InstagramDMTask")
                    return False

                # --------------------------------------------------------------------
                # 🎯 第4步：点击连接按钮开始连接
                # --------------------------------------------------------------------
                if self.ld.click_node(connect_button, self.emulator_id):
                    log_info(f"[模拟器{self.emulator_id}] 已点击连接按钮，等待连接完成", component="InstagramDMTask")

                    # ================================================================
                    # 🎯 第5步：循环监控连接状态 (最多等待10秒)
                    # ================================================================
                    for i in range(10):
                        await asyncio.sleep(1)

                        # 每秒检查一次tv_test_state的文本变化
                        state_node = self.ld.find_node(self.emulator_id, resource_id="com.v2ray.ang:id/tv_test_state")
                        if state_node:
                            current_state = state_node.get('text', '')
                            log_info(f"[模拟器{self.emulator_id}] 连接状态检查 ({i+1}/10): {current_state}", component="InstagramDMTask")

                            # 检测到"已连接"时返回成功
                            if "已连接" in current_state:
                                log_info(f"[模拟器{self.emulator_id}] V2Ray节点连接成功", component="InstagramDMTask")
                                return True

                    # ================================================================
                    # 🎯 第6步：如果10秒内未连接成功，返回超时失败
                    # ================================================================
                    log_error(f"[模拟器{self.emulator_id}] V2Ray节点连接超时", component="InstagramDMTask")
                    return False
                else:
                    log_error(f"[模拟器{self.emulator_id}] 连接按钮点击失败", component="InstagramDMTask")
                    return False
            else:
                log_error(f"[模拟器{self.emulator_id}] 未知的连接状态: {current_state}", component="InstagramDMTask")
                return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] V2Ray节点连接异常: {str(e)}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 4.2.4 V2Ray节点延迟测试步骤
    # ------------------------------------------------------------------------
    # 功能描述: 测试V2Ray节点延迟并处理测试结果
    # 调用关系: 被阶段二V2Ray连接流程调用，验证节点可用性
    # 注意事项: 包含重试机制，最多5次ping测试，失败则需要更换节点
    # 技术实现: 监控tv_test_state元素的文本变化，处理测试中/成功/失败状态
    #
    # 执行步骤:
    # 第1步: 简化的ping测试逻辑 (移除重复重试机制)
    # 第2步: 查找测试状态元素 (resource-id: com.v2ray.ang:id/tv_test_state)
    # 第3步: 读取当前测试状态文本
    # 第4步: 如果状态为"已连接，点击测试连接"，点击开始测试
    #        - 调用ld.click_node()点击测试按钮
    #        - 如果点击失败，返回失败
    # 第5步: 监控测试结果 (最多等待30秒，最多3次失败后切换节点)
    #        - 每100毫秒检查一次tv_test_state的文本变化
    #        - "连接成功" → 测试成功，等待1秒后返回
    #        - "失败" → 失败计数+1，未达到最大次数时点击重置UI继续
    #        - "测试中" → 继续等待测试完成
    # 第6步: 测试失败，触发节点切换逻辑
    #        - 检查是否已达到最大节点切换次数(8次)
    #        - 调用_switch_to_new_node()切换节点
    #        - 递归调用自身重新测试新节点
    # ------------------------------------------------------------------------

    async def _step_test_node_latency(self) -> bool:
        """
        4.2.4 测试V2Ray节点延迟
        点击测试按钮并监控测试结果，包含重试机制

        Returns:
            bool: 测试是否成功
        """
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始测试V2Ray节点延迟", component="InstagramDMTask")

            # ========================================================================
            # 🎯 第1步：简化的ping测试逻辑 (移除重复重试机制)
            # ========================================================================
            log_info(f"[模拟器{self.emulator_id}] 开始V2Ray节点延迟测试", component="InstagramDMTask")

            # --------------------------------------------------------------------
            # 🎯 第2步：查找测试状态元素
            # --------------------------------------------------------------------
            state_node = self.ld.find_node(self.emulator_id, resource_id="com.v2ray.ang:id/tv_test_state")

            if not state_node:
                log_error(f"[模拟器{self.emulator_id}] 未找到测试状态元素", component="InstagramDMTask")
                return False

            # --------------------------------------------------------------------
            # 🎯 第3步：读取当前测试状态文本
            # --------------------------------------------------------------------
            current_state = state_node.get('text', '')
            log_info(f"[模拟器{self.emulator_id}] 当前测试状态: {current_state}", component="InstagramDMTask")

            # --------------------------------------------------------------------
            # 🎯 第4步：如果状态为"已连接，点击测试连接"，点击开始测试
            # --------------------------------------------------------------------
            if "已连接" in current_state and "点击测试" in current_state:
                log_info(f"[模拟器{self.emulator_id}] 点击开始延迟测试", component="InstagramDMTask")

                if self.ld.click_node(state_node, self.emulator_id):
                    log_info(f"[模拟器{self.emulator_id}] 已点击测试按钮，等待测试结果", component="InstagramDMTask")
                else:
                    log_error(f"[模拟器{self.emulator_id}] 测试按钮点击失败", component="InstagramDMTask")
                    return False

                # ================================================================
                # 🎯 第5步：监控测试结果 (最多3次失败后切换节点)
                # ================================================================
                test_timeout = 30
                failure_count = 0
                max_failures = 3

                for i in range(test_timeout * 10):  # 乘以10因为现在是100毫秒间隔
                    await asyncio.sleep(0.1)  # 100毫秒间隔

                    # 重新获取状态
                    state_node = self.ld.find_node(self.emulator_id, resource_id="com.v2ray.ang:id/tv_test_state")
                    if not state_node:
                        continue

                    current_state = state_node.get('text', '')
                    # 每秒显示一次日志（每10次循环显示一次）
                    if i % 10 == 0:
                        log_info(f"[模拟器{self.emulator_id}] 测试状态监控 ({i//10+1}/{test_timeout}): {current_state}", component="InstagramDMTask")

                    # --------------------------------------------------------
                    # 🎯 第5.1步：检查测试结果 - 成功情况
                    # --------------------------------------------------------
                    if "连接成功" in current_state:
                        log_info(f"[模拟器{self.emulator_id}] ✅ V2Ray节点延迟测试成功: {current_state}", component="InstagramDMTask")
                        log_info(f"[模拟器{self.emulator_id}] 等待5秒后进入下一阶段", component="InstagramDMTask")
                        await asyncio.sleep(5)
                        return True

                    # --------------------------------------------------------
                    # 🎯 第5.2步：检查测试结果 - 失败情况
                    # --------------------------------------------------------
                    elif "失败" in current_state:
                        failure_count += 1
                        log_error(f"[模拟器{self.emulator_id}] ❌ V2Ray节点延迟测试失败 ({failure_count}/{max_failures}): {current_state}", component="InstagramDMTask")

                        # 先检查是否达到最大失败次数
                        if failure_count >= max_failures:
                            log_error(f"[模拟器{self.emulator_id}] 达到最大失败次数 ({max_failures})，触发节点切换", component="InstagramDMTask")
                            break  # 直接退出，不再点击重置
                        else:
                            # 只有未达到最大次数时才点击重置UI
                            log_info(f"[模拟器{self.emulator_id}] 点击失败状态重置UI", component="InstagramDMTask")
                            if self.ld.click_node(state_node, self.emulator_id):
                                log_info(f"[模拟器{self.emulator_id}] 已点击失败状态，等待UI重置", component="InstagramDMTask")
                                await asyncio.sleep(0.5)  # 等待UI重置，减少到500毫秒
                            else:
                                log_error(f"[模拟器{self.emulator_id}] 点击失败状态元素失败", component="InstagramDMTask")

                            log_info(f"[模拟器{self.emulator_id}] 继续等待测试结果 ({failure_count}/{max_failures})", component="InstagramDMTask")
                            continue

                    # --------------------------------------------------------
                    # 🎯 第5.3步：检查测试结果 - 测试中情况
                    # --------------------------------------------------------
                    elif "测试中" in current_state:
                        log_info(f"[模拟器{self.emulator_id}] 🔄 延迟测试进行中，继续等待...", component="InstagramDMTask")
                        continue  # 继续等待测试完成

                # 如果到这里说明测试失败或超时
                log_error(f"[模拟器{self.emulator_id}] V2Ray节点延迟测试失败或超时", component="InstagramDMTask")

            # ====================================================================
            # 🎯 第6步：测试失败，触发节点切换逻辑
            # ====================================================================
            # 检查是否已达到最大节点切换次数
            if self.node_switch_count >= 8:
                log_error(f"[模拟器{self.emulator_id}] 已达到最大节点切换次数(8次)，任务终止", component="InstagramDMTask")
                return False

            log_error(f"[模拟器{self.emulator_id}] V2Ray节点延迟测试失败，开始节点切换 (第{self.node_switch_count + 1}/8次)", component="InstagramDMTask")

            # 调用节点切换逻辑
            if await self._switch_to_new_node():
                self.node_switch_count += 1  # 节点切换成功，计数器+1
                log_info(f"[模拟器{self.emulator_id}] 节点切换成功，重新测试延迟", component="InstagramDMTask")
                # 递归调用自身重新测试新节点
                return await self._step_test_node_latency()
            else:
                log_error(f"[模拟器{self.emulator_id}] 节点切换失败，任务终止", component="InstagramDMTask")
                return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] V2Ray节点延迟测试异常: {str(e)}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 4.2.5 V2Ray节点切换步骤
    # ------------------------------------------------------------------------
    # 功能描述: 当前节点ping测试失败后，切换到新的可用节点
    # 调用关系: 被_step_test_node_latency调用，实现节点自动切换
    # 注意事项: 包含智能滑动、随机选择、连接验证的完整流程
    # 技术实现: 基于参考代码的节点切换算法，支持超时控制
    #
    # 执行步骤:
    # 第1步: 执行智能滑动浏览更多节点
    #        - 调用_perform_intelligent_swipe()方法
    #        - 随机滑动1-5次，增加节点选择的随机性
    # 第2步: 执行随机节点选择
    #        - 调用_select_random_node()方法
    #        - 获取所有可见节点列表 (resource-id: com.v2ray.ang:id/tv_name)
    #        - 随机选择一个节点并点击
    # 第3步: 重新连接V2Ray节点
    #        - 调用_step_connect_v2ray_node()方法
    #        - 验证新节点的连接状态
    #        - 如果连接失败，记录错误并返回失败
    # ------------------------------------------------------------------------

    async def _switch_to_new_node(self) -> bool:
        """
        4.2.5 切换到新的V2Ray节点
        智能滑动并随机选择新节点，包含连接验证

        Returns:
            bool: 节点切换是否成功
        """
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始V2Ray节点切换流程", component="InstagramDMTask")

            # ========================================================================
            # 🎯 第1步：执行智能滑动浏览更多节点
            # ========================================================================
            if not await self._perform_intelligent_swipe():
                log_error(f"[模拟器{self.emulator_id}] 智能滑动失败", component="InstagramDMTask")
                return False

            # ========================================================================
            # 🎯 第2步：随机选择新节点
            # ========================================================================
            if not await self._select_random_node():
                log_error(f"[模拟器{self.emulator_id}] 随机节点选择失败", component="InstagramDMTask")
                return False

            # ========================================================================
            # 🎯 第3步：重新连接V2Ray节点
            # ========================================================================
            if not await self._step_connect_v2ray_node():
                log_error(f"[模拟器{self.emulator_id}] 新节点连接失败", component="InstagramDMTask")
                return False

            log_info(f"[模拟器{self.emulator_id}] ✅ V2Ray节点切换成功", component="InstagramDMTask")
            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] V2Ray节点切换异常: {str(e)}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 4.2.6 智能滑动浏览节点步骤
    # ------------------------------------------------------------------------
    # 功能描述: 智能滑动浏览V2Ray节点列表，增加节点选择的随机性
    # 调用关系: 被_switch_to_new_node调用，实现节点列表的智能浏览
    # 注意事项: 基于参考代码的滑动算法，支持随机方向和距离
    # 技术实现: 使用scroll_list_enhanced方法进行滑动操作
    #
    # 执行步骤:
    # 第1步: 查找节点列表容器 (resource-id: com.v2ray.ang:id/recycler_view)
    #        - 如果未找到容器，记录信息并返回成功 (不是致命错误)
    # 第2步: 执行随机滑动 (1-5次)
    #        - 生成随机滑动次数 (1-5次)
    #        - 每次滑动使用随机方向 (up/down) 和距离比例 (0.3-0.7)
    #        - 计算滑动坐标并调用ld.swipe()方法执行滑动
    #        - 滑动持续时间300毫秒
    # 第3步: 滑动后固定等待
    #        - 每次滑动后等待0.2秒
    #        - 确保滑动操作完全生效
    # ------------------------------------------------------------------------

    async def _perform_intelligent_swipe(self) -> bool:
        """
        4.2.6 执行智能滑动浏览节点列表
        随机滑动1-5次，增加节点选择的随机性

        Returns:
            bool: 滑动是否成功
        """
        try:
            import random
            log_info(f"[模拟器{self.emulator_id}] 开始智能滑动浏览节点列表", component="InstagramDMTask")

            # ====================================================================
            # 🎯 第1步：查找节点列表容器
            # ====================================================================
            list_node = self.ld.find_node(self.emulator_id, resource_id="com.v2ray.ang:id/recycler_view")

            if not list_node:
                log_info(f"[模拟器{self.emulator_id}] 未找到节点列表容器，跳过滑动", component="InstagramDMTask")
                return True  # 不是致命错误，继续执行

            # ====================================================================
            # 🎯 第2步：执行随机滑动 (1-5次)
            # ====================================================================
            swipe_count = random.randint(1, 5)
            log_info(f"[模拟器{self.emulator_id}] 计划执行 {swipe_count} 次随机滑动", component="InstagramDMTask")

            for i in range(swipe_count):
                # 生成随机滑动参数
                direction = random.choice(['up', 'down'])
                distance_ratio = random.uniform(0.3, 0.7)  # 滑动距离比例

                log_info(f"[模拟器{self.emulator_id}] 第 {i+1}/{swipe_count} 次滑动: {direction}, 距离比例: {distance_ratio:.2f}", component="InstagramDMTask")

                # ================================================================
                # 🎯 执行滑动操作 - 直接使用swipe方法
                # ================================================================
                # 获取屏幕尺寸
                width, height = self.ld.get_screen_size(self.emulator_id)
                center_x = width // 2

                # 计算滑动坐标
                if direction == 'down':
                    start_y = int(height * 0.7)
                    end_y = int(height * (0.7 - distance_ratio))
                elif direction == 'up':
                    start_y = int(height * 0.3)
                    end_y = int(height * (0.3 + distance_ratio))
                else:
                    start_y = height // 2
                    end_y = height // 2

                # 执行滑动
                swipe_result = self.ld.swipe(
                    index=self.emulator_id,
                    start_x=center_x,
                    start_y=start_y,
                    end_x=center_x,
                    end_y=end_y,
                    duration=300  # 优化：从1000毫秒减少到300毫秒，提高滑动速度
                )

                if swipe_result:
                    log_info(f"[模拟器{self.emulator_id}] 第 {i+1} 次滑动成功", component="InstagramDMTask")
                else:
                    log_info(f"[模拟器{self.emulator_id}] 第 {i+1} 次滑动失败", component="InstagramDMTask")

                # ================================================================
                # 🎯 第3步：滑动后固定等待
                # ================================================================
                wait_time = 0.2  # 优化：从500毫秒减少到200毫秒，提高滑动速度
                log_info(f"[模拟器{self.emulator_id}] 滑动后等待 {wait_time:.1f} 秒", component="InstagramDMTask")
                await asyncio.sleep(wait_time)

            log_info(f"[模拟器{self.emulator_id}] ✅ 智能滑动完成，共执行 {swipe_count} 次", component="InstagramDMTask")
            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 智能滑动异常: {str(e)}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 4.2.7 随机节点选择步骤
    # ------------------------------------------------------------------------
    # 功能描述: 从当前可见的节点列表中随机选择一个节点并点击
    # 调用关系: 被_switch_to_new_node调用，实现节点的随机选择
    # 注意事项: 基于参考代码的节点选择算法，支持节点名称识别
    # 技术实现: 查找tv_name元素列表，随机选择并点击
    #
    # 执行步骤:
    # 第1步: 获取所有可见节点列表 (resource-id: com.v2ray.ang:id/tv_name)
    # 第2步: 检查节点列表是否为空
    #        - 如果为空，记录错误并返回失败
    #        - 如果不为空，记录找到的节点数量
    # 第3步: 随机选择一个节点
    #        - 使用random.choice()从节点列表中随机选择
    #        - 记录选中节点的文本内容和位置信息
    # 第4步: 点击选中的节点
    #        - 调用ld.click_node()方法点击节点
    #        - 如果点击失败，记录错误并返回失败
    # 第5步: 等待节点切换完成 (等待1秒)
    #        - 使用asyncio.sleep(1)等待节点切换生效
    # ------------------------------------------------------------------------

    async def _select_random_node(self) -> bool:
        """
        4.2.7 随机选择V2Ray节点
        从可见节点列表中随机选择一个节点并点击

        Returns:
            bool: 节点选择是否成功
        """
        try:
            import random
            log_info(f"[模拟器{self.emulator_id}] 开始随机选择V2Ray节点", component="InstagramDMTask")

            # ====================================================================
            # 🎯 第1步：获取所有可见节点列表
            # ====================================================================
            nodes = self.ld.find_nodes(resource_id="com.v2ray.ang:id/tv_name")

            # ====================================================================
            # 🎯 第2步：检查节点列表是否为空
            # ====================================================================
            if not nodes:
                log_error(f"[模拟器{self.emulator_id}] 未找到任何可用节点", component="InstagramDMTask")
                return False

            log_info(f"[模拟器{self.emulator_id}] 找到 {len(nodes)} 个可用节点", component="InstagramDMTask")

            # ====================================================================
            # 🎯 第3步：随机选择一个节点
            # ====================================================================
            selected_node = random.choice(nodes)
            node_text = selected_node.get('text', '未知节点')
            node_bounds = selected_node.get('bounds', '未知位置')

            log_info(f"[模拟器{self.emulator_id}] 随机选择节点: {node_text}", component="InstagramDMTask")
            log_info(f"[模拟器{self.emulator_id}] 节点位置: {node_bounds}", component="InstagramDMTask")

            # ====================================================================
            # 🎯 第4步：点击选中的节点
            # ====================================================================
            click_success = self.ld.click_node(selected_node, self.emulator_id)

            if not click_success:
                log_error(f"[模拟器{self.emulator_id}] 节点点击失败: {node_text}", component="InstagramDMTask")
                return False

            log_info(f"[模拟器{self.emulator_id}] ✅ 成功点击节点: {node_text}", component="InstagramDMTask")

            # ====================================================================
            # 🎯 第5步：等待节点切换完成
            # ====================================================================
            log_info(f"[模拟器{self.emulator_id}] 等待1秒让节点切换完成", component="InstagramDMTask")
            await asyncio.sleep(1)

            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 随机节点选择异常: {str(e)}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 4.3.1 Instagram应用启动步骤
    # ------------------------------------------------------------------------
    # 功能描述: 启动Instagram应用并等待加载完成
    # 调用关系: 被阶段三Instagram启动流程调用，作为私信发送的前置步骤
    # 注意事项: 只负责启动应用，不进行状态检测和验证
    # 技术实现: 使用LeiDian_Reorganized.py的runApp方法
    #
    # 执行步骤:
    # 第1步: 启动Instagram应用 (包名: com.instagram.android)
    # 第2步: 等待应用启动完成 (3秒等待时间)
    #
    # 注意: 具体的状态检测和验证由4.3.2步骤负责
    # ------------------------------------------------------------------------

    async def _step_app_launch_instagram(self) -> bool:
        """4.3.1 启动Instagram应用"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始启动Instagram应用", component="InstagramDMTask")

            ##########  第1步：启动Instagram应用  ##########
            # 移除Instagram启动命令的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 执行Instagram启动命令: {self.instagram_package}", component="InstagramDMTask")
            start_result = self.ld.runApp(self.emulator_id, self.instagram_package)

            # runApp现在返回字符串，空字符串表示失败
            if not start_result.strip():
                log_error(f"[模拟器{self.emulator_id}] Instagram应用启动命令执行失败", component="InstagramDMTask")
                return False

            log_info(f"[模拟器{self.emulator_id}] Instagram启动命令执行成功，等待应用加载", component="InstagramDMTask")

            ##########  第2步：等待应用启动完成  ##########
            await asyncio.sleep(3)

            # 启动命令执行成功，返回True
            # 具体的状态检测和验证由4.3.2步骤负责
            log_info(f"[模拟器{self.emulator_id}] ✅ Instagram应用启动命令执行完成", component="InstagramDMTask")
            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] Instagram应用启动异常: {str(e)}", component="InstagramDMTask")
            return False



    # ------------------------------------------------------------------------
    # 🎯 4.3.2 Instagram页面状态检测步骤（五次重启机制）
    # ------------------------------------------------------------------------
    # 功能描述: Instagram页面状态检测，实现五次重启机制和异常状态直接关闭
    # 调用关系: 被阶段三Instagram启动流程调用，确保应用可用状态
    # 注意事项: 启动超时最多重试5次，异常状态直接关闭模拟器不重试
    # 技术实现: 基于TEXT文本检测，支持中英文双语识别
    #
    # 执行步骤:
    # 第1步: 循环检测Instagram启动状态 (最多5次尝试)
    #        - 每次尝试40秒超时检测
    #        - 调用_detect_with_timeout()进行单次检测
    # 第2步: 判断检测结果并分类处理
    #        - "正常-在主页面" → 启动成功，返回继续任务
    #        - "正常-启动加载中"/"异常-Instagram启动超时" → 重启Instagram重试
    #        - 其他异常状态 → 直接关闭模拟器，任务终止
    # 第3步: 重启Instagram (仅限启动超时情况)
    #        - 调用_restart_instagram()方法
    #        - 继续下一次尝试
    # 第4步: 处理最终失败情况
    #        - 5次尝试都失败 → 关闭模拟器
    #        - 返回"任务终止-Instagram启动失败"
    # ------------------------------------------------------------------------

    async def _step_detect_instagram_page_status(self) -> str:
        """4.3.2 Instagram页面状态检测（五次重启机制）"""

        MAX_STARTUP_ATTEMPTS = 5  # 最多5次启动尝试
        STARTUP_TIMEOUT = 40      # 每次40秒超时

        ##########  第1步：循环检测Instagram启动状态 (最多5次尝试)  ##########
        for attempt in range(1, MAX_STARTUP_ATTEMPTS + 1):
            log_info(f"[模拟器{self.emulator_id}] Instagram启动检测 第{attempt}/{MAX_STARTUP_ATTEMPTS}次", component="InstagramDMTask")

            status = await self._detect_with_timeout(STARTUP_TIMEOUT)

            ##########  第2步：判断检测结果并分类处理  ##########
            if status == "正常-在主页面":
                return status
            elif status == "异常-应用崩溃":
                ##########  第3步：处理应用崩溃 - 点击应用按钮继续  ##########
                log_info(f"[模拟器{self.emulator_id}] 检测到Instagram应用崩溃，尝试点击应用按钮继续", component="InstagramDMTask")

                # 查找并点击"关闭应用"按钮
                close_button = self.ld.find_node(text="关闭应用")
                if close_button:
                    success = self.ld.click_node(close_button)
                    if success:
                        log_info(f"[模拟器{self.emulator_id}] 成功点击关闭应用按钮，准备重启Instagram", component="InstagramDMTask")
                        await asyncio.sleep(3)
                        # 主动重启Instagram而不是期望自动恢复
                        await self._restart_instagram()
                        continue
                    else:
                        log_error(f"[模拟器{self.emulator_id}] 点击关闭应用按钮失败", component="InstagramDMTask")
                else:
                    log_error(f"[模拟器{self.emulator_id}] 未找到关闭应用按钮", component="InstagramDMTask")

                # 如果点击失败，重启Instagram
                if attempt < MAX_STARTUP_ATTEMPTS:
                    log_info(f"[模拟器{self.emulator_id}] 应用崩溃处理失败，重启Instagram...", component="InstagramDMTask")
                    await self._restart_instagram()
                    continue
                else:
                    log_error(f"[模拟器{self.emulator_id}] 应用崩溃处理失败且达到最大重试次数", component="InstagramDMTask")
                    await self._capture_exception_screenshot("应用崩溃处理失败")
                    await self._close_emulator("应用崩溃处理失败")
                    return "任务终止-应用崩溃处理失败"
            elif status == "正常-启动加载中" or status == "异常-Instagram启动超时" or status == "需要重启-Instagram闪退":
                ##########  第4步：重启Instagram (仅限启动超时情况)  ##########
                if attempt < MAX_STARTUP_ATTEMPTS:
                    log_info(f"[模拟器{self.emulator_id}] 第{attempt}次启动超时，重启Instagram...", component="InstagramDMTask")
                    await self._restart_instagram()
                    continue
                else:
                    ##########  第5步：处理最终失败情况  ##########
                    # 5.1 记录失败日志
                    log_error(f"[模拟器{self.emulator_id}] Instagram五次启动均失败", component="InstagramDMTask")
                    # 5.2 截图保存现场
                    await self._capture_exception_screenshot("Instagram启动失败")
                    # 5.3 关闭模拟器
                    await self._close_emulator("Instagram启动失败")
                    return "任务终止-Instagram启动失败"
            else:
                # 检测到异常状态处理流程
                # 1. 记录异常日志
                log_error(f"[模拟器{self.emulator_id}] 检测到异常状态: {status}", component="InstagramDMTask")
                # 2. 截图保存现场
                await self._capture_exception_screenshot(status)
                # 3. 关闭模拟器
                await self._close_emulator(status)
                return f"任务终止-{status}"

        # 兜底异常处理（理论上不会到这里）
        # 1. 截图保存现场
        await self._capture_exception_screenshot("Instagram启动失败")
        # 2. 关闭模拟器
        await self._close_emulator("Instagram启动失败")
        return "任务终止-Instagram启动失败"

    # ========================================================================
    # 🎯 第1步相关方法：循环检测Instagram启动状态
    # ========================================================================

    # ------------------------------------------------------------------------
    # 1.1 单次检测（带超时控制）
    # ------------------------------------------------------------------------
    async def _detect_with_timeout(self, timeout_seconds: int) -> str:
        """1.1 单次检测（带超时控制）"""

        CHECK_INTERVAL = 2  # 每2秒检测一次
        start_time = time.time()

        # 循环检测页面状态（每2秒一次，直到超时或检测到结果）
        while time.time() - start_time < timeout_seconds:
            # 🎯 检查总任务超时
            if hasattr(self, 'task_start_time') and hasattr(self, 'task_timeout'):
                if time.time() - self.task_start_time >= self.task_timeout:
                    elapsed = time.time() - self.task_start_time
                    log_info(f"[模拟器{self.emulator_id}] 总任务超时({self.task_timeout}秒)，Instagram启动检测中断，耗时: {elapsed:.2f}秒", component="InstagramDMTask")
                    return "异常-总任务超时"

            status = await self._detect_page_by_text_final()

            if status != "正常-启动加载中":
                return status

            await asyncio.sleep(CHECK_INTERVAL)
            elapsed = time.time() - start_time
            log_info(f"[模拟器{self.emulator_id}] Instagram启动检测中... 已等待{elapsed:.1f}秒", component="InstagramDMTask")

        # 检测超时处理
        return "异常-Instagram启动超时"

    # ------------------------------------------------------------------------
    # 1.2 核心页面状态检测
    # ------------------------------------------------------------------------
    async def _detect_page_by_text_final(self) -> str:
        """1.2 核心页面状态检测"""

        try:
            # 获取UI层次结构XML数据
            success, _ = self.ld.execute_ld(self.emulator_id, "uiautomator dump /sdcard/ui.xml")
            if not success:
                return "正常-启动加载中"

            success, xml_content = self.ld.execute_ld(self.emulator_id, "cat /sdcard/ui.xml")
            if not success or not xml_content:
                return "正常-启动加载中"

            # 解析XML并提取所有文本节点
            import xml.etree.ElementTree as ET
            try:
                root = ET.fromstring(xml_content)
                all_texts = []

                for elem in root.iter():
                    text = elem.get('text', '').strip()
                    if text:
                        all_texts.append(text)

                if not all_texts:
                    return "正常-启动加载中"

                page_text = " ".join(all_texts)

            except Exception as e:
                return "正常-启动加载中"

            # 优先检测主页状态（正常状态）- 直接使用主页元素验证
            # 移除主页状态检测的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 开始主页状态检测", component="InstagramDMTask")
            if self._verify_homepage_elements():
                log_info(f"[模拟器{self.emulator_id}] ✅ 验证成功", component="InstagramDMTask")
                return "正常-在主页面"
            else:
                log_info(f"[模拟器{self.emulator_id}] ❌ 验证失败", component="InstagramDMTask")

            # 检测各种异常状态（一旦检测到立即返回）
            # 检测应用崩溃异常，，因为"异常-应用崩溃"有专门的elif分支处理，所以不会进入下面的else分支：
            crash_texts = ["反复停止运行", "应用信息", "关闭应用", "强制停止", "Instagram反复停止运行"]
            found_crash_texts = [text for text in crash_texts if text in page_text]
            if len(found_crash_texts) >= 1:
                return "异常-应用崩溃"

            # 检测登录异常
            login_texts = ["账号、邮箱或手机号", "密码", "登录", "忘记密码", "创建新账户"]
            found_login_texts = [text for text in login_texts if text in page_text]
            if len(found_login_texts) >= 2:
                return "异常-需要登录"

            # 检测注册引导异常
            signup_texts = ["加入Instagram", "立即开始", "我已有账户", "和志趣相投的人分享"]
            found_signup_texts = [text for text in signup_texts if text in page_text]
            if len(found_signup_texts) >= 2:
                return "异常-需要注册"

            # 检测账号申诉异常
            challenge_texts = ["我们已暂时停用", "申诉", "为什么会发生这种情况", "身份验证", "继续"]
            found_challenge_texts = [text for text in challenge_texts if text in page_text]
            if len(found_challenge_texts) >= 2:
                return "异常-账号申诉"

            # 检测网络异常
            network_texts = ["网络连接", "无法连接", "重试", "网络错误"]
            found_network_texts = [text for text in network_texts if text in page_text]
            if len(found_network_texts) >= 2:
                return "异常-网络异常"

            # 检测更新异常
            update_texts = ["更新Instagram", "新版本", "立即更新"]
            found_update_texts = [text for text in update_texts if text in page_text]
            if len(found_update_texts) >= 2:
                return "异常-需要更新"

            # 检查Activity状态（判断是否在Instagram应用内）
            current_activity = self.ld.get_activity_name()

            if current_activity and "instagram" in current_activity.lower():
                return "正常-启动加载中"
            else:
                return "需要重启-Instagram闪退"

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 页面检测异常: {e}", component="InstagramDMTask")
            return "异常-检测失败"

    # ------------------------------------------------------------------------
    # 1.3 主页元素验证
    # ------------------------------------------------------------------------
    def _verify_homepage_elements(self) -> bool:
        """1.3 主页元素验证 - 使用高性能批量API"""
        try:
            # 移除批量验证的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 开始批量验证Instagram主页元素", component="InstagramDMTask")

            # 准备批量搜索条件
            search_criteria = [
                {"resource_id": "com.instagram.android:id/feed_tab"},      # 首页
                {"resource_id": "com.instagram.android:id/search_tab"},    # 搜索
                {"resource_id": "com.instagram.android:id/creation_tab"},  # 相机
                {"resource_id": "com.instagram.android:id/clips_tab"},     # Reels标签
                {"resource_id": "com.instagram.android:id/profile_tab"},   # 主页
                {"content_desc": "首页"},
                {"content_desc": "搜索"},
                {"content_desc": "相机"},
                {"content_desc": "主页"}
            ]

            # 使用批量API一次性查找所有元素
            results = self.ld.find_nodes_batch(search_criteria, early_exit=True, min_matches=2)

            # 统计找到的元素
            found_elements = []

            for i, result in enumerate(results):
                if result:
                    if i < 5:  # resource_id元素
                        element_name = search_criteria[i]["resource_id"].split(':id/')[-1]
                        found_elements.append(f"resource_id:{element_name}")
                    else:  # content_desc元素
                        element_name = search_criteria[i]["content_desc"]
                        found_elements.append(f"content_desc:{element_name}")

            found_count = len(found_elements)
            result = found_count >= 2

            if result:
                # 移除元素详细信息的技术日志
                # log_info(f"[模拟器{self.emulator_id}] ✅ 批量验证成功，找到{found_count}个元素: {found_elements}", component="InstagramDMTask")
                log_info(f"[模拟器{self.emulator_id}] ✅ 批量验证成功", component="InstagramDMTask")
            else:
                # 移除元素详细信息的技术日志
                # log_info(f"[模拟器{self.emulator_id}] ❌ 批量验证失败，仅找到{found_count}个元素: {found_elements}", component="InstagramDMTask")
                log_info(f"[模拟器{self.emulator_id}] ❌ 批量验证失败", component="InstagramDMTask")

            return result

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 批量验证异常: {e}", component="InstagramDMTask")
            return False

    # ========================================================================
    # 🎯 异常截图方法：记录异常状态现场
    # ========================================================================

    async def _capture_exception_screenshot(self, exception_type: str):
        """捕获异常状态截图"""
        try:
            from .screenshot_manager import get_screenshot_manager
            screenshot_manager = get_screenshot_manager()

            # 清理异常类型名称，移除"异常-"前缀，用于文件名
            clean_exception_type = exception_type.replace("异常-", "").replace("需要重启-", "")

            await screenshot_manager.capture_failure_screenshot(
                emulator_id=self.emulator_id,
                error_type=f"Instagram_{clean_exception_type}",
                failure_count=1
            )

            log_info(f"[模拟器{self.emulator_id}] 异常状态截图已保存: {exception_type}", component="InstagramDMTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 捕获异常截图失败: {e}", component="InstagramDMTask")

    # ========================================================================
    # 🎯 第3步相关方法：重启Instagram (仅限启动超时情况)
    # ========================================================================

    # ------------------------------------------------------------------------
    # 3.1 重启Instagram应用
    # ------------------------------------------------------------------------
    async def _restart_instagram(self):
        """3.1 重启Instagram应用"""

        try:
            log_info(f"[模拟器{self.emulator_id}] 重启Instagram...", component="InstagramDMTask")

            # 1. 强制结束进程
            self.ld.killApp(self.emulator_id, self.instagram_package)
            await asyncio.sleep(3)

            # 2. 清理可能的系统弹窗
            await self._clear_system_dialogs()

            # 3. 重新启动
            self.ld.runApp(self.emulator_id, self.instagram_package)
            await asyncio.sleep(5)

            log_info(f"[模拟器{self.emulator_id}] Instagram重启完成", component="InstagramDMTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] Instagram重启失败: {e}", component="InstagramDMTask")

    # ------------------------------------------------------------------------
    # 3.2 清理系统弹窗
    # ------------------------------------------------------------------------
    async def _clear_system_dialogs(self):
        """3.2 清理系统弹窗"""
        try:
            # 清理可能的崩溃弹窗 - 优先处理崩溃相关按钮
            dialog_buttons = ["关闭应用", "强制停止", "确定", "关闭", "OK", "Close"]
            for button_text in dialog_buttons:
                button = self.ld.find_node(self.emulator_id, text=button_text)
                if button:
                    self.ld.click_node(button)
                    await asyncio.sleep(1)
        except:
            pass

    # ========================================================================
    # 🎯 第4步相关方法：处理最终失败情况
    # ========================================================================

    # ------------------------------------------------------------------------
    # 4.1 关闭模拟器并记录原因
    # ------------------------------------------------------------------------
    async def _close_emulator(self, reason: str):
        """4.1 关闭模拟器并记录原因"""

        try:
            log_info(f"[模拟器{self.emulator_id}] 任务终止原因: {reason}", component="InstagramDMTask")
            log_info(f"[模拟器{self.emulator_id}] 开始关闭模拟器...", component="InstagramDMTask")

            # 直接关闭模拟器
            self.ld.quit(self.emulator_id)
            log_info(f"[模拟器{self.emulator_id}] 模拟器已关闭", component="InstagramDMTask")

            # 更新任务状态
            self._update_task_status("失败", reason)

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 关闭模拟器失败: {e}", component="InstagramDMTask")

    # ------------------------------------------------------------------------
    # 4.2 更新任务状态
    # ------------------------------------------------------------------------
    def _update_task_status(self, status: str, reason: str):
        """4.2 更新任务状态"""
        # 这里可以更新数据库或发送状态通知
        log_info(f"[模拟器{self.emulator_id}] 任务状态更新: {status} - {reason}", component="InstagramDMTask")

    # ------------------------------------------------------------------------
    # 🎯 4.3 业务逻辑步骤
    # ------------------------------------------------------------------------
    # 功能描述: 具体业务逻辑实现的相关步骤方法
    # 调用关系: 被阶段四执行流程调用，实现核心的业务自动化逻辑
    # 注意事项: 业务逻辑应支持批量处理和进度反馈
    # 方法格式: async def _step_business_xxx(self) -> bool:
    # ------------------------------------------------------------------------

    # ------------------------------------------------------------------------
    # 🎯 4.3.1 导航到个人主页步骤
    # ------------------------------------------------------------------------
    # 功能描述: 从Instagram主页导航到个人资料页面
    # 调用关系: 被阶段四批量私信流程调用，作为私信发送的前置步骤
    # 注意事项: 需要处理页面加载、元素定位、导航失败等情况
    # 技术实现: 基于UI元素点击和页面状态检测
    # ------------------------------------------------------------------------

    async def _step_navigate_to_profile(self) -> bool:
        """步骤11：导航到个人主页"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始导航到个人主页", component="InstagramDMTask")

            if self.ld is None:
                log_error(f"[模拟器{self.emulator_id}] LeiDian API未初始化", component="InstagramDMTask")
                return False

            # 重试机制：最多3次
            for attempt in range(3):
                ##########  第1步：查找个人主页标识  ##########
                profile_tab = self.ld.find_node(resource_id="com.instagram.android:id/profile_tab")
                if not profile_tab:
                    if attempt < 2:
                        await asyncio.sleep(0.5)
                        continue
                    log_error(f"[模拟器{self.emulator_id}] 未找到个人主页标识", component="InstagramDMTask")
                    return False

                ##########  第2步：点击进入个人主页  ##########
                click_result = self.ld.click_node(profile_tab)
                if not click_result:
                    if attempt < 2:
                        await asyncio.sleep(0.5)
                        continue
                    log_error(f"[模拟器{self.emulator_id}] 点击个人主页失败", component="InstagramDMTask")
                    return False

                ##########  第3步：智能等待粉丝数元素出现  ##########
                start_time = time.time()
                while time.time() - start_time < 3.0:  # 最大3秒超时
                    followers_element = self.ld.find_node(resource_id=self.INSTAGRAM_ELEMENTS["followers_count"])
                    if followers_element:
                        log_info(f"[模拟器{self.emulator_id}] ✅ 个人主页加载完成", component="InstagramDMTask")
                        return True
                    await asyncio.sleep(0.2)  # 0.2秒检测间隔

                if attempt < 2:
                    await asyncio.sleep(0.5)
                    continue

            log_error(f"[模拟器{self.emulator_id}] 导航到个人主页失败", component="InstagramDMTask")
            return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 个人主页加载异常: {e}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 4.3.2 获取粉丝数量信息步骤
    # ------------------------------------------------------------------------
    # 功能描述: 获取并解析个人主页的粉丝数量信息
    # 调用关系: 被阶段四批量私信流程调用，验证账号粉丝数量
    # 注意事项: 需要处理K、M等单位转换，验证有效性
    # 技术实现: 基于UI元素文本解析和数量转换
    # ------------------------------------------------------------------------

    async def _step_get_followers_count(self) -> bool:
        """步骤12：获取粉丝数量信息"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始获取粉丝数量信息", component="InstagramDMTask")

            if self.ld is None:
                log_error(f"[模拟器{self.emulator_id}] LeiDian API未初始化", component="InstagramDMTask")
                return False

            ##########  第1步：智能查找粉丝数标识  ##########
            followers_count_node = None
            for attempt in range(3):  # 3次重试
                # 方案1: resource_id查找
                followers_count_node = self.ld.find_node(resource_id=self.INSTAGRAM_ELEMENTS["followers_count"])
                if followers_count_node:
                    break
                # 方案2: text查找
                followers_count_node = self.ld.find_node(text_contains="粉丝")
                if followers_count_node:
                    break
                if attempt < 2:
                    await asyncio.sleep(0.3)  # 0.3秒间隔

            if not followers_count_node:
                log_error(f"[模拟器{self.emulator_id}] 未找到粉丝数标识", component="InstagramDMTask")
                return False

            ##########  第2步：解析数量  ##########
            followers_text = followers_count_node.get('text', '0')
            log_info(f"[模拟器{self.emulator_id}] 原始粉丝数文本: {followers_text}", component="InstagramDMTask")

            # 处理K、M、万、逗号等单位转换
            followers_count = self._parse_count_text(followers_text)

            ##########  第3步：验证有效性  ##########
            if followers_count <= 0:
                log_error(f"[模拟器{self.emulator_id}] 粉丝数量不足: {followers_count} <= 0", component="InstagramDMTask")
                return False

            log_info(f"[模拟器{self.emulator_id}] ✅ 粉丝数量验证通过: {followers_count}", component="InstagramDMTask")
            self.followers_count = followers_count
            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 获取粉丝数量异常: {e}", component="InstagramDMTask")
            return False

    def _parse_count_text(self, text: str) -> int:
        """解析数量文本，处理K、M、万、逗号等单位"""
        try:
            text = text.strip().replace(',', '')  # 支持逗号

            if text.endswith('K') or text.endswith('k'):
                return int(float(text[:-1]) * 1000)
            elif text.endswith('M') or text.endswith('m'):
                return int(float(text[:-1]) * 1000000)
            elif text.endswith('万'):
                return int(float(text[:-1]) * 10000)  # 支持万
            else:
                return int(text)
        except (ValueError, IndexError):
            log_error(f"[模拟器{self.emulator_id}] 解析数量文本失败: {text}", component="InstagramDMTask")
            return 0



    # ------------------------------------------------------------------------
    # 🎯 4.3.3 打开粉丝列表步骤
    # ------------------------------------------------------------------------
    # 功能描述: 点击粉丝数量，打开粉丝列表页面
    # 调用关系: 被阶段四批量私信流程调用，进入粉丝列表界面
    # 注意事项: 需要等待列表加载，验证列表是否有可见粉丝项目
    # 技术实现: 基于UI元素点击和页面状态检测
    # ------------------------------------------------------------------------

    async def _step_open_followers_list(self) -> bool:
        """步骤13：打开粉丝列表"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始打开粉丝列表", component="InstagramDMTask")

            if self.ld is None:
                log_error(f"[模拟器{self.emulator_id}] LeiDian API未初始化", component="InstagramDMTask")
                return False

            # 重试机制：最多3次
            for attempt in range(3):
                ##########  第1步：点击粉丝数  ##########
                followers_count_node = self.ld.find_node(resource_id=self.INSTAGRAM_ELEMENTS["followers_count"])
                if not followers_count_node:
                    if attempt < 2:
                        await asyncio.sleep(0.5)
                        continue
                    log_error(f"[模拟器{self.emulator_id}] 未找到粉丝数节点", component="InstagramDMTask")
                    return False

                click_result = self.ld.click_node(followers_count_node)
                if not click_result:
                    if attempt < 2:
                        await asyncio.sleep(0.5)
                        continue
                    log_error(f"[模拟器{self.emulator_id}] 点击粉丝数失败", component="InstagramDMTask")
                    return False

                ##########  第2步：智能等待列表加载  ##########
                start_time = time.time()
                while time.time() - start_time < 3.0:  # 最大3秒超时
                    # 只检测列表容器
                    list_container = self.ld.find_node(resource_id=self.INSTAGRAM_ELEMENTS["list_view"])
                    if list_container:
                        log_info(f"[模拟器{self.emulator_id}] ✅ 粉丝列表加载完成", component="InstagramDMTask")
                        return True
                    await asyncio.sleep(0.2)  # 0.2秒检测间隔

                if attempt < 2:
                    await asyncio.sleep(0.5)
                    continue

            log_error(f"[模拟器{self.emulator_id}] 打开粉丝列表失败", component="InstagramDMTask")
            return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 打开粉丝列表异常: {e}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 4.3.4 初始化私信任务步骤
    # ------------------------------------------------------------------------
    # 功能描述: 初始化私信任务的相关参数和去重记录
    # 调用关系: 被阶段四批量私信流程调用，准备私信发送环境
    # 注意事项: 需要加载去重记录文件，初始化计数器和任务参数
    # 技术实现: 基于文件读取和配置管理
    # ------------------------------------------------------------------------

    async def _step_initialize_dm_task(self) -> bool:
        """步骤14：初始化私信任务"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始初始化私信任务", component="InstagramDMTask")

            ##########  第1步：异步加载去重记录  ##########
            record_file_path = self.config_manager.get("instagram_dm.record_file_path", "sent_users.txt")
            self.record_file_path = record_file_path

            # 异步读取文件
            import os
            import aiofiles
            sent_users = set()
            if os.path.exists(record_file_path):
                async with aiofiles.open(record_file_path, 'r', encoding='utf-8') as f:
                    async for line in f:
                        username = line.strip()
                        if username:
                            sent_users.add(username)
            self.sent_users = sent_users

            log_info(f"[模拟器{self.emulator_id}] 已加载 {len(self.sent_users)} 条去重记录", component="InstagramDMTask")

            ##########  第2步：初始化计数器  ##########
            self.sent_count = 0

            ##########  第3步：设置任务参数  ##########
            log_info(f"[模拟器{self.emulator_id}] 任务目标数量: {self.message_count}", component="InstagramDMTask")
            log_info(f"[模拟器{self.emulator_id}] 延迟范围: {self.delay_min}-{self.delay_max}ms", component="InstagramDMTask")

            log_info(f"[模拟器{self.emulator_id}] ✅ 私信任务初始化完成", component="InstagramDMTask")

            # 🎯 发送初始进度更新到UI
            self._emit_progress_update()
            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 初始化私信任务异常: {e}", component="InstagramDMTask")
            return False

    def _load_sent_users(self) -> set:
        """加载已发送用户记录"""
        try:
            import os
            if not os.path.exists(self.record_file_path):
                # 如果文件不存在，创建空文件
                with open(self.record_file_path, 'w', encoding='utf-8') as f:
                    f.write("")
                log_info(f"[模拟器{self.emulator_id}] 创建新的记录文件: {self.record_file_path}", component="InstagramDMTask")
                return set()

            with open(self.record_file_path, 'r', encoding='utf-8') as f:
                sent_users = set(line.strip() for line in f if line.strip())

            return sent_users

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 加载去重记录失败: {e}", component="InstagramDMTask")
            return set()

    # ------------------------------------------------------------------------
    # 🎯 步骤15：批量私信发送循环
    # ------------------------------------------------------------------------
    # 功能描述: Instagram粉丝批量私信发送的核心循环逻辑，支持去重、延迟、重试机制
    # 调用关系: 被execute()主流程调用，作为阶段四的核心业务逻辑
    # 注意事项: 包含完整的错误处理、用户去重、滚动加载、底部检测机制
    # 技术实现: 基于雷电模拟器原生API，支持多种发送模式和智能重试
    #
    # 执行步骤:
    # 第1步: 初始化循环参数 (重试计数器、最大重试次数)
    # 第2步: 主循环开始 (while sent_count < message_count)
    # 第3步: 获取当前屏幕可见粉丝 (调用_get_mine_followers)
    # 第4步: 遍历每个粉丝进行私信发送
    #        - 提取粉丝用户名和位置信息
    #        - 检查去重机制 (避免重复发送)
    #        - 调用_send_message_to_follower发送私信
    #        - 保存已发送用户记录
    #        - 执行用户间延迟控制
    # 第5步: 检查是否需要滚动加载更多
    #        - 点击"查看更多"按钮
    #        - 检查是否到达列表底部
    #        - 执行滚动操作加载更多粉丝
    # ------------------------------------------------------------------------
    async def _step_business_send_mass_dm(self) -> bool:
        """步骤15：批量私信发送循环"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始批量私信发送循环", component="InstagramDMTask")

            if self.ld is None:
                log_error(f"[模拟器{self.emulator_id}] LeiDian API未初始化", component="InstagramDMTask")
                return False

            ##########  第1步：初始化循环参数  ##########
            log_info(f"[模拟器{self.emulator_id}] 初始化循环参数", component="InstagramDMTask")

            retry_count = 0
            max_retries = 3

            ##########  第2步：主循环开始  ##########
            log_info(f"[模拟器{self.emulator_id}] 开始主循环，目标数量: {self.message_count}", component="InstagramDMTask")

            while self.sent_count < self.message_count:
                # 🎯 步骤1：检查总任务超时（参考代码实现）
                if time.time() - self.task_start_time >= self.task_timeout:
                    log_info(f"[模拟器{self.emulator_id}] 总任务超时({self.task_timeout}秒).任务进度: {self.sent_count} / {self.message_count} 耗时: {time.time() - self.task_start_time:.2f}秒",
                            component="InstagramDMTask")
                    break

                log_info(f"[模拟器{self.emulator_id}] 当前进度: {self.sent_count}/{self.message_count}", component="InstagramDMTask")

                ##########  第3步：获取当前屏幕可见粉丝  ##########
                followers = await self._get_mine_followers()
                if not followers:
                    if retry_count >= max_retries:
                        log_error(f"[模拟器{self.emulator_id}] 连续{max_retries}次未找到粉丝，停止任务", component="InstagramDMTask")
                        break
                    retry_count += 1
                    log_warning(f"[模拟器{self.emulator_id}] 未找到粉丝，重试第{retry_count}次", component="InstagramDMTask")

                    # 滚动加载更多粉丝
                    await self._scroll_followers_list()
                    await asyncio.sleep(1)  # 优化：从2秒减少到1秒
                    continue

                log_info(f"[模拟器{self.emulator_id}] 找到 {len(followers)} 个可见粉丝", component="InstagramDMTask")
                retry_count = 0

                ##########  第4步：遍历每个粉丝  ##########
                for follower in followers:
                    if self.sent_count >= self.message_count:
                        log_info(f"[模拟器{self.emulator_id}] 已达到目标数量，退出循环", component="InstagramDMTask")
                        break

                    # 提取粉丝信息
                    username = follower.get('username', '')
                    if not username:
                        log_warning(f"[模拟器{self.emulator_id}] 粉丝用户名为空，跳过", component="InstagramDMTask")
                        continue

                    # 检查去重
                    if username in self.sent_users:
                        log_info(f"[模拟器{self.emulator_id}] 用户 {username} 已发送过，跳过", component="InstagramDMTask")
                        continue

                    # 发送私信给单个粉丝
                    log_info(f"[模拟器{self.emulator_id}] 📤 准备发送私信给: {username} (第{self.sent_count + 1}个)", component="InstagramDMTask")

                    send_result = await self._send_message_to_follower(follower)
                    if send_result:
                        self.sent_count += 1
                        log_info(f"[模拟器{self.emulator_id}] ✅ 成功发送第 {self.sent_count} 条私信给 {username}", component="InstagramDMTask")

                        # 保存已发送用户记录
                        await self._save_sent_user(username)

                        # 🎯 发送进度更新到UI
                        self._emit_progress_update()

                        # 用户间延迟
                        delay_ms = random.randint(self.delay_min, self.delay_max)
                        log_info(f"[模拟器{self.emulator_id}] ⏱️ 等待 {delay_ms}ms 后继续下一个用户", component="InstagramDMTask")
                        await asyncio.sleep(delay_ms / 1000.0)
                    else:
                        log_warning(f"[模拟器{self.emulator_id}] ❌ 发送私信给 {username} 失败，继续下一个", component="InstagramDMTask")
                        # 失败后也要短暂延迟，避免过快操作
                        await asyncio.sleep(1)

                ##########  第5步：检查是否需要滚动加载更多  ##########
                if self.sent_count < self.message_count:
                    # 检查"查看更多"按钮
                    if await self._check_and_click_view_more():
                        log_info(f"[模拟器{self.emulator_id}] 点击查看更多", component="InstagramDMTask")
                        await asyncio.sleep(1)
                        continue

                    # 检查是否到达底部
                    if await self._check_reached_bottom():
                        log_info(f"[模拟器{self.emulator_id}] 已到达粉丝列表底部", component="InstagramDMTask")
                        break

                    # 滚动加载更多粉丝
                    await self._scroll_followers_list()
                    await asyncio.sleep(1)  # 优化：从2秒减少到1秒

            log_info(f"[模拟器{self.emulator_id}] ✅ 批量私信发送完成，共发送 {self.sent_count} 条私信", component="InstagramDMTask")

            # 🎯 发送最终进度更新到UI
            self._emit_progress_update()
            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 批量私信发送异常: {e}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 获取当前屏幕可见的粉丝列表
    # ------------------------------------------------------------------------
    # 功能描述: 获取当前屏幕可见的自有粉丝列表，提取用户名和位置信息
    # 调用关系: 被_step_business_send_mass_dm调用，提供私信发送的目标用户列表
    # 注意事项: 只获取右侧按钮显示"移除"的粉丝，确保是自己的粉丝而非关注的用户
    # 技术实现: 批量XML解析，一次获取UI结构，避免重复XML操作，提升性能
    #
    # 执行步骤:
    # 第1步: 一次性获取UI结构 (uiautomator dump + cat)
    # 第2步: 解析XML查找所有粉丝容器节点 (follow_list_container)
    # 第3步: 批量处理每个容器，在容器范围内查找子元素
    #        - 查找用户名节点 (follow_list_username)
    #        - 查找右侧按钮节点 (follow_list_right_button)
    #        - 查找关注按钮 (text="关注"或"Follow")
    # 第4步: 行级筛选逻辑，只保留仅有"移除"按钮的行
    #        - 必须有用户名和移除按钮
    #        - 不能有关注按钮 (排除同时有关注和移除的行)
    # 第5步: 提取用户名文本和点击位置坐标
    # 第6步: 构建粉丝信息字典并返回列表
    # ------------------------------------------------------------------------
    async def _get_mine_followers(self) -> List[Dict[str, Any]]:
        """获取当前屏幕可见的粉丝列表 - 批量解析（一次XML获取）"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始批量获取当前屏幕可见的粉丝列表", component="InstagramDMTask")

            followers = []

            # 第1步: 一次性获取UI结构
            success, _ = self.ld.execute_ld(self.emulator_id, "uiautomator dump /sdcard/ui.xml")
            if not success:
                log_error(f"[模拟器{self.emulator_id}] 获取UI结构失败", component="InstagramDMTask")
                return []

            success, xml_content = self.ld.execute_ld(self.emulator_id, "cat /sdcard/ui.xml")
            if not success or not xml_content:
                log_error(f"[模拟器{self.emulator_id}] 读取XML内容失败", component="InstagramDMTask")
                return []

            # 第2步: 解析XML查找所有粉丝容器节点
            import xml.etree.ElementTree as ET
            import re

            try:
                root = ET.fromstring(xml_content)

                # 查找所有粉丝容器
                containers = []
                for elem in root.iter():
                    if elem.get('resource-id') == self.INSTAGRAM_ELEMENTS["follow_list_container"]:
                        containers.append(elem)

                # 移除粉丝容器数量的技术日志
                # log_info(f"[模拟器{self.emulator_id}] 找到 {len(containers)} 个粉丝容器", component="InstagramDMTask")
                log_info(f"[模拟器{self.emulator_id}] 找到 {len(containers)} 个粉丝", component="InstagramDMTask")

                # 第3步: 批量处理每个容器，在容器范围内查找子元素
                for container in containers:
                    # 初始化容器内的元素变量
                    username_elem = None
                    remove_button_elem = None
                    follow_button_elem = None

                    # 获取容器坐标范围
                    container_bounds = container.get('bounds', '')
                    if not container_bounds:
                        continue

                    container_coords = re.findall(r'\d+', container_bounds)
                    if len(container_coords) < 4:
                        continue

                    cx1, cy1, cx2, cy2 = map(int, container_coords[:4])

                    # 在容器范围内查找子元素
                    for child in root.iter():
                        child_bounds = child.get('bounds', '')
                        if not child_bounds:
                            continue

                        child_coords = re.findall(r'\d+', child_bounds)
                        if len(child_coords) < 4:
                            continue

                        chx1, chy1, chx2, chy2 = map(int, child_coords[:4])

                        # 检查是否在容器范围内
                        if chx1 >= cx1 and chy1 >= cy1 and chx2 <= cx2 and chy2 <= cy2:
                            # 查找用户名节点
                            if child.get('resource-id') == self.INSTAGRAM_ELEMENTS["follow_list_username"]:
                                username_elem = child

                            # 查找移除按钮节点
                            elif child.get('resource-id') == self.INSTAGRAM_ELEMENTS["follow_list_right_button"]:
                                button_text = child.get('text', '').strip()
                                if button_text in ['移除', 'Remove']:
                                    remove_button_elem = child

                            # 查找关注按钮节点
                            elif child.get('text', '').strip() in ['关注', 'Follow']:
                                follow_button_elem = child

                    # 第4步: 行级筛选逻辑，只保留仅有"移除"按钮的行
                    if username_elem is not None and remove_button_elem is not None:
                        # 关键筛选：如果同时有"关注"按钮，则排除此行
                        if follow_button_elem is not None:
                            continue

                        # 第5步: 提取用户名文本和点击位置坐标
                        username = username_elem.get('text', '').strip()
                        if username:
                            # 解析用户名位置坐标
                            username_bounds = username_elem.get('bounds', '')
                            username_coords = re.findall(r'\d+', username_bounds)
                            if len(username_coords) >= 4:
                                position = tuple(map(int, username_coords[:4]))
                            else:
                                position = None

                            # 第6步: 构建粉丝信息字典并添加到列表
                            followers.append({
                                'username': username,
                                'position': position,
                                'method': 'batch_parse',
                                'button_text': remove_button_elem.get('text', '').strip()
                            })
                            log_info(f"[模拟器{self.emulator_id}] ✅ 找到符合条件的粉丝: {username}", component="InstagramDMTask")

                # 移除批量解析的技术日志
                # log_info(f"[模拟器{self.emulator_id}] 批量解析完成，找到 {len(followers)} 个可操作粉丝", component="InstagramDMTask")
                return followers

            except ET.ParseError as e:
                log_error(f"[模拟器{self.emulator_id}] XML解析失败: {e}", component="InstagramDMTask")
                return []

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 获取粉丝列表异常: {e}", component="InstagramDMTask")
            return []



    # ------------------------------------------------------------------------
    # 🎯 根据发送模式生成随机私信内容
    # ------------------------------------------------------------------------
    # 功能描述: 根据UI配置的发送模式生成随机私信内容，支持三种发送策略
    # 调用关系: 被_send_message_to_follower调用，为每条私信提供内容
    # 注意事项: 支持|分隔的多条话术，自动过滤空值，提供默认话术兜底
    # 技术实现: 基于配置的send_mode参数选择内容源，使用random模块随机选择
    #
    # 执行步骤:
    # 第1步: 根据发送模式选择消息内容源
    #        - 模式1: 仅使用message_content_1
    #        - 模式2: 仅使用message_content_2
    #        - 模式3: 随机选择message_content_1或message_content_2
    # 第2步: 分割话术内容 (使用|分隔符)
    # 第3步: 过滤空值和空白字符
    # 第4步: 随机选择一条有效消息
    # 第5步: 如果没有有效消息，使用默认话术
    # ------------------------------------------------------------------------

    # ------------------------------------------------------------------------
    # 🎯 使用ldconsole.exe直接输入文本（保证完整性）
    # ------------------------------------------------------------------------
    async def _input_text_via_ldconsole(self, text: str) -> bool:
        """
        使用ldconsole.exe直接输入文本，保证完整性

        Args:
            text: 要输入的文本

        Returns:
            是否输入成功
        """
        try:
            # 获取ldconsole.exe路径
            emulator_path = self.config_manager.get("emulator_path", "G:/leidian/LDPlayer9")
            ldconsole_path = Path(emulator_path) / "ldconsole.exe"

            if not ldconsole_path.exists():
                log_error(f"[模拟器{self.emulator_id}] ldconsole.exe不存在: {ldconsole_path}", component="InstagramDMTask")
                return False

            # 移除ldconsole输入的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 使用ldconsole.exe直接输入，文本长度: {len(text)}", component="InstagramDMTask")

            # 构建命令 - 不做任何转义，保持原样
            command = [
                str(ldconsole_path),
                'action',
                '--index', str(self.emulator_id),
                '--key', 'call.input',
                '--value', text  # 直接使用原始文本
            ]

            # 执行命令
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                creationflags=0x08000000  # CREATE_NO_WINDOW
            )

            _, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=15
            )

            if process.returncode == 0:
                # 移除ldconsole输入成功的技术日志
                # log_info(f"[模拟器{self.emulator_id}] ✅ ldconsole.exe输入成功", component="InstagramDMTask")
                return True
            else:
                error = stderr.decode('utf-8', errors='ignore').strip()
                log_error(f"[模拟器{self.emulator_id}] ldconsole.exe输入失败: {error}", component="InstagramDMTask")
                return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] ldconsole.exe输入异常: {e}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 高效元素检测方法 - 智能等待优化
    # ------------------------------------------------------------------------
    async def _wait_for_element_fast(self, resource_id=None, text=None, timeout=3, interval=0.1):
        """
        高频检测元素出现 - 0.1秒间隔检测

        Args:
            resource_id: 资源ID
            text: 文本内容
            timeout: 超时时间（秒）
            interval: 检测间隔（秒）

        Returns:
            找到的元素或None
        """
        start_time = time.time()
        check_count = 0

        while time.time() - start_time < timeout:
            check_count += 1

            # 根据参数选择查找方式
            if resource_id:
                element = self.ld.find_node(resource_id=resource_id)
            elif text:
                element = self.ld.find_node(text=text)
            else:
                return None

            if element:
                elapsed = time.time() - start_time
                log_info(f"[模拟器{self.emulator_id}] ⚡ 快速检测成功: {elapsed:.2f}s, 检测{check_count}次", component="InstagramDMTask")
                return element

            await asyncio.sleep(interval)

        log_warning(f"[模拟器{self.emulator_id}] 快速检测超时: {timeout}s, 共检测{check_count}次", component="InstagramDMTask")
        return None

    # ------------------------------------------------------------------------
    # 🎯 长按操作方法 - 用于消息撤回
    # ------------------------------------------------------------------------
    async def _long_press_element(self, element, duration=2000):
        """
        长按元素（模拟长按操作）

        Args:
            element: 要长按的元素
            duration: 长按持续时间（毫秒）

        Returns:
            是否长按成功
        """
        try:
            if not element or 'center_x' not in element or 'center_y' not in element:
                log_error(f"[模拟器{self.emulator_id}] 长按失败：元素无效", component="InstagramDMTask")
                return False

            # 动态计算长按位置：消息上方1/4处（适配不同分辨率）
            center_x = element['center_x']
            center_y = element['center_y']

            # 获取元素边界信息来计算1/4位置
            bounds = element.get('bounds', '')
            message_type = element.get('message_type', 'unknown')



            if bounds:
                try:
                    # 解析边界信息 "[left,top][right,bottom]"
                    import re
                    coords = re.findall(r'\[(\d+),(\d+)\]', bounds)
                    if len(coords) >= 2:
                        _, top = int(coords[0][0]), int(coords[0][1])
                        _, bottom = int(coords[1][0]), int(coords[1][1])

                        # 动态计算消息高度的1/4位置
                        height = bottom - top
                        quarter_height = height // 4

                        # 长按位置：水平居中，垂直在上方1/4处
                        x = center_x  # 保持水平居中
                        y = top + quarter_height  # 上方1/4处


                    else:
                        # 边界解析失败，使用中心点
                        x, y = center_x, center_y
                        log_warning(f"[模拟器{self.emulator_id}] ⚠️ 边界解析失败，坐标数量不足: {len(coords)}，使用中心点", component="InstagramDMTask")
                except Exception as parse_error:
                    # 边界解析异常，使用中心点
                    x, y = center_x, center_y
                    log_warning(f"[模拟器{self.emulator_id}] ⚠️ 边界解析异常，使用中心点: {parse_error}", component="InstagramDMTask")
            else:
                # 没有边界信息，使用中心点
                x, y = center_x, center_y
                log_warning(f"[模拟器{self.emulator_id}] ⚠️ 无边界信息，使用中心点 - 消息类型: {message_type}", component="InstagramDMTask")

            # 使用input swipe命令模拟长按（优化位置）
            # 在计算出的坐标进行滑动，持续指定时间，实现长按效果
            swipe_command = f"input swipe {x} {y} {x} {y} {duration}"
            success, output = self.ld.execute_ld(self.emulator_id, swipe_command)

            if success:
                log_info(f"[模拟器{self.emulator_id}] 长按消息", component="InstagramDMTask")
                return True
            else:
                log_warning(f"[模拟器{self.emulator_id}] swipe长按失败: {output}", component="InstagramDMTask")
                return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 长按操作异常: {str(e)}", component="InstagramDMTask")
            return False



    # ------------------------------------------------------------------------
    # 🎯 私信撤回功能 - 发送前撤回之前的消息
    # ------------------------------------------------------------------------

    async def _find_all_message_elements(self):
        """
        异步批量查找所有类型的消息元素（不阻塞UI）

        支持的消息类型：
        1. 文本消息
        2. 图片/视频标题
        3. 媒体消息
        4. 消息内容布局

        Returns:
            list: 所有找到的消息元素列表，按类型标记
        """
        try:
            import asyncio
            from concurrent.futures import ThreadPoolExecutor

            # 定义要查找的消息元素类型
            message_element_types = [
                ("text_message", self.INSTAGRAM_ELEMENTS["direct_text_message"]),
                ("caption_container", self.INSTAGRAM_ELEMENTS["caption_container"]),
                ("media_container", self.INSTAGRAM_ELEMENTS["media_container"]),
                ("content_layout", self.INSTAGRAM_ELEMENTS["message_content_layout"])
            ]

            def find_single_type(element_type, resource_id):
                """查找单一类型的消息元素"""
                elements = self.ld.find_nodes(resource_id=resource_id)
                result = []
                if elements:
                    for element in elements:
                        element['message_type'] = element_type
                        result.append(element)
                return result, len(elements) if elements else 0

            all_message_elements = []
            type_counts = {}

            # 获取事件循环
            loop = asyncio.get_event_loop()

            # 使用异步执行器，不阻塞主线程
            with ThreadPoolExecutor(max_workers=4) as executor:
                tasks = []
                for element_type, resource_id in message_element_types:
                    # 将同步任务包装为异步任务
                    task = loop.run_in_executor(executor, find_single_type, element_type, resource_id)
                    tasks.append((element_type, task))

                # 异步等待所有任务完成（不阻塞UI）
                for element_type, task in tasks:
                    elements, count = await task  # 异步等待，不阻塞UI
                    all_message_elements.extend(elements)
                    if count > 0:
                        type_counts[element_type] = count

            # 按Y坐标排序（从上到下）
            if all_message_elements:
                all_message_elements.sort(key=lambda x: x.get('center_y', 0))
                log_info(f"[模拟器{self.emulator_id}] 找到 {len(all_message_elements)} 条消息", component="InstagramDMTask")
            else:
                log_info(f"[模拟器{self.emulator_id}] 未找到消息", component="InstagramDMTask")

            return all_message_elements

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 异步批量查找消息元素异常: {str(e)}", component="InstagramDMTask")
            return []

    async def _recall_previous_messages(self):
        """
        撤回之前发送的消息（支持多条撤回）

        流程：
        1. 查找消息元素
        2. 区分自己发送的消息（通过位置或其他特征）
        3. 长按消息元素
        4. 等待撤回选项出现
        5. 点击撤回
        6. 处理可能的确认弹窗
        7. 重新查找消息元素（因为撤回后位置会变化）
        8. 重复步骤1-6，最多撤回5条消息

        注意：每次撤回成功后都需要重新获取消息元素，
        因为撤回操作会改变剩余消息的位置和索引。

        Returns:
            是否撤回成功（至少撤回一条消息或没有消息可撤回）
        """
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始执行私信撤回功能（支持多条撤回）", component="InstagramDMTask")

            # 配置：最多撤回消息数量
            MAX_RECALL_COUNT = 10
            recalled_count = 0

            # 1. 异步查找所有类型的消息元素（不阻塞UI）
            message_elements = await self._find_all_message_elements()

            # 如果第一次没找到，等待界面加载后重试
            if not message_elements:
                log_warning(f"[模拟器{self.emulator_id}] 第一次未找到消息元素，等待界面加载后重试", component="InstagramDMTask")
                await asyncio.sleep(2.0)  # 等待界面完全加载
                message_elements = await self._find_all_message_elements()

                if not message_elements:
                    log_info(f"[模拟器{self.emulator_id}] 重试后仍未找到消息元素，可能是首次对话或界面异常", component="InstagramDMTask")
                    return True  # 没有消息可撤回，视为成功
                else:
                    log_info(f"[模拟器{self.emulator_id}] 重试成功，找到消息元素", component="InstagramDMTask")

            log_info(f"[模拟器{self.emulator_id}] 找到 {len(message_elements)} 个消息元素（多种类型），最多尝试撤回 {MAX_RECALL_COUNT} 条", component="InstagramDMTask")

            # 2. 尝试撤回多条消息（从最新的开始）
            # 使用while循环，每次撤回后重新开始
            while recalled_count < MAX_RECALL_COUNT and message_elements:
                # 总是尝试撤回最后一条消息（最新的消息）
                message_element = message_elements[-1]
                element_index = len(message_elements) - 1

                log_info(f"[模拟器{self.emulator_id}] 尝试撤回第 {element_index + 1} 条消息", component="InstagramDMTask")

                # 2.5. 【新增】右边界预判断 - 避免对对方消息进行无效长按
                boundary_result = self._is_own_message_by_boundary(message_element)

                if boundary_result is False:
                    # 预判断为对方消息，直接跳过
                    bounds = message_element.get('bounds', '')
                    if bounds:
                        import re
                        coords = re.findall(r'\d+', bounds)
                        if len(coords) >= 4:
                            right = int(coords[2])
                            screen_width = self.ld.get_screen_size(self.emulator_id)[0]
                            threshold = screen_width * 0.92
                            log_info(f"[模拟器{self.emulator_id}] 边界预判断: 右边界={right} < {threshold:.0f}，跳过对方消息", component="InstagramDMTask")
                        else:
                            log_info(f"[模拟器{self.emulator_id}] 边界预判断: 跳过对方消息", component="InstagramDMTask")
                    else:
                        log_info(f"[模拟器{self.emulator_id}] 边界预判断: 跳过对方消息", component="InstagramDMTask")

                    message_elements.pop()  # 移除这条消息
                    continue

                elif boundary_result is True:
                    # 预判断为自己消息，显示判断信息并继续长按
                    bounds = message_element.get('bounds', '')
                    if bounds:
                        import re
                        coords = re.findall(r'\d+', bounds)
                        if len(coords) >= 4:
                            right = int(coords[2])
                            screen_width = self.ld.get_screen_size(self.emulator_id)[0]
                            threshold = screen_width * 0.92
                            log_info(f"[模拟器{self.emulator_id}] 边界预判断: 右边界={right} >= {threshold:.0f}，判定为自己的消息", component="InstagramDMTask")
                        else:
                            log_info(f"[模拟器{self.emulator_id}] 边界预判断: 判定为自己的消息", component="InstagramDMTask")
                    else:
                        log_info(f"[模拟器{self.emulator_id}] 边界预判断: 判定为自己的消息", component="InstagramDMTask")
                else:
                    # boundary_result is None，无法判断，使用原有流程
                    log_info(f"[模拟器{self.emulator_id}] 边界预判断失败，使用原有撤回流程", component="InstagramDMTask")

                # 3. 长按消息元素
                long_press_success = await self._long_press_element(message_element, duration=2000)
                if not long_press_success:
                    log_warning(f"[模拟器{self.emulator_id}] 长按第 {element_index + 1} 条消息失败", component="InstagramDMTask")
                    # 移除这条消息，避免无限重试
                    message_elements.pop()
                    continue

                # 4. 智能等待撤回选项出现

                # 先快速检查一次，如果是对方消息则立即跳过
                recall_option = await self._find_recall_option()

                if not recall_option:
                    # 第一次没找到，可能需要等待菜单出现，但限制等待时间
                    max_wait_time = 1.0  # 减少到1秒
                    check_interval = 0.3  # 增加到300ms检查一次
                    elapsed_time = 0

                    while elapsed_time < max_wait_time:
                        await asyncio.sleep(check_interval)
                        elapsed_time += check_interval

                        # 尝试查找撤回选项
                        recall_option = await self._find_recall_option()
                        if recall_option:
                            log_info(f"[模拟器{self.emulator_id}] ✅ 撤回选项已出现，耗时: {elapsed_time:.1f}s", component="InstagramDMTask")
                            break

                    # 如果还是没找到，说明是对方消息
                    if not recall_option:
                        log_info(f"[模拟器{self.emulator_id}] 智能等待超时，可能是对方发送的消息", component="InstagramDMTask")
                else:
                    log_info(f"[模拟器{self.emulator_id}] ✅ 撤回选项立即找到", component="InstagramDMTask")

                if recall_option:
                    # 5. 点击撤回
                    log_info(f"[模拟器{self.emulator_id}] 找到撤回选项，点击撤回", component="InstagramDMTask")

                    # 使用图色识别结果点击撤回（雷电一键找图）
                    x, y = recall_option['center_x'], recall_option['center_y']
                    template_name = recall_option['template']

                    log_info(f"[模拟器{self.emulator_id}] 使用图色识别点击撤回: 模板={template_name} 坐标=({x},{y})", component="InstagramDMTask")

                    # 执行点击撤回操作
                    click_success = self.ld.touch(self.emulator_id, x, y)
                    log_info(f"[模拟器{self.emulator_id}] 点击撤回操作: {click_success}", component="InstagramDMTask")

                    # 处理确认弹窗
                    await self._handle_recall_confirmation()

                    # 等待界面更新
                    await asyncio.sleep(1.5)

                    # 每次都异步重新获取消息元素（无论撤回成功失败，界面都可能变化）
                    log_info(f"[模拟器{self.emulator_id}] 重新获取消息元素（撤回后位置已变化）", component="InstagramDMTask")
                    message_elements = await self._find_all_message_elements()

                    # 简单判断：如果没有消息了，结束撤回
                    if not message_elements:
                        log_info(f"[模拟器{self.emulator_id}] 撤回后无更多消息，撤回完成", component="InstagramDMTask")
                        break

                    # 记录撤回操作（假设每次点击都可能成功）
                    recalled_count += 1
                    log_info(f"[模拟器{self.emulator_id}] ✅ 第 {recalled_count} 条消息撤回操作完成，剩余 {len(message_elements)} 条消息", component="InstagramDMTask")

                    # 调试：检查重新获取的元素是否有边界信息
                    if message_elements:
                        last_element = message_elements[-1]
                        bounds_info = last_element.get('bounds', '')
                        msg_type = last_element.get('message_type', 'unknown')
                        log_info(f"[模拟器{self.emulator_id}] 🔍 下一条待撤回消息 - 类型: {msg_type}, 边界: '{bounds_info[:50]}{'...' if len(bounds_info) > 50 else ''}'", component="InstagramDMTask")

                    # 继续while循环，尝试撤回下一条消息
                    continue
                else:
                    log_info(f"[模拟器{self.emulator_id}] 未找到撤回选项，可能是对方的消息", component="InstagramDMTask")
                    log_info(f"[模拟器{self.emulator_id}] 对方消息，取消菜单", component="InstagramDMTask")

                    # 获取屏幕分辨率
                    screen_width = self.ld.get_screen_size(self.emulator_id)[0]
                    screen_height = self.ld.get_screen_size(self.emulator_id)[1]

                    # 固定比例计算点击位置（基于260×400分辨率的5×200位置）
                    # X比例：5/260 = 0.0192 (1.92%)
                    # Y比例：200/400 = 0.5 (50%)
                    click_x = round(screen_width * 0.0192)
                    click_y = round(screen_height * 0.5)

                    # 边界检查，确保点击位置安全
                    click_x = max(3, min(click_x, 30))  # X坐标限制在3-30像素
                    click_y = max(50, min(click_y, screen_height - 50))  # Y坐标避开顶部和底部



                    # 点击左侧空白区域取消菜单
                    self.ld.touch(self.emulator_id, click_x, click_y)
                    await asyncio.sleep(0.5)  # 等待菜单消失

                    # 移除这条消息，尝试下一条
                    message_elements.pop()  # 移除最后一条消息，避免重复尝试
                    continue

            # 撤回流程完成
            if recalled_count > 0:
                log_info(f"[模拟器{self.emulator_id}] 撤回流程完成，成功撤回 {recalled_count} 条消息", component="InstagramDMTask")
            else:
                log_info(f"[模拟器{self.emulator_id}] 撤回流程完成，未找到可撤回的消息", component="InstagramDMTask")

            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 私信撤回异常: {str(e)}", component="InstagramDMTask")
            return False

    async def _find_recall_option(self):
        """
        使用雷电一键找图查找撤回选项（优化版本）

        Returns:
            找到的撤回选项坐标 (x, y) 或 None
        """
        try:


            # 导入雷电一键找图功能（只使用这一套图色识别）
            from core.leidianapi.雷电一键找图 import find_image_position

            # 撤回按钮图片模板列表（优先级排序）
            import os
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            recall_templates = [
                ("chehui.png", os.path.join(project_root, "img", "chehui.png")),      # 主要模板
                ("chehui2.png", os.path.join(project_root, "img", "chehui2.png"))     # 备用模板
            ]

            # 获取模拟器窗口标题
            window_title = f"雷电模拟器-{self.emulator_id}"

            # 尝试每个撤回按钮模板（优化：找到第一个就返回）
            for template_name, template_path in recall_templates:
                # 检查模板文件是否存在
                if not os.path.exists(template_path):
                    continue

                try:
                    # 使用雷电一键找图功能（性能最优）
                    result = find_image_position(
                        template_path=template_path,
                        window_title=window_title
                    )

                    if result:
                        # 解析结果并返回
                        if len(result) == 4:  # 包含耗时和置信度信息
                            x, y, _, confidence = result
                            log_info(f"[模拟器{self.emulator_id}] ✅ 找到撤回按钮 (置信度:{confidence:.3f})", component="InstagramDMTask")
                        elif len(result) == 3:  # 包含耗时信息
                            x, y, _ = result
                            confidence = 0.8
                            log_info(f"[模拟器{self.emulator_id}] ✅ 找到撤回按钮 (置信度:{confidence:.3f})", component="InstagramDMTask")
                        else:  # 兼容旧版本
                            x, y = result
                            confidence = 0.8
                            log_info(f"[模拟器{self.emulator_id}] ✅ 找到撤回按钮 (置信度:{confidence:.3f})", component="InstagramDMTask")

                        # 立即返回找到的结果（优化：不继续查找其他模板）
                        return {
                            'center_x': x,
                            'center_y': y,
                            'text': '撤回',
                            'clickable': True,
                            'bounds': f"[{x-20},{y-10}][{x+20},{y+10}]",
                            'class': 'ImageRecognition',
                            'template': template_name,
                            'confidence': confidence
                        }

                except Exception:
                    continue

            log_info(f"[模拟器{self.emulator_id}] 撤回按钮未找到", component="InstagramDMTask")
            return None

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 雷电一键找图查找撤回选项异常: {str(e)}", component="InstagramDMTask")
            return None

    async def _is_own_message(self, message_element):
        """
        判断消息是否是自己发送的

        Args:
            message_element: 消息元素

        Returns:
            bool: True表示是自己发送的消息
        """
        try:
            # 方法1: 通过消息位置判断（自己的消息通常在右侧）
            screen_width, _ = self.ld.get_screen_size(self.emulator_id)
            message_x = message_element.get('center_x', 0)

            # 如果消息在屏幕右半部分，可能是自己发送的
            is_right_side = message_x > screen_width * 0.6

            # 方法2: 检查消息的样式特征（这里简化处理）
            # 在实际应用中，可以通过更多特征来判断，如bounds、颜色等



            # 简单判断：如果在右侧，认为是自己的消息
            return is_right_side

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 判断消息归属异常: {str(e)}", component="InstagramDMTask")
            # 出错时默认认为是自己的消息，尝试撤回
            return True

    async def _handle_recall_confirmation(self):
        """
        处理撤回确认弹窗

        可能出现的情况：
        1. 直接撤回成功，无弹窗
        2. 出现"撤回"确认弹窗
        3. 出现"确定"确认弹窗
        4. 出现两个弹窗（先撤回，后确定）
        """
        try:
            log_info(f"[模拟器{self.emulator_id}] 处理撤回确认弹窗", component="InstagramDMTask")

            # 等待可能的弹窗出现
            await asyncio.sleep(0.5)

            # 检查是否有"撤回"弹窗
            recall_confirm = await self._wait_for_element_fast(text="撤回", timeout=2, interval=0.1)
            if recall_confirm:
                log_info(f"[模拟器{self.emulator_id}] 发现撤回确认弹窗，点击撤回", component="InstagramDMTask")
                self.ld.click_node(recall_confirm, self.emulator_id)
                await asyncio.sleep(0.5)

            # 检查是否有"确定"弹窗
            confirm_button = await self._wait_for_element_fast(text="确定", timeout=2, interval=0.1)
            if confirm_button:
                log_info(f"[模拟器{self.emulator_id}] 发现确定弹窗，点击确定", component="InstagramDMTask")
                self.ld.click_node(confirm_button, self.emulator_id)
                await asyncio.sleep(0.5)

            log_info(f"[模拟器{self.emulator_id}] 撤回确认处理完成", component="InstagramDMTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 处理撤回确认异常: {str(e)}", component="InstagramDMTask")

    def _is_own_message_by_boundary(self, message_element):
        """
        基于右边界判断消息归属（预判断方法）

        Args:
            message_element: 消息元素

        Returns:
            bool: True=自己的消息, False=对方的消息
            None: 无法判断（边界信息缺失等）
        """
        try:
            bounds = message_element.get('bounds', '')
            if not bounds:
                return None  # 无边界信息，无法判断

            # 解析边界信息 "[left,top][right,bottom]"
            import re
            coords = re.findall(r'\d+', bounds)
            if len(coords) < 4:
                return None  # 边界信息不完整

            right = int(coords[2])  # 右边界
            screen_width = self.ld.get_screen_size(self.emulator_id)[0]

            # 判断阈值：92%的屏幕宽度
            threshold = screen_width * 0.92

            if right >= threshold:
                return True  # 自己的消息
            else:
                return False  # 对方的消息

        except Exception as e:
            log_warning(f"[模拟器{self.emulator_id}] 边界判断异常: {e}，将使用原有撤回流程", component="InstagramDMTask")
            return None  # 判断失败，使用原有流程

    async def recall_messages(self):
        """
        公共撤回消息方法

        Returns:
            bool: 撤回是否成功
        """
        try:
            return await self._recall_previous_messages()
        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 撤回消息异常: {str(e)}", component="InstagramDMTask")
            return False

    async def _dismiss_long_press_menu(self):
        """
        取消长按菜单（点击空白区域）
        """
        try:
            # 点击屏幕中央空白区域来取消菜单
            # 获取屏幕尺寸
            screen_width, screen_height = self.ld.get_screen_size(self.emulator_id)
            center_x = screen_width // 2
            center_y = screen_height // 2

            # 点击中央区域
            self.ld.touch(self.emulator_id, center_x, center_y)
            await asyncio.sleep(0.3)

            log_info(f"[模拟器{self.emulator_id}] 已取消长按菜单", component="InstagramDMTask")

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 取消长按菜单异常: {str(e)}", component="InstagramDMTask")

    async def _get_random_message(self) -> str:
        """根据发送模式生成随机私信内容"""
        try:
            # 1. 根据发送模式选择消息内容源
            if self.send_mode == 1:
                # 模式1: 仅使用message_content_1
                content = self.message_content_1
            elif self.send_mode == 2:
                # 模式2: 仅使用message_content_2
                content = self.message_content_2
            elif self.send_mode == 3:
                # 模式3: 分段发送模式（注意：正常情况下不会执行到这里，因为分段发送在发送逻辑中独立处理）
                # 这里作为备用逻辑，随机选择一个内容
                content = random.choice([self.message_content_1, self.message_content_2])
            else:
                # 默认使用内容1
                content = self.message_content_1

            # 2. 分割话术内容并过滤空值
            messages = [m.strip() for m in content.split('|') if m.strip()]

            # 3. 检查有效消息并提供默认话术
            if not messages:
                # 5. 如果没有有效消息，使用默认话术
                default_messages = ['Hello!', 'Hi!', 'Nice to meet you!']
                return random.choice(default_messages)

            # 4. 随机选择一条有效消息
            selected_message = random.choice(messages)
            # 移除消息选择的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 选择消息: {selected_message}", component="InstagramDMTask")
            return selected_message

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 生成随机消息异常: {e}", component="InstagramDMTask")
            return "Hello!"

    # ------------------------------------------------------------------------
    # 🎯 发送私信给单个粉丝
    # ------------------------------------------------------------------------
    # 功能描述: 向单个粉丝发送私信的完整流程，包含导航、输入、发送、返回
    # 调用关系: 被_step_business_send_mass_dm调用，处理单个粉丝的私信发送
    # 注意事项: 包含完整的错误处理和重试机制，失败时自动返回粉丝列表
    # 技术实现: 基于Instagram UI自动化，支持多步骤操作和状态验证
    #
    # 执行步骤:
    # 第1步: 点击粉丝头像进入主页 (使用position坐标)
    # 第2步: 等待并点击"发消息"按钮 (最多重试5次)
    # 第3步: 点击私信输入框 (message_input元素)
    # 第4步: 输入私信内容 (调用_get_random_message获取内容)
    # 第5步: 点击发送按钮 (send_button元素)
    # 第6步: 返回粉丝列表 (调用_return_to_followers_list)
    #
    # 错误处理: 每个步骤失败都会调用_return_to_followers_list确保状态一致
    # ------------------------------------------------------------------------
    async def _send_message_to_follower(self, follower: Dict[str, Any]) -> bool:
        """发送私信给单个粉丝"""
        try:
            username = follower.get('username', '')
            position = follower.get('position')

            log_info(f"[模拟器{self.emulator_id}] 开始向 {username} 发送私信", component="InstagramDMTask")

            if not position:
                log_error(f"[模拟器{self.emulator_id}] 粉丝 {username} 位置信息缺失", component="InstagramDMTask")
                return False

            # 1. 点击用户名进入主页
            log_info(f"[模拟器{self.emulator_id}] 点击用户名进入主页", component="InstagramDMTask")

            # 计算点击坐标（用户名中心点）
            if len(position) == 4:
                # position = (x1, y1, x2, y2)
                center_x = (position[0] + position[2]) // 2
                center_y = (position[1] + position[3]) // 2
                # 移除坐标详细信息的技术日志
                # log_info(f"[模拟器{self.emulator_id}] 点击用户名坐标: 原始={position}, 中心点=({center_x}, {center_y})", component="InstagramDMTask")
            else:
                # 兼容旧格式
                center_x, center_y = position[0], position[1]
                # 移除坐标详细信息的技术日志
                # log_info(f"[模拟器{self.emulator_id}] 点击用户名坐标: ({center_x}, {center_y})", component="InstagramDMTask")

            # 使用click_node方法，和V2节点保持一致
            # 构造节点对象
            user_node = {
                'center_x': center_x,
                'center_y': center_y
            }

            success = self.ld.click_node(user_node, self.emulator_id)
            # 移除点击结果的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 点击结果: {success}", component="InstagramDMTask")

            if not success:
                log_error(f"[模拟器{self.emulator_id}] 点击用户名失败", component="InstagramDMTask")
                return False

            log_info(f"[模拟器{self.emulator_id}] ✅ 点击用户名成功", component="InstagramDMTask")

            # 2. 智能等待并点击"发消息"按钮
            log_info(f"[模拟器{self.emulator_id}] 智能等待发消息按钮出现", component="InstagramDMTask")

            # 2.1 智能等待发消息按钮出现 (3秒超时，0.1秒间隔)
            message_button = await self._wait_for_element_fast(
                text="发消息", timeout=3, interval=0.1
            )

            if not message_button:
                log_error(f"[模拟器{self.emulator_id}] 未找到发消息按钮", component="InstagramDMTask")
                await self._return_to_followers_list()
                return False

            # 2.2 点击发消息按钮
            click_result = self.ld.click_node(message_button)
            if not click_result:
                log_error(f"[模拟器{self.emulator_id}] 点击发消息按钮失败", component="InstagramDMTask")
                await self._return_to_followers_list()
                return False

            # 2.3 执行撤回功能（如果启用）
            if self.recall_before_dm:
                log_info(f"[模拟器{self.emulator_id}] 撤回功能已启用，开始执行撤回", component="InstagramDMTask")
                recall_success = await self._recall_previous_messages()
                if recall_success:
                    log_info(f"[模拟器{self.emulator_id}] ✅ 撤回功能执行完成", component="InstagramDMTask")
                else:
                    log_warning(f"[模拟器{self.emulator_id}] ⚠️ 撤回功能执行失败，继续发送流程", component="InstagramDMTask")
            else:
                log_info(f"[模拟器{self.emulator_id}] 撤回功能未启用，跳过撤回步骤", component="InstagramDMTask")

            # 3. 智能等待并点击私信输入框
            log_info(f"[模拟器{self.emulator_id}] 智能等待输入框出现", component="InstagramDMTask")

            # 3.1 智能等待输入框出现 (2秒超时，0.1秒间隔)
            input_box = await self._wait_for_element_fast(
                resource_id=self.INSTAGRAM_ELEMENTS["message_input"],
                timeout=2, interval=0.1
            )

            if not input_box:
                log_error(f"[模拟器{self.emulator_id}] 未找到输入框", component="InstagramDMTask")
                await self._return_to_followers_list()
                return False

            # 3.2 点击输入框
            click_result = self.ld.click_node(input_box)
            if not click_result:
                log_error(f"[模拟器{self.emulator_id}] 点击输入框失败", component="InstagramDMTask")
                await self._return_to_followers_list()
                return False

            # 智能等待输入框激活 (检测输入框是否可用)
            log_info(f"[模拟器{self.emulator_id}] 智能等待输入框激活", component="InstagramDMTask")
            input_ready = await self._wait_for_element_fast(
                resource_id=self.INSTAGRAM_ELEMENTS["message_input"],
                timeout=1, interval=0.05
            )
            if not input_ready:
                log_warning(f"[模拟器{self.emulator_id}] 输入框激活检测超时，继续执行", component="InstagramDMTask")

            # ========================================================================
            # 🎯 第4步：根据发送模式执行不同的发送策略
            # ========================================================================
            # 功能描述: 根据send_mode参数选择发送策略
            # 模式1: 仅发送私信内容1 (从内容1中用|分割随机选择)
            # 模式2: 仅发送私信内容2 (从内容2中用|分割随机选择)
            # 模式3: 分段发送私信内容1，私信内容2 (先发送内容1，再发送内容2)
            # ========================================================================

            if self.send_mode == 3:
                # ------------------------------------------------------------------------
                # 🎯 分段发送模式：完整处理两段发送流程
                # ------------------------------------------------------------------------
                # 执行流程: 内容1选择→输入→发送→延迟→内容2选择→输入→发送→返回
                # 特点: 每段都支持|分割的随机话术选择，真正的分段发送体验
                # 注意: 分段发送完成后直接返回，不执行后续的单条发送逻辑
                # ------------------------------------------------------------------------
                log_info(f"[模拟器{self.emulator_id}] 🎯 分段发送模式", component="InstagramDMTask")

                # ========================================================================
                # 🎯 第一段发送：从message_content_1中随机选择并发送
                # ========================================================================

                # 4.1 解析第一段内容并随机选择
                messages1 = [m.strip() for m in self.message_content_1.split('|') if m.strip()]
                message1 = random.choice(messages1) if messages1 else "Hello!"
                log_info(f"[模拟器{self.emulator_id}] 第一段内容选择: {message1}", component="InstagramDMTask")

                # 4.2 输入第一段内容
                log_info(f"[模拟器{self.emulator_id}] 开始输入第一段内容", component="InstagramDMTask")
                input_result1 = await self._input_text_via_ldconsole(message1)
                if not input_result1:
                    log_error(f"[模拟器{self.emulator_id}] 输入第一段内容失败", component="InstagramDMTask")
                    await self._return_to_followers_list()
                    return False

                # 4.3 发送第一段内容
                log_info(f"[模拟器{self.emulator_id}] 准备发送第一段内容", component="InstagramDMTask")
                await asyncio.sleep(self.message_delay / 1000.0)  # 消息输入后延迟

                # 查找发送按钮（支持新旧版本）
                send_button1 = self.ld.find_node(resource_id=self.INSTAGRAM_ELEMENTS["send_button_container"]) or \
                              self.ld.find_node(resource_id=self.INSTAGRAM_ELEMENTS["send_button"])
                if not send_button1 or not self.ld.click_node(send_button1):
                    log_error(f"[模拟器{self.emulator_id}] 发送第一段失败", component="InstagramDMTask")
                    await self._return_to_followers_list()
                    return False

                log_info(f"[模拟器{self.emulator_id}] ✅ 第一段发送成功", component="InstagramDMTask")

                # ========================================================================
                # 🎯 分段间延迟：智能等待第一段发送完成，准备第二段
                # ========================================================================
                log_info(f"[模拟器{self.emulator_id}] 智能等待分段间隔", component="InstagramDMTask")
                # 检测输入框是否重新可用（表示第一段已发送完成）
                input_ready = await self._wait_for_element_fast(
                    resource_id=self.INSTAGRAM_ELEMENTS["message_input"],
                    timeout=2, interval=0.1
                )
                if not input_ready:
                    log_warning(f"[模拟器{self.emulator_id}] 分段间隔检测超时，继续执行", component="InstagramDMTask")

                # ========================================================================
                # 🎯 第二段发送：从message_content_2中随机选择并发送
                # ========================================================================

                # 4.4 解析第二段内容并随机选择
                messages2 = [m.strip() for m in self.message_content_2.split('|') if m.strip()]
                message2 = random.choice(messages2) if messages2 else "Nice day!"
                log_info(f"[模拟器{self.emulator_id}] 第二段内容选择: {message2}", component="InstagramDMTask")

                # 4.5 输入第二段内容
                log_info(f"[模拟器{self.emulator_id}] 开始输入第二段内容", component="InstagramDMTask")
                input_result2 = await self._input_text_via_ldconsole(message2)
                if not input_result2:
                    log_error(f"[模拟器{self.emulator_id}] 输入第二段内容失败", component="InstagramDMTask")
                    await self._return_to_followers_list()
                    return False

                # 4.6 发送第二段内容
                log_info(f"[模拟器{self.emulator_id}] 准备发送第二段内容", component="InstagramDMTask")
                await asyncio.sleep(self.message_delay / 1000.0)  # 消息输入后延迟

                # 查找发送按钮（支持新旧版本）
                send_button2 = self.ld.find_node(resource_id=self.INSTAGRAM_ELEMENTS["send_button_container"]) or \
                              self.ld.find_node(resource_id=self.INSTAGRAM_ELEMENTS["send_button"])
                if not send_button2 or not self.ld.click_node(send_button2):
                    log_error(f"[模拟器{self.emulator_id}] 发送第二段失败", component="InstagramDMTask")
                    await self._return_to_followers_list()
                    return False

                log_info(f"[模拟器{self.emulator_id}] ✅ 第二段发送成功", component="InstagramDMTask")

                # ========================================================================
                # 🎯 分段发送完成处理：等待发送完成并返回粉丝列表
                # ========================================================================

                # 4.7 等待分段发送完成 (固定0.5秒)
                log_info(f"[模拟器{self.emulator_id}] 等待分段发送完成", component="InstagramDMTask")
                await asyncio.sleep(0.5)

                # 4.8 返回粉丝列表
                log_info(f"[模拟器{self.emulator_id}] 开始返回粉丝列表", component="InstagramDMTask")
                if not await self._return_to_followers_list():
                    log_error(f"[模拟器{self.emulator_id}] 返回粉丝列表失败", component="InstagramDMTask")
                    return False

                # 4.9 分段发送任务完成
                log_info(f"[模拟器{self.emulator_id}] ✅ 分段发送完成，成功向 {username} 发送私信", component="InstagramDMTask")
                return True  # 分段发送完成，直接返回，不执行后续单条发送逻辑

            # ========================================================================
            # 🎯 单条发送模式（模式1和2）- 保持原有逻辑完全不变
            # ========================================================================
            # 功能描述: 传统的单条消息发送流程
            # 模式1: 从message_content_1中随机选择一条发送
            # 模式2: 从message_content_2中随机选择一条发送
            # 执行流程: 消息选择→输入→延迟→发送按钮点击→返回
            # ========================================================================

            # 4.1 获取随机消息内容
            message = await self._get_random_message()
            # 移除输入内容的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 输入私信内容: {message}", component="InstagramDMTask")

            # 4.2 使用ldconsole.exe直接输入，保证完整性
            input_result = await self._input_text_via_ldconsole(message)
            # 移除输入结果的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 输入结果: {input_result}", component="InstagramDMTask")

            # 4.3 输入失败处理
            if not input_result:
                log_error(f"[模拟器{self.emulator_id}] 输入私信内容失败", component="InstagramDMTask")
                await self._return_to_followers_list()
                return False

            log_info(f"[模拟器{self.emulator_id}] ✅ 私信内容输入成功", component="InstagramDMTask")

            # 4.4 消息间延迟（为发送按钮点击做准备）
            log_info(f"[模拟器{self.emulator_id}] 等待消息延迟: {self.message_delay}ms", component="InstagramDMTask")
            await asyncio.sleep(self.message_delay / 1000.0)

            # 5. 点击发送按钮
            # 移除发送按钮查找的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 开始查找并点击发送按钮", component="InstagramDMTask")

            # 5.1 查找发送按钮（支持多个版本）
            send_button = None
            send_button_candidates = [
                self.INSTAGRAM_ELEMENTS["send_button_container"], # 新版本（优先）
                self.INSTAGRAM_ELEMENTS["send_button"]            # 旧版本（兼容）
            ]

            # 移除发送按钮查找的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 开始查找发送按钮，支持多个版本", component="InstagramDMTask")

            for attempt in range(5):  # 最多尝试5次
                # 移除发送按钮查找尝试的技术日志
                # log_info(f"[模拟器{self.emulator_id}] 发送按钮查找尝试 {attempt + 1}/5", component="InstagramDMTask")

                # 尝试所有候选resource-id
                for candidate_id in send_button_candidates:
                    # 移除候选尝试的技术日志
                    # log_info(f"[模拟器{self.emulator_id}] 尝试候选{i+1}: {candidate_id}", component="InstagramDMTask")
                    send_button = self.ld.find_node(resource_id=candidate_id)

                    if send_button:
                        # 移除发送按钮详细信息的技术日志
                        # log_info(f"[模拟器{self.emulator_id}] ✅ 找到发送按钮 (候选{i+1}): {send_button}", component="InstagramDMTask")
                        log_info(f"[模拟器{self.emulator_id}] ✅ 找到发送按钮", component="InstagramDMTask")
                        break

                if send_button:
                    break
                else:
                    log_warning(f"[模拟器{self.emulator_id}] 第{attempt + 1}次所有候选都未找到，等待0.2秒后重试", component="InstagramDMTask")
                    await asyncio.sleep(0.2)

            if not send_button:
                log_error(f"[模拟器{self.emulator_id}] 5次尝试后仍未找到发送按钮", component="InstagramDMTask")

                # 调试：查找页面上所有可能的发送相关按钮
                log_info(f"[模拟器{self.emulator_id}] 调试：查找页面上所有包含'发送'或'send'的元素", component="InstagramDMTask")
                all_buttons = self.ld.find_nodes(clickable="true")
                for btn in all_buttons[:10]:  # 只显示前10个
                    btn_text = btn.get('text', '').strip()
                    btn_desc = btn.get('content-desc', '').strip()
                    btn_id = btn.get('resource-id', '').strip()
                    if any(keyword in (btn_text + btn_desc + btn_id).lower()
                          for keyword in ['send', '发送', 'composer']):
                        log_info(f"[模拟器{self.emulator_id}] 可能的发送按钮: text='{btn_text}', desc='{btn_desc}', id='{btn_id}'", component="InstagramDMTask")

                await self._return_to_followers_list()
                return False

            # 5.2 点击发送按钮
            click_result = self.ld.click_node(send_button)
            if not click_result:
                log_error(f"[模拟器{self.emulator_id}] 点击发送按钮失败", component="InstagramDMTask")
                await self._return_to_followers_list()
                return False

            # 等待发送完成 (固定0.5秒)
            await asyncio.sleep(0.5)

            # 6. 返回粉丝列表
            if not await self._return_to_followers_list():
                log_error(f"[模拟器{self.emulator_id}] 返回粉丝列表失败", component="InstagramDMTask")
                return False

            log_info(f"[模拟器{self.emulator_id}] ✅ 成功向 {username} 发送私信", component="InstagramDMTask")
            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 发送私信异常: {e}", component="InstagramDMTask")
            await self._return_to_followers_list()
            return False

    # ------------------------------------------------------------------------
    # 🎯 返回粉丝列表
    # ------------------------------------------------------------------------
    async def _return_to_followers_list(self) -> bool:
        """
        返回粉丝列表 - 需要点击2次返回按钮
        ========================================
        功能描述: 从私信界面返回到粉丝列表，需要执行2次返回操作

        返回路径:
        1. 第1次返回: 私信界面 → 用户主页
        2. 第2次返回: 用户主页 → 粉丝列表

        技术实现: 连续调用2次通用返回方法，每次返回后等待页面加载
        ========================================
        """
        try:
            log_info(f"[模拟器{self.emulator_id}] 开始返回粉丝列表（需要2次返回）", component="InstagramDMTask")

            # 第1次返回: 私信界面 → 用户主页 (使用title_container)
            log_info(f"[模拟器{self.emulator_id}] 第1次返回: 私信界面 → 用户主页", component="InstagramDMTask")
            first_back = await self._navigate_back_instagram_with_element("title_container")
            if not first_back:
                log_error(f"[模拟器{self.emulator_id}] 第1次返回失败", component="InstagramDMTask")
                return False

            # 智能等待用户主页加载
            log_info(f"[模拟器{self.emulator_id}] 智能等待用户主页加载", component="InstagramDMTask")
            profile_loaded = await self._wait_for_element_fast(
                text="发消息", timeout=1, interval=0.1
            )
            if not profile_loaded:
                log_warning(f"[模拟器{self.emulator_id}] 用户主页加载检测超时，继续执行", component="InstagramDMTask")

            # 第2次返回: 用户主页 → 粉丝列表 (使用back_button)
            log_info(f"[模拟器{self.emulator_id}] 第2次返回: 用户主页 → 粉丝列表", component="InstagramDMTask")
            second_back = await self._navigate_back_instagram_with_element("back_button")
            if not second_back:
                log_error(f"[模拟器{self.emulator_id}] 第2次返回失败", component="InstagramDMTask")
                return False

            # 智能等待粉丝列表加载
            log_info(f"[模拟器{self.emulator_id}] 智能等待粉丝列表加载", component="InstagramDMTask")
            list_loaded = await self._wait_for_element_fast(
                resource_id=self.INSTAGRAM_ELEMENTS["list_view"], timeout=1, interval=0.1
            )
            if not list_loaded:
                log_warning(f"[模拟器{self.emulator_id}] 粉丝列表加载检测超时，继续执行", component="InstagramDMTask")

            log_info(f"[模拟器{self.emulator_id}] ✅ 成功返回粉丝列表", component="InstagramDMTask")
            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 返回粉丝列表异常: {e}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 Instagram指定元素返回导航方法
    # ------------------------------------------------------------------------
    async def _navigate_back_instagram_with_element(self, element_key: str) -> bool:
        """
        使用指定元素ID执行Instagram返回导航
        ========================================
        功能描述: 根据指定的元素键名执行返回操作

        参数:
            element_key: INSTAGRAM_ELEMENTS中的键名
                - "title_container": 用于私信界面 → 用户主页
                - "back_button": 用于用户主页 → 粉丝列表

        Returns:
            bool: 返回操作是否成功
        ========================================
        """
        try:
            element_id = self.INSTAGRAM_ELEMENTS[element_key]
            # 移除返回操作的技术日志
            # log_info(f"[模拟器{self.emulator_id}] 使用元素 {element_key} ({element_id}) 执行返回", component="InstagramDMTask")

            # 查找指定元素
            back_element = self.ld.find_node(resource_id=element_id)

            if not back_element:
                log_error(f"[模拟器{self.emulator_id}] 未找到返回元素: {element_key}", component="InstagramDMTask")
                return False

            # 点击返回元素
            click_result = self.ld.click_node(back_element)
            if not click_result:
                log_error(f"[模拟器{self.emulator_id}] 点击返回元素失败: {element_key}", component="InstagramDMTask")
                return False

            # 智能等待页面加载 (0.3秒固定等待)
            await asyncio.sleep(0.3)

            log_info(f"[模拟器{self.emulator_id}] ✅ 返回操作成功 ({element_key})", component="InstagramDMTask")
            return True

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 返回导航异常 ({element_key}): {e}", component="InstagramDMTask")
            return False



    # ------------------------------------------------------------------------
    # 🎯 保存已发送用户记录
    # ------------------------------------------------------------------------
    # 功能描述: 保存已发送私信的用户记录，实现去重机制和持久化存储
    # 调用关系: 被_step_business_send_mass_dm调用，记录每次成功发送的用户
    # 注意事项: 同时更新内存集合和文件存储，确保数据一致性和持久化
    # 技术实现: 基于set集合的内存去重和文本文件的持久化存储
    #
    # 执行步骤:
    # 第1步: 添加用户名到内存集合 (sent_users.add)
    # 第2步: 追加用户名到记录文件 (record_file_path)
    #        - 使用UTF-8编码确保中文用户名正确保存
    #        - 每行一个用户名的格式
    # 第3步: 记录操作日志
    # 第4步: 异常处理和错误日志记录
    # ------------------------------------------------------------------------
    async def _save_sent_user(self, username: str) -> bool:
        """保存已发送用户记录"""
        try:
            # 1. 添加到内存集合
            self.sent_users.add(username)

            # 2. 追加到记录文件
            with open(self.record_file_path, 'a', encoding='utf-8') as f:
                f.write(f"{username}\n")

            # 3. 记录操作日志
            log_info(f"[模拟器{self.emulator_id}] 已记录用户: {username}", component="InstagramDMTask")
            return True

        except Exception as e:
            # 4. 异常处理和错误日志记录
            log_error(f"[模拟器{self.emulator_id}] 保存用户记录异常: {e}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 滚动粉丝列表加载更多
    # ------------------------------------------------------------------------
    # 功能描述: 滚动粉丝列表加载更多粉丝，支持智能滚动和备选方案
    # 调用关系: 被_step_business_send_mass_dm调用，当前屏幕粉丝处理完后加载更多
    # 注意事项: 优先使用列表节点滚动，备选使用屏幕滑动确保兼容性
    # 技术实现: 基于scroll_list_enhanced API和swipe备选方案
    #
    # 执行步骤:
    # 第1步: 查找列表节点 (list_view元素)
    # 第2步: 使用scroll_list_enhanced进行智能滚动
    #        - 方向: 向下滚动 ('down')
    #        - 自动计算滚动距离和速度
    # 第3步: 备选方案 - 屏幕滑动
    #        - 起点: (360, 800) 终点: (360, 400)
    #        - 持续时间: 1000毫秒
    # 第4步: 异常处理和日志记录
    # ------------------------------------------------------------------------
    async def _scroll_followers_list(self) -> bool:
        """滚动粉丝列表加载更多"""
        try:
            log_info(f"[模拟器{self.emulator_id}] 滚动粉丝列表", component="InstagramDMTask")

            # 智能滑动：检测容器数量，滑动(容器数-1)个位置
            current_followers = self.ld.find_nodes(resource_id=self.INSTAGRAM_ELEMENTS["follow_list_username"])
            visible_count = len(current_followers) if current_followers else 4  # 默认4个
            scroll_items = max(1, visible_count - 1)  # 至少滑动1个位置

            # 计算滑动距离
            width, height = self.ld.get_screen_size(self.emulator_id)
            item_height = int(height * 0.7) // visible_count  # 简单估算每项高度
            item_height = max(50, min(item_height, 150))  # 限制在合理范围
            scroll_distance = item_height * scroll_items

            # 执行滑动
            center_x = width // 2
            start_y = int(height * 0.75)
            end_y = max(start_y - scroll_distance, int(height * 0.2))

            log_info(f"[模拟器{self.emulator_id}] 滑动{visible_count}个容器中的{scroll_items}个位置，距离{scroll_distance}px", component="InstagramDMTask")
            self.ld.swipe(self.emulator_id, center_x, start_y, center_x, end_y, 500)

            return True

        except Exception as e:
            # 4. 异常处理和日志记录
            log_error(f"[模拟器{self.emulator_id}] 滚动粉丝列表异常: {e}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 检查并点击"查看更多"按钮
    # ------------------------------------------------------------------------
    # 功能描述: 检查并点击Instagram粉丝列表中的"查看更多"按钮
    # 调用关系: 被_step_business_send_mass_dm调用，用于加载更多粉丝内容
    # 注意事项: 按钮可能不存在，需要进行存在性检查
    # 技术实现: 基于文本内容查找和点击操作
    #
    # 执行步骤:
    # 第1步: 查找"查看更多"文本按钮
    # 第2步: 验证按钮是否存在
    # 第3步: 点击按钮并记录操作日志
    # 第4步: 返回操作结果 (True/False)
    # ------------------------------------------------------------------------
    async def _check_and_click_view_more(self) -> bool:
        """检查并点击"查看更多"按钮"""
        try:
            # 1. 查找"查看更多"文本按钮
            view_more_button = self.ld.find_node(text="查看更多")

            # 2. 验证按钮是否存在
            if view_more_button:
                # 3. 点击按钮并记录操作日志
                click_result = self.ld.click_node(view_more_button)
                log_info(f"[模拟器{self.emulator_id}] 点击查看更多按钮", component="InstagramDMTask")
                return click_result

            # 4. 返回操作结果
            return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 检查查看更多按钮异常: {e}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 检查是否到达粉丝列表底部
    # ------------------------------------------------------------------------
    # 功能描述: 检查是否到达Instagram粉丝列表底部，避免无限滚动
    # 调用关系: 被_step_business_send_mass_dm调用，判断是否需要停止滚动
    # 注意事项: 基于Instagram特定的底部提示文本进行判断
    # 技术实现: 查找"为你推荐"文本作为底部标识
    #
    # 执行步骤:
    # 第1步: 查找"为你推荐"文本节点
    # 第2步: 验证节点是否存在
    # 第3步: 记录底部状态日志
    # 第4步: 返回是否到达底部的布尔值
    # ------------------------------------------------------------------------
    async def _check_reached_bottom(self) -> bool:
        """检查是否到达粉丝列表底部"""
        try:
            # 1. 查找"为你推荐"文本节点
            bottom_indicator = self.ld.find_node(text="为你推荐")

            # 2. 验证节点是否存在
            if bottom_indicator:
                # 3. 记录底部状态日志
                log_info(f"[模拟器{self.emulator_id}] 已到达粉丝列表底部", component="InstagramDMTask")
                return True

            # 4. 返回是否到达底部的布尔值
            return False

        except Exception as e:
            log_error(f"[模拟器{self.emulator_id}] 检查底部状态异常: {e}", component="InstagramDMTask")
            return False

    # ------------------------------------------------------------------------
    # 🎯 4.3.5 发送记录管理步骤
    # ------------------------------------------------------------------------
    # 功能描述: 管理已发送用户记录，实现去重和进度跟踪功能
    # 调用关系: 被批量私信发送步骤调用，确保不重复发送给同一用户
    # 注意事项: 需要文件读写、数据去重、异常恢复等功能
    # 技术实现: 基于文件存储和内存缓存的混合方案
    # ------------------------------------------------------------------------

    # ------------------------------------------------------------------------
    # 🎯 4.3.4 消息撤回功能步骤
    # ------------------------------------------------------------------------
    # 功能描述: 在发送私信前执行消息撤回操作（可选功能）
    # 调用关系: 被批量私信发送步骤调用，根据配置决定是否执行
    # 注意事项: 需要检测撤回按钮、处理撤回失败等情况
    # 技术实现: 基于UI元素检测和操作序列
    # ------------------------------------------------------------------------
    # TODO: async def _step_business_recall_messages(self) -> bool:

    # ------------------------------------------------------------------------
    # 🎯 4.4 扩展任务步骤
    # ------------------------------------------------------------------------
    # 功能描述: 其他类型自动化任务的步骤方法扩展区域
    # 调用关系: 根据任务类型被相应的执行流程调用
    # 注意事项: 新增任务类型应遵循现有的架构模式和命名规范
    # 方法格式: async def _step_[task_type]_xxx(self) -> bool:
    # ------------------------------------------------------------------------

    # ------------------------------------------------------------------------
    # 🎯 4.4.1 抖音关注任务步骤
    # ------------------------------------------------------------------------
    # 功能描述: 抖音应用的自动关注功能步骤方法
    # 调用关系: 被抖音关注任务流程调用，实现抖音平台的自动化操作
    # 注意事项: 需要适配抖音应用的UI结构和操作流程
    # 技术实现: 基于抖音应用的UI自动化和状态检测
    # ------------------------------------------------------------------------
    # TODO: async def _step_douyin_launch_app(self) -> bool:
    # TODO: async def _step_douyin_navigate_to_user(self) -> bool:
    # TODO: async def _step_douyin_follow_user(self) -> bool:

    # ------------------------------------------------------------------------
    # 🎯 4.4.2 微博互动任务步骤
    # ------------------------------------------------------------------------
    # 功能描述: 微博应用的自动点赞、评论、转发等互动功能步骤方法
    # 调用关系: 被微博互动任务流程调用，实现微博平台的自动化操作
    # 注意事项: 需要适配微博应用的UI结构和反爬虫机制
    # 技术实现: 基于微博应用的UI自动化和内容识别
    # ------------------------------------------------------------------------
    # TODO: async def _step_weibo_launch_app(self) -> bool:
    # TODO: async def _step_weibo_like_post(self) -> bool:
    # TODO: async def _step_weibo_comment_post(self) -> bool:

    # ------------------------------------------------------------------------
    # 🎯 4.4.3 TikTok任务步骤
    # ------------------------------------------------------------------------
    # 功能描述: TikTok应用的自动评论、关注等功能步骤方法
    # 调用关系: 被TikTok任务流程调用，实现TikTok平台的自动化操作
    # 注意事项: 需要适配TikTok应用的国际化UI和操作流程
    # 技术实现: 基于TikTok应用的UI自动化和多语言支持
    # ------------------------------------------------------------------------
    # TODO: async def _step_tiktok_launch_app(self) -> bool:
    # TODO: async def _step_tiktok_comment_video(self) -> bool:
    # TODO: async def _step_tiktok_follow_user(self) -> bool:

    # ------------------------------------------------------------------------
    # 🎯 4.4.4 通用扩展步骤
    # ------------------------------------------------------------------------
    # 功能描述: 可复用的通用功能步骤方法，支持多平台共享
    # 调用关系: 被各种任务类型调用，提供通用的自动化功能
    # 注意事项: 保持平台无关性，提供统一的接口和错误处理
    # 技术实现: 基于抽象化的UI操作和通用状态管理
    # ------------------------------------------------------------------------
    # TODO: async def _step_common_wait_for_element(self, element_id: str, timeout: int) -> bool:
    # TODO: async def _step_common_take_screenshot(self, filename: str) -> bool:
    # TODO: async def _step_common_input_text(self, element_id: str, text: str) -> bool:

    # ========================================================================
    # 🎯 Instagram通用方法使用示例
    # ========================================================================
    #
    # 1. 通用返回导航使用示例:
    #    ```python
    #    # 在任何需要返回的地方调用
    #    if not await self._navigate_back_instagram():
    #        log_error("返回操作失败")
    #        return False
    #    ```
    #
    # 2. Instagram元素ID常量使用示例:
    #    ```python
    #    # 查找粉丝数量
    #    followers_node = self.ld.find_node(
    #        resource_id=self.INSTAGRAM_ELEMENTS["followers_count"]
    #    )
    #
    #    # 查找私信输入框
    #    input_box = self.ld.find_node(
    #        resource_id=self.INSTAGRAM_ELEMENTS["message_input"]
    #    )
    #
    #    # 点击返回按钮
    #    back_btn = self.ld.find_node(
    #        resource_id=self.INSTAGRAM_ELEMENTS["back_button"]
    #    )
    #    ```
    #
    # 3. 可复用的Instagram操作模式:
    #    - 所有Instagram页面导航都应使用 _navigate_back_instagram()
    #    - 所有元素查找都应使用 INSTAGRAM_ELEMENTS 常量
    #    - 保持一致的错误处理和日志记录模式
    # ========================================================================
