# -*- coding: utf-8 -*-
"""
🧪 打包测试脚本
功能：验证打包后的程序是否包含所有必要的模块和功能
"""

import sys
import importlib
from pathlib import Path

def test_core_imports():
    """测试核心模块导入"""
    print("🔍 测试核心模块导入...")
    
    core_modules = [
        "PyQt6.QtCore",
        "PyQt6.QtGui", 
        "PyQt6.QtWidgets",
        "requests",
        "psutil",
        "sqlite3",
        "asyncio",
        "threading",
        "subprocess",
        "json",
        "xml.etree.ElementTree",
    ]
    
    failed_imports = []
    
    for module in core_modules:
        try:
            importlib.import_module(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            print(f"  ❌ {module}: {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0, failed_imports

def test_project_modules():
    """测试项目模块导入"""
    print("\n🔍 测试项目模块导入...")
    
    # 添加项目根目录到路径
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    project_modules = [
        "core.async_bridge",
        "core.unified_emulator_manager", 
        "core.instagram_task",
        "core.instagram_follow_task",
        "core.logger_manager",
        "core.simple_config",
        "core.heartbeat_manager",
        "core.screenshot_manager",
        "ui.main_window_v2",
        "ui.instagram_dm_ui",
        "ui.instagram_follow_ui",
        "data.database_manager",
    ]
    
    failed_imports = []
    
    for module in project_modules:
        try:
            importlib.import_module(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            print(f"  ❌ {module}: {e}")
            failed_imports.append(module)
        except Exception as e:
            print(f"  ⚠️  {module}: {e} (模块存在但初始化有问题)")
    
    return len(failed_imports) == 0, failed_imports

def test_data_files():
    """测试数据文件是否存在"""
    print("\n🔍 测试数据文件...")
    
    required_files = [
        "img",
        "config", 
        "data",
        "logs",
        "screenshots",
        "app_config.json",
        "main.py",
    ]
    
    missing_files = []
    project_root = Path(__file__).parent
    
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} (缺失)")
            missing_files.append(file_path)
    
    return len(missing_files) == 0, missing_files

def test_main_functionality():
    """测试主程序功能"""
    print("\n🔍 测试主程序功能...")
    
    try:
        # 测试主程序导入
        import main
        print("  ✅ main.py 导入成功")
        
        # 测试核心组件创建
        from core.simple_config import get_config_manager
        config_manager = get_config_manager()
        print("  ✅ 配置管理器创建成功")
        
        from core.async_bridge import get_async_bridge
        bridge = get_async_bridge()
        print("  ✅ 异步桥梁创建成功")
        
        # 测试UI组件创建（不显示窗口）
        from PyQt6.QtWidgets import QApplication
        app = QApplication([])
        
        from ui.main_window_v2 import MainWindowV2
        main_window = MainWindowV2(config_manager=config_manager)
        print("  ✅ 主窗口创建成功")
        
        app.quit()
        return True, []
        
    except Exception as e:
        print(f"  ❌ 主程序功能测试失败: {e}")
        return False, [str(e)]

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始打包测试...")
    print("=" * 50)
    
    all_passed = True
    all_errors = []
    
    # 测试核心导入
    passed, errors = test_core_imports()
    if not passed:
        all_passed = False
        all_errors.extend(errors)
    
    # 测试项目模块
    passed, errors = test_project_modules()
    if not passed:
        all_passed = False
        all_errors.extend(errors)
    
    # 测试数据文件
    passed, errors = test_data_files()
    if not passed:
        all_passed = False
        all_errors.extend(errors)
    
    # 测试主程序功能
    passed, errors = test_main_functionality()
    if not passed:
        all_passed = False
        all_errors.extend(errors)
    
    # 输出结果
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！程序可以正常打包")
        print("💡 建议：现在可以运行 build.bat 进行打包")
    else:
        print("❌ 测试失败，需要解决以下问题：")
        for error in all_errors:
            print(f"  - {error}")
        print("\n💡 建议：解决上述问题后再进行打包")
    
    return all_passed

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
