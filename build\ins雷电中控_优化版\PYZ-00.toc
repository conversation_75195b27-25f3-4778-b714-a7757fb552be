('e:\\VScodexiangmu\\insleidian2\\build\\ins雷电中控_优化版\\PYZ-00.pyz',
 [('PIL',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE-2'),
  ('PIL.AvifImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BlpImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BmpImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BufrStubImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.CurImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.DcxImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.DdsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.EpsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ExifTags',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE-2'),
  ('PIL.FitsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FliImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FpxImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FtexImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GbrImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GifImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GimpGradientFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE-2'),
  ('PIL.GimpPaletteFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE-2'),
  ('PIL.GribStubImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Hdf5StubImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IcnsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IcoImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Image',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE-2'),
  ('PIL.ImageChops',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE-2'),
  ('PIL.ImageCms',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE-2'),
  ('PIL.ImageColor',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE-2'),
  ('PIL.ImageFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE-2'),
  ('PIL.ImageFilter',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE-2'),
  ('PIL.ImageGrab',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE-2'),
  ('PIL.ImageMath',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE-2'),
  ('PIL.ImageMode',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE-2'),
  ('PIL.ImageOps',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE-2'),
  ('PIL.ImagePalette',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE-2'),
  ('PIL.ImageQt',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE-2'),
  ('PIL.ImageSequence',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE-2'),
  ('PIL.ImageShow',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE-2'),
  ('PIL.ImageTk',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE-2'),
  ('PIL.ImageWin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE-2'),
  ('PIL.ImtImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IptcImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Jpeg2KImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.JpegImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.JpegPresets',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE-2'),
  ('PIL.McIdasImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MicImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MpegImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MpoImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MspImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PaletteFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE-2'),
  ('PIL.PalmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PcdImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PcxImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PdfImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PdfParser',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE-2'),
  ('PIL.PixarImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PngImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PpmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PsdImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.QoiImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SgiImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SpiderImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SunImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TgaImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TiffImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TiffTags',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE-2'),
  ('PIL.WebPImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.WmfImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XVThumbImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XbmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XpmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL._binary',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE-2'),
  ('PIL._deprecate',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE-2'),
  ('PIL._typing',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE-2'),
  ('PIL._util',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE-2'),
  ('PIL._version',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE-2'),
  ('PIL.features',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE-2'),
  ('PyQt6',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE-2'),
  ('__future__', 'E:\\python-3.12.9\\Lib\\__future__.py', 'PYMODULE-2'),
  ('_aix_support', 'E:\\python-3.12.9\\Lib\\_aix_support.py', 'PYMODULE-2'),
  ('_compat_pickle', 'E:\\python-3.12.9\\Lib\\_compat_pickle.py', 'PYMODULE-2'),
  ('_compression', 'E:\\python-3.12.9\\Lib\\_compression.py', 'PYMODULE-2'),
  ('_py_abc', 'E:\\python-3.12.9\\Lib\\_py_abc.py', 'PYMODULE-2'),
  ('_pydatetime', 'E:\\python-3.12.9\\Lib\\_pydatetime.py', 'PYMODULE-2'),
  ('_pydecimal', 'E:\\python-3.12.9\\Lib\\_pydecimal.py', 'PYMODULE-2'),
  ('_pyi_rth_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE-2'),
  ('_pyi_rth_utils.qt',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE-2'),
  ('_strptime', 'E:\\python-3.12.9\\Lib\\_strptime.py', 'PYMODULE-2'),
  ('_threading_local',
   'E:\\python-3.12.9\\Lib\\_threading_local.py',
   'PYMODULE-2'),
  ('aiofiles',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\__init__.py',
   'PYMODULE-2'),
  ('aiofiles.base',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\base.py',
   'PYMODULE-2'),
  ('aiofiles.tempfile',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\tempfile\\__init__.py',
   'PYMODULE-2'),
  ('aiofiles.tempfile.temptypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\tempfile\\temptypes.py',
   'PYMODULE-2'),
  ('aiofiles.threadpool',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\__init__.py',
   'PYMODULE-2'),
  ('aiofiles.threadpool.binary',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\binary.py',
   'PYMODULE-2'),
  ('aiofiles.threadpool.text',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\text.py',
   'PYMODULE-2'),
  ('aiofiles.threadpool.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\utils.py',
   'PYMODULE-2'),
  ('argparse', 'E:\\python-3.12.9\\Lib\\argparse.py', 'PYMODULE-2'),
  ('ast', 'E:\\python-3.12.9\\Lib\\ast.py', 'PYMODULE-2'),
  ('asyncio', 'E:\\python-3.12.9\\Lib\\asyncio\\__init__.py', 'PYMODULE-2'),
  ('asyncio.base_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_events.py',
   'PYMODULE-2'),
  ('asyncio.base_futures',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_futures.py',
   'PYMODULE-2'),
  ('asyncio.base_subprocess',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE-2'),
  ('asyncio.base_tasks',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE-2'),
  ('asyncio.constants',
   'E:\\python-3.12.9\\Lib\\asyncio\\constants.py',
   'PYMODULE-2'),
  ('asyncio.coroutines',
   'E:\\python-3.12.9\\Lib\\asyncio\\coroutines.py',
   'PYMODULE-2'),
  ('asyncio.events',
   'E:\\python-3.12.9\\Lib\\asyncio\\events.py',
   'PYMODULE-2'),
  ('asyncio.exceptions',
   'E:\\python-3.12.9\\Lib\\asyncio\\exceptions.py',
   'PYMODULE-2'),
  ('asyncio.format_helpers',
   'E:\\python-3.12.9\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE-2'),
  ('asyncio.futures',
   'E:\\python-3.12.9\\Lib\\asyncio\\futures.py',
   'PYMODULE-2'),
  ('asyncio.locks', 'E:\\python-3.12.9\\Lib\\asyncio\\locks.py', 'PYMODULE-2'),
  ('asyncio.log', 'E:\\python-3.12.9\\Lib\\asyncio\\log.py', 'PYMODULE-2'),
  ('asyncio.mixins',
   'E:\\python-3.12.9\\Lib\\asyncio\\mixins.py',
   'PYMODULE-2'),
  ('asyncio.proactor_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE-2'),
  ('asyncio.protocols',
   'E:\\python-3.12.9\\Lib\\asyncio\\protocols.py',
   'PYMODULE-2'),
  ('asyncio.queues',
   'E:\\python-3.12.9\\Lib\\asyncio\\queues.py',
   'PYMODULE-2'),
  ('asyncio.runners',
   'E:\\python-3.12.9\\Lib\\asyncio\\runners.py',
   'PYMODULE-2'),
  ('asyncio.selector_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\selector_events.py',
   'PYMODULE-2'),
  ('asyncio.sslproto',
   'E:\\python-3.12.9\\Lib\\asyncio\\sslproto.py',
   'PYMODULE-2'),
  ('asyncio.staggered',
   'E:\\python-3.12.9\\Lib\\asyncio\\staggered.py',
   'PYMODULE-2'),
  ('asyncio.streams',
   'E:\\python-3.12.9\\Lib\\asyncio\\streams.py',
   'PYMODULE-2'),
  ('asyncio.subprocess',
   'E:\\python-3.12.9\\Lib\\asyncio\\subprocess.py',
   'PYMODULE-2'),
  ('asyncio.taskgroups',
   'E:\\python-3.12.9\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE-2'),
  ('asyncio.tasks', 'E:\\python-3.12.9\\Lib\\asyncio\\tasks.py', 'PYMODULE-2'),
  ('asyncio.threads',
   'E:\\python-3.12.9\\Lib\\asyncio\\threads.py',
   'PYMODULE-2'),
  ('asyncio.timeouts',
   'E:\\python-3.12.9\\Lib\\asyncio\\timeouts.py',
   'PYMODULE-2'),
  ('asyncio.transports',
   'E:\\python-3.12.9\\Lib\\asyncio\\transports.py',
   'PYMODULE-2'),
  ('asyncio.trsock',
   'E:\\python-3.12.9\\Lib\\asyncio\\trsock.py',
   'PYMODULE-2'),
  ('asyncio.unix_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\unix_events.py',
   'PYMODULE-2'),
  ('asyncio.windows_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\windows_events.py',
   'PYMODULE-2'),
  ('asyncio.windows_utils',
   'E:\\python-3.12.9\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE-2'),
  ('base64', 'E:\\python-3.12.9\\Lib\\base64.py', 'PYMODULE-2'),
  ('bisect', 'E:\\python-3.12.9\\Lib\\bisect.py', 'PYMODULE-2'),
  ('bz2', 'E:\\python-3.12.9\\Lib\\bz2.py', 'PYMODULE-2'),
  ('calendar', 'E:\\python-3.12.9\\Lib\\calendar.py', 'PYMODULE-2'),
  ('certifi',
   'E:\\python-3.12.9\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE-2'),
  ('certifi.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE-2'),
  ('charset_normalizer',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE-2'),
  ('charset_normalizer.api',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE-2'),
  ('charset_normalizer.cd',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE-2'),
  ('charset_normalizer.constant',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE-2'),
  ('charset_normalizer.legacy',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE-2'),
  ('charset_normalizer.models',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE-2'),
  ('charset_normalizer.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE-2'),
  ('charset_normalizer.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE-2'),
  ('code', 'E:\\python-3.12.9\\Lib\\code.py', 'PYMODULE-2'),
  ('codeop', 'E:\\python-3.12.9\\Lib\\codeop.py', 'PYMODULE-2'),
  ('colorsys', 'E:\\python-3.12.9\\Lib\\colorsys.py', 'PYMODULE-2'),
  ('concurrent',
   'E:\\python-3.12.9\\Lib\\concurrent\\__init__.py',
   'PYMODULE-2'),
  ('concurrent.futures',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE-2'),
  ('concurrent.futures._base',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE-2'),
  ('concurrent.futures.process',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE-2'),
  ('concurrent.futures.thread',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE-2'),
  ('contextlib', 'E:\\python-3.12.9\\Lib\\contextlib.py', 'PYMODULE-2'),
  ('contextvars', 'E:\\python-3.12.9\\Lib\\contextvars.py', 'PYMODULE-2'),
  ('copy', 'E:\\python-3.12.9\\Lib\\copy.py', 'PYMODULE-2'),
  ('core', '-', 'PYMODULE-2'),
  ('core.async_bridge',
   'e:\\VScodexiangmu\\insleidian2\\core\\async_bridge.py',
   'PYMODULE-2'),
  ('core.config_hot_reload',
   'e:\\VScodexiangmu\\insleidian2\\core\\config_hot_reload.py',
   'PYMODULE-2'),
  ('core.heartbeat_manager',
   'e:\\VScodexiangmu\\insleidian2\\core\\heartbeat_manager.py',
   'PYMODULE-2'),
  ('core.instagram_follow_task',
   'e:\\VScodexiangmu\\insleidian2\\core\\instagram_follow_task.py',
   'PYMODULE-2'),
  ('core.instagram_task',
   'e:\\VScodexiangmu\\insleidian2\\core\\instagram_task.py',
   'PYMODULE-2'),
  ('core.leidianapi', '-', 'PYMODULE-2'),
  ('core.leidianapi.LeiDian_Reorganized',
   'e:\\VScodexiangmu\\insleidian2\\core\\leidianapi\\LeiDian_Reorganized.py',
   'PYMODULE-2'),
  ('core.leidianapi.雷电一键找图',
   'e:\\VScodexiangmu\\insleidian2\\core\\leidianapi\\雷电一键找图.py',
   'PYMODULE-2'),
  ('core.logger_manager',
   'e:\\VScodexiangmu\\insleidian2\\core\\logger_manager.py',
   'PYMODULE-2'),
  ('core.native',
   'e:\\VScodexiangmu\\insleidian2\\core\\native\\__init__.py',
   'PYMODULE-2'),
  ('core.native.base_api',
   'e:\\VScodexiangmu\\insleidian2\\core\\native\\base_api.py',
   'PYMODULE-2'),
  ('core.native.image_recognition_engine',
   'e:\\VScodexiangmu\\insleidian2\\core\\native\\image_recognition_engine.py',
   'PYMODULE-2'),
  ('core.native.screenshot_engine',
   'e:\\VScodexiangmu\\insleidian2\\core\\native\\screenshot_engine.py',
   'PYMODULE-2'),
  ('core.screenshot_manager',
   'e:\\VScodexiangmu\\insleidian2\\core\\screenshot_manager.py',
   'PYMODULE-2'),
  ('core.simple_config',
   'e:\\VScodexiangmu\\insleidian2\\core\\simple_config.py',
   'PYMODULE-2'),
  ('core.status_converter',
   'e:\\VScodexiangmu\\insleidian2\\core\\status_converter.py',
   'PYMODULE-2'),
  ('core.unified_emulator_manager',
   'e:\\VScodexiangmu\\insleidian2\\core\\unified_emulator_manager.py',
   'PYMODULE-2'),
  ('core.window_arrangement_manager',
   'e:\\VScodexiangmu\\insleidian2\\core\\window_arrangement_manager.py',
   'PYMODULE-2'),
  ('csv', 'E:\\python-3.12.9\\Lib\\csv.py', 'PYMODULE-2'),
  ('ctypes', 'E:\\python-3.12.9\\Lib\\ctypes\\__init__.py', 'PYMODULE-2'),
  ('ctypes._endian',
   'E:\\python-3.12.9\\Lib\\ctypes\\_endian.py',
   'PYMODULE-2'),
  ('ctypes.wintypes',
   'E:\\python-3.12.9\\Lib\\ctypes\\wintypes.py',
   'PYMODULE-2'),
  ('data', 'e:\\VScodexiangmu\\insleidian2\\data\\__init__.py', 'PYMODULE-2'),
  ('data.database_manager',
   'e:\\VScodexiangmu\\insleidian2\\data\\database_manager.py',
   'PYMODULE-2'),
  ('data.models',
   'e:\\VScodexiangmu\\insleidian2\\data\\models\\__init__.py',
   'PYMODULE-2'),
  ('data.models.emulator_model',
   'e:\\VScodexiangmu\\insleidian2\\data\\models\\emulator_model.py',
   'PYMODULE-2'),
  ('data.repositories',
   'e:\\VScodexiangmu\\insleidian2\\data\\repositories\\__init__.py',
   'PYMODULE-2'),
  ('data.repositories.emulator_repository',
   'e:\\VScodexiangmu\\insleidian2\\data\\repositories\\emulator_repository.py',
   'PYMODULE-2'),
  ('dataclasses', 'E:\\python-3.12.9\\Lib\\dataclasses.py', 'PYMODULE-2'),
  ('datetime', 'E:\\python-3.12.9\\Lib\\datetime.py', 'PYMODULE-2'),
  ('decimal', 'E:\\python-3.12.9\\Lib\\decimal.py', 'PYMODULE-2'),
  ('dis', 'E:\\python-3.12.9\\Lib\\dis.py', 'PYMODULE-2'),
  ('email', 'E:\\python-3.12.9\\Lib\\email\\__init__.py', 'PYMODULE-2'),
  ('email._encoded_words',
   'E:\\python-3.12.9\\Lib\\email\\_encoded_words.py',
   'PYMODULE-2'),
  ('email._header_value_parser',
   'E:\\python-3.12.9\\Lib\\email\\_header_value_parser.py',
   'PYMODULE-2'),
  ('email._parseaddr',
   'E:\\python-3.12.9\\Lib\\email\\_parseaddr.py',
   'PYMODULE-2'),
  ('email._policybase',
   'E:\\python-3.12.9\\Lib\\email\\_policybase.py',
   'PYMODULE-2'),
  ('email.base64mime',
   'E:\\python-3.12.9\\Lib\\email\\base64mime.py',
   'PYMODULE-2'),
  ('email.charset', 'E:\\python-3.12.9\\Lib\\email\\charset.py', 'PYMODULE-2'),
  ('email.contentmanager',
   'E:\\python-3.12.9\\Lib\\email\\contentmanager.py',
   'PYMODULE-2'),
  ('email.encoders',
   'E:\\python-3.12.9\\Lib\\email\\encoders.py',
   'PYMODULE-2'),
  ('email.errors', 'E:\\python-3.12.9\\Lib\\email\\errors.py', 'PYMODULE-2'),
  ('email.feedparser',
   'E:\\python-3.12.9\\Lib\\email\\feedparser.py',
   'PYMODULE-2'),
  ('email.generator',
   'E:\\python-3.12.9\\Lib\\email\\generator.py',
   'PYMODULE-2'),
  ('email.header', 'E:\\python-3.12.9\\Lib\\email\\header.py', 'PYMODULE-2'),
  ('email.headerregistry',
   'E:\\python-3.12.9\\Lib\\email\\headerregistry.py',
   'PYMODULE-2'),
  ('email.iterators',
   'E:\\python-3.12.9\\Lib\\email\\iterators.py',
   'PYMODULE-2'),
  ('email.message', 'E:\\python-3.12.9\\Lib\\email\\message.py', 'PYMODULE-2'),
  ('email.parser', 'E:\\python-3.12.9\\Lib\\email\\parser.py', 'PYMODULE-2'),
  ('email.policy', 'E:\\python-3.12.9\\Lib\\email\\policy.py', 'PYMODULE-2'),
  ('email.quoprimime',
   'E:\\python-3.12.9\\Lib\\email\\quoprimime.py',
   'PYMODULE-2'),
  ('email.utils', 'E:\\python-3.12.9\\Lib\\email\\utils.py', 'PYMODULE-2'),
  ('fnmatch', 'E:\\python-3.12.9\\Lib\\fnmatch.py', 'PYMODULE-2'),
  ('fractions', 'E:\\python-3.12.9\\Lib\\fractions.py', 'PYMODULE-2'),
  ('ftplib', 'E:\\python-3.12.9\\Lib\\ftplib.py', 'PYMODULE-2'),
  ('getopt', 'E:\\python-3.12.9\\Lib\\getopt.py', 'PYMODULE-2'),
  ('getpass', 'E:\\python-3.12.9\\Lib\\getpass.py', 'PYMODULE-2'),
  ('gettext', 'E:\\python-3.12.9\\Lib\\gettext.py', 'PYMODULE-2'),
  ('gzip', 'E:\\python-3.12.9\\Lib\\gzip.py', 'PYMODULE-2'),
  ('hashlib', 'E:\\python-3.12.9\\Lib\\hashlib.py', 'PYMODULE-2'),
  ('hmac', 'E:\\python-3.12.9\\Lib\\hmac.py', 'PYMODULE-2'),
  ('html', 'E:\\python-3.12.9\\Lib\\html\\__init__.py', 'PYMODULE-2'),
  ('html.entities', 'E:\\python-3.12.9\\Lib\\html\\entities.py', 'PYMODULE-2'),
  ('http', 'E:\\python-3.12.9\\Lib\\http\\__init__.py', 'PYMODULE-2'),
  ('http.client', 'E:\\python-3.12.9\\Lib\\http\\client.py', 'PYMODULE-2'),
  ('http.cookiejar',
   'E:\\python-3.12.9\\Lib\\http\\cookiejar.py',
   'PYMODULE-2'),
  ('http.cookies', 'E:\\python-3.12.9\\Lib\\http\\cookies.py', 'PYMODULE-2'),
  ('http.server', 'E:\\python-3.12.9\\Lib\\http\\server.py', 'PYMODULE-2'),
  ('idna',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE-2'),
  ('idna.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE-2'),
  ('idna.idnadata',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE-2'),
  ('idna.intranges',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE-2'),
  ('idna.package_data',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE-2'),
  ('idna.uts46data',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE-2'),
  ('importlib', 'E:\\python-3.12.9\\Lib\\importlib\\__init__.py', 'PYMODULE-2'),
  ('importlib._abc',
   'E:\\python-3.12.9\\Lib\\importlib\\_abc.py',
   'PYMODULE-2'),
  ('importlib._bootstrap',
   'E:\\python-3.12.9\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE-2'),
  ('importlib._bootstrap_external',
   'E:\\python-3.12.9\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE-2'),
  ('importlib.abc', 'E:\\python-3.12.9\\Lib\\importlib\\abc.py', 'PYMODULE-2'),
  ('importlib.machinery',
   'E:\\python-3.12.9\\Lib\\importlib\\machinery.py',
   'PYMODULE-2'),
  ('importlib.metadata',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE-2'),
  ('importlib.metadata._adapters',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.metadata._collections',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE-2'),
  ('importlib.metadata._functools',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE-2'),
  ('importlib.metadata._itertools',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.metadata._meta',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE-2'),
  ('importlib.metadata._text',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE-2'),
  ('importlib.readers',
   'E:\\python-3.12.9\\Lib\\importlib\\readers.py',
   'PYMODULE-2'),
  ('importlib.resources',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE-2'),
  ('importlib.resources._adapters',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.resources._common',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE-2'),
  ('importlib.resources._itertools',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.resources._legacy',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE-2'),
  ('importlib.resources.abc',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE-2'),
  ('importlib.resources.readers',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE-2'),
  ('importlib.util',
   'E:\\python-3.12.9\\Lib\\importlib\\util.py',
   'PYMODULE-2'),
  ('inspect', 'E:\\python-3.12.9\\Lib\\inspect.py', 'PYMODULE-2'),
  ('ipaddress', 'E:\\python-3.12.9\\Lib\\ipaddress.py', 'PYMODULE-2'),
  ('json', 'E:\\python-3.12.9\\Lib\\json\\__init__.py', 'PYMODULE-2'),
  ('json.decoder', 'E:\\python-3.12.9\\Lib\\json\\decoder.py', 'PYMODULE-2'),
  ('json.encoder', 'E:\\python-3.12.9\\Lib\\json\\encoder.py', 'PYMODULE-2'),
  ('json.scanner', 'E:\\python-3.12.9\\Lib\\json\\scanner.py', 'PYMODULE-2'),
  ('logging', 'E:\\python-3.12.9\\Lib\\logging\\__init__.py', 'PYMODULE-2'),
  ('lzma', 'E:\\python-3.12.9\\Lib\\lzma.py', 'PYMODULE-2'),
  ('mimetypes', 'E:\\python-3.12.9\\Lib\\mimetypes.py', 'PYMODULE-2'),
  ('multiprocessing',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing.connection',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\connection.py',
   'PYMODULE-2'),
  ('multiprocessing.context',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\context.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy.connection',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE-2'),
  ('multiprocessing.forkserver',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.heap',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\heap.py',
   'PYMODULE-2'),
  ('multiprocessing.managers',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\managers.py',
   'PYMODULE-2'),
  ('multiprocessing.pool',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\pool.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_fork',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_forkserver',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_posix',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_win32',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE-2'),
  ('multiprocessing.process',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\process.py',
   'PYMODULE-2'),
  ('multiprocessing.queues',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\queues.py',
   'PYMODULE-2'),
  ('multiprocessing.reduction',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_sharer',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_tracker',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE-2'),
  ('multiprocessing.shared_memory',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE-2'),
  ('multiprocessing.sharedctypes',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE-2'),
  ('multiprocessing.spawn',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE-2'),
  ('multiprocessing.synchronize',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE-2'),
  ('multiprocessing.util',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\util.py',
   'PYMODULE-2'),
  ('netrc', 'E:\\python-3.12.9\\Lib\\netrc.py', 'PYMODULE-2'),
  ('nturl2path', 'E:\\python-3.12.9\\Lib\\nturl2path.py', 'PYMODULE-2'),
  ('numbers', 'E:\\python-3.12.9\\Lib\\numbers.py', 'PYMODULE-2'),
  ('numpy',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE-2'),
  ('numpy.__config__',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE-2'),
  ('numpy._array_api_info',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE-2'),
  ('numpy._core',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs_scalars',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE-2'),
  ('numpy._core._asarray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE-2'),
  ('numpy._core._dtype',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE-2'),
  ('numpy._core._dtype_ctypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE-2'),
  ('numpy._core._exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE-2'),
  ('numpy._core._internal',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE-2'),
  ('numpy._core._machar',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE-2'),
  ('numpy._core._methods',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE-2'),
  ('numpy._core._string_helpers',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE-2'),
  ('numpy._core._type_aliases',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE-2'),
  ('numpy._core._ufunc_config',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE-2'),
  ('numpy._core.arrayprint',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE-2'),
  ('numpy._core.defchararray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE-2'),
  ('numpy._core.einsumfunc',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE-2'),
  ('numpy._core.fromnumeric',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE-2'),
  ('numpy._core.function_base',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE-2'),
  ('numpy._core.getlimits',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE-2'),
  ('numpy._core.memmap',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE-2'),
  ('numpy._core.multiarray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE-2'),
  ('numpy._core.numeric',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE-2'),
  ('numpy._core.numerictypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE-2'),
  ('numpy._core.overrides',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE-2'),
  ('numpy._core.printoptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE-2'),
  ('numpy._core.records',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE-2'),
  ('numpy._core.shape_base',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE-2'),
  ('numpy._core.strings',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE-2'),
  ('numpy._core.umath',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE-2'),
  ('numpy._distributor_init',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE-2'),
  ('numpy._expired_attrs_2_0',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE-2'),
  ('numpy._globals',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE-2'),
  ('numpy._pytesttester',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE-2'),
  ('numpy._typing',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy._typing._add_docstring',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE-2'),
  ('numpy._typing._array_like',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE-2'),
  ('numpy._typing._char_codes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE-2'),
  ('numpy._typing._dtype_like',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit_base',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE-2'),
  ('numpy._typing._nested_sequence',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE-2'),
  ('numpy._typing._scalars',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE-2'),
  ('numpy._typing._shape',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE-2'),
  ('numpy._typing._ufunc',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE-2'),
  ('numpy._utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE-2'),
  ('numpy._utils._convertions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE-2'),
  ('numpy._utils._inspect',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE-2'),
  ('numpy.char',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE-2'),
  ('numpy.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE-2'),
  ('numpy.core._utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE-2'),
  ('numpy.core.multiarray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE-2'),
  ('numpy.ctypeslib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE-2'),
  ('numpy.dtypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE-2'),
  ('numpy.exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE-2'),
  ('numpy.fft',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE-2'),
  ('numpy.fft._helper',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE-2'),
  ('numpy.fft._pocketfft',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE-2'),
  ('numpy.fft.helper',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE-2'),
  ('numpy.lib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.lib._array_utils_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arraypad_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arraysetops_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arrayterator_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._datasource',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE-2'),
  ('numpy.lib._function_base_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._histograms_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._index_tricks_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._iotools',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE-2'),
  ('numpy.lib._nanfunctions_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._npyio_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._polynomial_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._scimath_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._shape_base_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._stride_tricks_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._twodim_base_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._type_check_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._ufunclike_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._utils_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._version',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE-2'),
  ('numpy.lib.array_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE-2'),
  ('numpy.lib.format',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE-2'),
  ('numpy.lib.introspect',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE-2'),
  ('numpy.lib.mixins',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE-2'),
  ('numpy.lib.npyio',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE-2'),
  ('numpy.lib.scimath',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE-2'),
  ('numpy.lib.stride_tricks',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE-2'),
  ('numpy.linalg',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE-2'),
  ('numpy.linalg._linalg',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE-2'),
  ('numpy.linalg.linalg',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE-2'),
  ('numpy.ma',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE-2'),
  ('numpy.ma.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE-2'),
  ('numpy.ma.extras',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE-2'),
  ('numpy.ma.mrecords',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE-2'),
  ('numpy.matlib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE-2'),
  ('numpy.matrixlib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.matrixlib.defmatrix',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE-2'),
  ('numpy.polynomial',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE-2'),
  ('numpy.polynomial._polybase',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE-2'),
  ('numpy.polynomial.chebyshev',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite_e',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE-2'),
  ('numpy.polynomial.laguerre',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.legendre',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polynomial',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polyutils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE-2'),
  ('numpy.random',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE-2'),
  ('numpy.random._pickle',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE-2'),
  ('numpy.rec',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE-2'),
  ('numpy.strings',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE-2'),
  ('numpy.typing',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE-2'),
  ('opcode', 'E:\\python-3.12.9\\Lib\\opcode.py', 'PYMODULE-2'),
  ('pathlib', 'E:\\python-3.12.9\\Lib\\pathlib.py', 'PYMODULE-2'),
  ('pickle', 'E:\\python-3.12.9\\Lib\\pickle.py', 'PYMODULE-2'),
  ('pkgutil', 'E:\\python-3.12.9\\Lib\\pkgutil.py', 'PYMODULE-2'),
  ('platform', 'E:\\python-3.12.9\\Lib\\platform.py', 'PYMODULE-2'),
  ('pprint', 'E:\\python-3.12.9\\Lib\\pprint.py', 'PYMODULE-2'),
  ('psutil',
   'E:\\python-3.12.9\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE-2'),
  ('psutil._common',
   'E:\\python-3.12.9\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE-2'),
  ('psutil._pswindows',
   'E:\\python-3.12.9\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE-2'),
  ('py_compile', 'E:\\python-3.12.9\\Lib\\py_compile.py', 'PYMODULE-2'),
  ('pydoc', 'E:\\python-3.12.9\\Lib\\pydoc.py', 'PYMODULE-2'),
  ('pydoc_data',
   'E:\\python-3.12.9\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE-2'),
  ('pydoc_data.topics',
   'E:\\python-3.12.9\\Lib\\pydoc_data\\topics.py',
   'PYMODULE-2'),
  ('queue', 'E:\\python-3.12.9\\Lib\\queue.py', 'PYMODULE-2'),
  ('quopri', 'E:\\python-3.12.9\\Lib\\quopri.py', 'PYMODULE-2'),
  ('random', 'E:\\python-3.12.9\\Lib\\random.py', 'PYMODULE-2'),
  ('requests',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE-2'),
  ('requests.__version__',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE-2'),
  ('requests._internal_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE-2'),
  ('requests.adapters',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE-2'),
  ('requests.api',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE-2'),
  ('requests.auth',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE-2'),
  ('requests.certs',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE-2'),
  ('requests.compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE-2'),
  ('requests.cookies',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE-2'),
  ('requests.exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE-2'),
  ('requests.hooks',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE-2'),
  ('requests.models',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE-2'),
  ('requests.packages',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE-2'),
  ('requests.sessions',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE-2'),
  ('requests.status_codes',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE-2'),
  ('requests.structures',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE-2'),
  ('requests.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE-2'),
  ('runpy', 'E:\\python-3.12.9\\Lib\\runpy.py', 'PYMODULE-2'),
  ('secrets', 'E:\\python-3.12.9\\Lib\\secrets.py', 'PYMODULE-2'),
  ('selectors', 'E:\\python-3.12.9\\Lib\\selectors.py', 'PYMODULE-2'),
  ('shlex', 'E:\\python-3.12.9\\Lib\\shlex.py', 'PYMODULE-2'),
  ('shutil', 'E:\\python-3.12.9\\Lib\\shutil.py', 'PYMODULE-2'),
  ('signal', 'E:\\python-3.12.9\\Lib\\signal.py', 'PYMODULE-2'),
  ('socket', 'E:\\python-3.12.9\\Lib\\socket.py', 'PYMODULE-2'),
  ('socketserver', 'E:\\python-3.12.9\\Lib\\socketserver.py', 'PYMODULE-2'),
  ('socks', 'E:\\python-3.12.9\\Lib\\site-packages\\socks.py', 'PYMODULE-2'),
  ('sqlite3', 'E:\\python-3.12.9\\Lib\\sqlite3\\__init__.py', 'PYMODULE-2'),
  ('sqlite3.__main__',
   'E:\\python-3.12.9\\Lib\\sqlite3\\__main__.py',
   'PYMODULE-2'),
  ('sqlite3.dbapi2',
   'E:\\python-3.12.9\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE-2'),
  ('sqlite3.dump', 'E:\\python-3.12.9\\Lib\\sqlite3\\dump.py', 'PYMODULE-2'),
  ('ssl', 'E:\\python-3.12.9\\Lib\\ssl.py', 'PYMODULE-2'),
  ('statistics', 'E:\\python-3.12.9\\Lib\\statistics.py', 'PYMODULE-2'),
  ('string', 'E:\\python-3.12.9\\Lib\\string.py', 'PYMODULE-2'),
  ('stringprep', 'E:\\python-3.12.9\\Lib\\stringprep.py', 'PYMODULE-2'),
  ('subprocess', 'E:\\python-3.12.9\\Lib\\subprocess.py', 'PYMODULE-2'),
  ('sysconfig', 'E:\\python-3.12.9\\Lib\\sysconfig.py', 'PYMODULE-2'),
  ('tarfile', 'E:\\python-3.12.9\\Lib\\tarfile.py', 'PYMODULE-2'),
  ('tempfile', 'E:\\python-3.12.9\\Lib\\tempfile.py', 'PYMODULE-2'),
  ('textwrap', 'E:\\python-3.12.9\\Lib\\textwrap.py', 'PYMODULE-2'),
  ('threading', 'E:\\python-3.12.9\\Lib\\threading.py', 'PYMODULE-2'),
  ('token', 'E:\\python-3.12.9\\Lib\\token.py', 'PYMODULE-2'),
  ('tokenize', 'E:\\python-3.12.9\\Lib\\tokenize.py', 'PYMODULE-2'),
  ('tracemalloc', 'E:\\python-3.12.9\\Lib\\tracemalloc.py', 'PYMODULE-2'),
  ('tty', 'E:\\python-3.12.9\\Lib\\tty.py', 'PYMODULE-2'),
  ('typing', 'E:\\python-3.12.9\\Lib\\typing.py', 'PYMODULE-2'),
  ('typing_extensions',
   'E:\\python-3.12.9\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE-2'),
  ('ui', 'e:\\VScodexiangmu\\insleidian2\\ui\\__init__.py', 'PYMODULE-2'),
  ('ui.basic_config_ui',
   'e:\\VScodexiangmu\\insleidian2\\ui\\basic_config_ui.py',
   'PYMODULE-2'),
  ('ui.instagram_dm_ui',
   'e:\\VScodexiangmu\\insleidian2\\ui\\instagram_dm_ui.py',
   'PYMODULE-2'),
  ('ui.instagram_follow_ui',
   'e:\\VScodexiangmu\\insleidian2\\ui\\instagram_follow_ui.py',
   'PYMODULE-2'),
  ('ui.main_window_v2',
   'e:\\VScodexiangmu\\insleidian2\\ui\\main_window_v2.py',
   'PYMODULE-2'),
  ('ui.settings_ui',
   'e:\\VScodexiangmu\\insleidian2\\ui\\settings_ui.py',
   'PYMODULE-2'),
  ('ui.style_manager',
   'e:\\VScodexiangmu\\insleidian2\\ui\\style_manager.py',
   'PYMODULE-2'),
  ('ui.styled_widgets',
   'e:\\VScodexiangmu\\insleidian2\\ui\\styled_widgets.py',
   'PYMODULE-2'),
  ('ui.ui_service_layer',
   'e:\\VScodexiangmu\\insleidian2\\ui\\ui_service_layer.py',
   'PYMODULE-2'),
  ('urllib', 'E:\\python-3.12.9\\Lib\\urllib\\__init__.py', 'PYMODULE-2'),
  ('urllib.error', 'E:\\python-3.12.9\\Lib\\urllib\\error.py', 'PYMODULE-2'),
  ('urllib.parse', 'E:\\python-3.12.9\\Lib\\urllib\\parse.py', 'PYMODULE-2'),
  ('urllib.request',
   'E:\\python-3.12.9\\Lib\\urllib\\request.py',
   'PYMODULE-2'),
  ('urllib.response',
   'E:\\python-3.12.9\\Lib\\urllib\\response.py',
   'PYMODULE-2'),
  ('urllib3',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE-2'),
  ('urllib3._base_connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE-2'),
  ('urllib3._collections',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE-2'),
  ('urllib3._request_methods',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE-2'),
  ('urllib3._version',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE-2'),
  ('urllib3.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE-2'),
  ('urllib3.connectionpool',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE-2'),
  ('urllib3.contrib',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.request',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.response',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE-2'),
  ('urllib3.contrib.pyopenssl',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE-2'),
  ('urllib3.contrib.socks',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE-2'),
  ('urllib3.exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE-2'),
  ('urllib3.fields',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE-2'),
  ('urllib3.filepost',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE-2'),
  ('urllib3.http2',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.http2.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE-2'),
  ('urllib3.http2.probe',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE-2'),
  ('urllib3.poolmanager',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE-2'),
  ('urllib3.response',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE-2'),
  ('urllib3.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.util.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE-2'),
  ('urllib3.util.proxy',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE-2'),
  ('urllib3.util.request',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE-2'),
  ('urllib3.util.response',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE-2'),
  ('urllib3.util.retry',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE-2'),
  ('urllib3.util.ssl_',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE-2'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE-2'),
  ('urllib3.util.ssltransport',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE-2'),
  ('urllib3.util.timeout',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE-2'),
  ('urllib3.util.url',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE-2'),
  ('urllib3.util.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE-2'),
  ('urllib3.util.wait',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE-2'),
  ('webbrowser', 'E:\\python-3.12.9\\Lib\\webbrowser.py', 'PYMODULE-2'),
  ('win32con',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE-2'),
  ('xml', 'E:\\python-3.12.9\\Lib\\xml\\__init__.py', 'PYMODULE-2'),
  ('xml.etree',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE-2'),
  ('xml.etree.ElementInclude',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE-2'),
  ('xml.etree.ElementPath',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE-2'),
  ('xml.etree.ElementTree',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE-2'),
  ('xml.etree.cElementTree',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE-2'),
  ('xml.parsers',
   'E:\\python-3.12.9\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE-2'),
  ('xml.parsers.expat',
   'E:\\python-3.12.9\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE-2'),
  ('xml.sax', 'E:\\python-3.12.9\\Lib\\xml\\sax\\__init__.py', 'PYMODULE-2'),
  ('xml.sax._exceptions',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE-2'),
  ('xml.sax.expatreader',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE-2'),
  ('xml.sax.handler',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\handler.py',
   'PYMODULE-2'),
  ('xml.sax.saxutils',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE-2'),
  ('xml.sax.xmlreader',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE-2'),
  ('xmlrpc', 'E:\\python-3.12.9\\Lib\\xmlrpc\\__init__.py', 'PYMODULE-2'),
  ('xmlrpc.client', 'E:\\python-3.12.9\\Lib\\xmlrpc\\client.py', 'PYMODULE-2'),
  ('yaml',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE-2'),
  ('yaml.composer',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE-2'),
  ('yaml.constructor',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE-2'),
  ('yaml.cyaml',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE-2'),
  ('yaml.dumper',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE-2'),
  ('yaml.emitter',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE-2'),
  ('yaml.error',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE-2'),
  ('yaml.events',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE-2'),
  ('yaml.loader',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE-2'),
  ('yaml.nodes',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE-2'),
  ('yaml.parser',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE-2'),
  ('yaml.reader',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE-2'),
  ('yaml.representer',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE-2'),
  ('yaml.resolver',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE-2'),
  ('yaml.scanner',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE-2'),
  ('yaml.serializer',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE-2'),
  ('yaml.tokens',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE-2'),
  ('zipfile', 'E:\\python-3.12.9\\Lib\\zipfile\\__init__.py', 'PYMODULE-2'),
  ('zipfile._path',
   'E:\\python-3.12.9\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE-2'),
  ('zipfile._path.glob',
   'E:\\python-3.12.9\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE-2'),
  ('zipimport', 'E:\\python-3.12.9\\Lib\\zipimport.py', 'PYMODULE-2')])
