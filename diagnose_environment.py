# -*- coding: utf-8 -*-
"""
🔍 环境诊断脚本
功能：诊断Python环境，找出文件大小过大的原因
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python_environment():
    """检查Python环境"""
    print("🔍 Python环境诊断")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"Python前缀: {sys.prefix}")
    
    # 检查是否在虚拟环境中
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 当前在虚拟环境中")
        print(f"虚拟环境路径: {sys.prefix}")
    else:
        print("⚠️  当前在全局Python环境中")
        print("💡 建议: 使用虚拟环境可以大幅减小打包文件大小")
    
    # 检查环境类型
    if 'conda' in sys.executable.lower() or 'anaconda' in sys.executable.lower():
        print("📦 环境类型: Conda/Anaconda")
        print("💡 提示: Conda环境通常比pip环境大，建议使用纯pip环境")
    else:
        print("📦 环境类型: 标准Python/pip")

def check_installed_packages():
    """检查已安装的包"""
    print("\n🔍 已安装包分析")
    print("=" * 50)
    
    try:
        import pkg_resources
        packages = list(pkg_resources.working_set)
        
        print(f"总包数量: {len(packages)}")
        
        # 大型包列表
        large_packages = [
            'numpy', 'pandas', 'scipy', 'matplotlib', 'tensorflow', 
            'torch', 'opencv', 'pillow', 'scikit-learn', 'jupyter',
            'notebook', 'ipython', 'conda', 'anaconda'
        ]
        
        found_large_packages = []
        for pkg in packages:
            if any(large_pkg in pkg.project_name.lower() for large_pkg in large_packages):
                found_large_packages.append(f"{pkg.project_name}: {pkg.version}")
        
        if found_large_packages:
            print("\n⚠️  发现大型包（可能导致文件过大）:")
            for pkg in found_large_packages:
                print(f"  - {pkg}")
            print("\n💡 建议: 在干净的虚拟环境中只安装必要的包")
        else:
            print("✅ 未发现明显的大型包")
        
        # 显示所有包（前20个）
        print(f"\n📋 已安装包列表（前20个）:")
        for i, pkg in enumerate(packages[:20]):
            print(f"  {i+1:2d}. {pkg.project_name}: {pkg.version}")
        
        if len(packages) > 20:
            print(f"  ... 还有 {len(packages) - 20} 个包")
            
    except Exception as e:
        print(f"❌ 检查包列表失败: {e}")

def check_pyqt_version():
    """检查PyQt版本"""
    print("\n🔍 GUI框架检查")
    print("=" * 50)
    
    # 检查PyQt6
    try:
        import PyQt6
        print("✅ PyQt6 已安装")
        print(f"   版本: {PyQt6.QtCore.PYQT_VERSION_STR}")
        print("   大小: ~150-200MB（较大）")
    except ImportError:
        print("❌ PyQt6 未安装")
    
    # 检查PyQt5
    try:
        import PyQt5
        print("✅ PyQt5 已安装")
        print(f"   版本: {PyQt5.QtCore.PYQT_VERSION_STR}")
        print("   大小: ~80-120MB（较小）")
    except ImportError:
        print("❌ PyQt5 未安装")
    
    # 检查tkinter
    try:
        import tkinter
        print("✅ tkinter 可用（Python内置）")
        print("   大小: ~5-10MB（最小）")
    except ImportError:
        print("❌ tkinter 不可用")

def estimate_package_size():
    """估算打包大小"""
    print("\n🔍 打包大小估算")
    print("=" * 50)
    
    base_size = 30  # Python基础大小 MB
    
    # 检查主要组件大小
    components = {
        "Python基础": 30,
        "PyQt6": 0,
        "PyQt5": 0,
        "requests": 5,
        "psutil": 2,
        "其他包": 0
    }
    
    try:
        import PyQt6
        components["PyQt6"] = 150
    except ImportError:
        pass
    
    try:
        import PyQt5
        components["PyQt5"] = 80
    except ImportError:
        pass
    
    # 计算其他包大小
    try:
        import pkg_resources
        packages = list(pkg_resources.working_set)
        other_size = max(0, len(packages) * 2 - 10)  # 估算
        components["其他包"] = other_size
    except:
        pass
    
    total_size = sum(components.values())
    
    print("📊 大小估算:")
    for component, size in components.items():
        if size > 0:
            print(f"  {component}: ~{size}MB")
    
    print(f"\n📏 预估总大小: ~{total_size}MB")
    
    if total_size > 100:
        print("⚠️  文件可能较大，建议优化")
    else:
        print("✅ 文件大小合理")

def suggest_optimization():
    """建议优化方案"""
    print("\n💡 优化建议")
    print("=" * 50)
    
    # 检查环境状态
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    is_conda = 'conda' in sys.executable.lower() or 'anaconda' in sys.executable.lower()
    
    suggestions = []
    
    if not in_venv:
        suggestions.append("🎯 创建虚拟环境，只安装必要的包")
    
    if is_conda:
        suggestions.append("🎯 使用纯pip环境替代conda环境")
    
    try:
        import PyQt6
        suggestions.append("🎯 考虑使用PyQt5替代PyQt6（可减少70MB）")
    except ImportError:
        pass
    
    # 检查大型包
    try:
        import pkg_resources
        packages = [pkg.project_name.lower() for pkg in pkg_resources.working_set]
        large_packages = ['numpy', 'pandas', 'scipy', 'matplotlib', 'tensorflow', 'torch']
        found_large = [pkg for pkg in large_packages if pkg in packages]
        
        if found_large:
            suggestions.append(f"🎯 移除不必要的大型包: {', '.join(found_large)}")
    except:
        pass
    
    if suggestions:
        print("建议的优化步骤:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"  {i}. {suggestion}")
    else:
        print("✅ 当前环境已经比较优化")
    
    print("\n🚀 推荐的最小环境配置:")
    print("  1. 创建新的虚拟环境")
    print("  2. 只安装: PyQt5, requests, psutil")
    print("  3. 预期文件大小: 40-60MB")

def create_minimal_requirements():
    """创建最小依赖文件"""
    print("\n📝 创建最小依赖配置")
    print("=" * 50)
    
    minimal_requirements = """# 最小依赖配置 - 用于减小打包文件大小
PyQt5>=5.15.0
requests>=2.28.0
psutil>=5.9.0

# 打包工具
pyinstaller>=5.0.0
"""
    
    try:
        with open("requirements_minimal.txt", "w", encoding="utf-8") as f:
            f.write(minimal_requirements)
        print("✅ 已创建 requirements_minimal.txt")
        print("💡 使用方法:")
        print("   1. 创建新虚拟环境: python -m venv venv_minimal")
        print("   2. 激活环境: venv_minimal\\Scripts\\activate")
        print("   3. 安装依赖: pip install -r requirements_minimal.txt")
        print("   4. 重新打包程序")
    except Exception as e:
        print(f"❌ 创建文件失败: {e}")

def main():
    """主函数"""
    print("🔍 Python环境诊断工具")
    print("目的: 找出打包文件过大的原因")
    print("=" * 60)
    
    check_python_environment()
    check_installed_packages()
    check_pyqt_version()
    estimate_package_size()
    suggest_optimization()
    create_minimal_requirements()
    
    print("\n" + "=" * 60)
    print("🎯 诊断完成！请根据建议优化环境后重新打包")
    
    input("\n按Enter键退出...")

if __name__ == "__main__":
    main()
