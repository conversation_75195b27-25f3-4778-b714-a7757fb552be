# -*- coding: utf-8 -*-
"""
🚀 Instagram任务管理器 - 完整打包和测试流程 (Python版本)
功能：一键完成环境检查、依赖安装、功能测试、程序打包、结果验证
"""

import os
import sys
import subprocess
import importlib
from pathlib import Path
import time

class PackageManager:
    """打包管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.app_name = "Instagram任务管理器"
        self.dist_path = self.project_root / "dist" / f"{self.app_name}.exe"
        
    def print_step(self, step_num, title):
        """打印步骤标题"""
        print(f"\n📋 步骤{step_num}：{title}")
        print("=" * 50)
    
    def check_python_environment(self):
        """检查Python环境"""
        self.print_step(1, "检查Python环境")
        
        try:
            python_version = sys.version
            print(f"✅ Python版本: {python_version}")
            return True
        except Exception as e:
            print(f"❌ Python环境检查失败: {e}")
            return False
    
    def install_dependencies(self):
        """安装依赖包"""
        self.print_step(2, "安装依赖包")
        
        try:
            # 检查requirements.txt是否存在
            requirements_file = self.project_root / "requirements.txt"
            if not requirements_file.exists():
                print("⚠️  requirements.txt不存在，跳过依赖安装")
                return True
            
            print("📦 正在安装依赖包...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 依赖包安装完成")
                return True
            else:
                print(f"❌ 依赖安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 依赖安装过程出错: {e}")
            return False
    
    def run_functionality_test(self):
        """运行功能测试"""
        self.print_step(3, "运行功能测试")
        
        try:
            # 运行测试脚本
            result = subprocess.run([
                sys.executable, "test_package.py"
            ], cwd=self.project_root, capture_output=True, text=True)
            
            print(result.stdout)
            
            if result.returncode == 0:
                print("✅ 功能测试通过")
                return True
            else:
                print("❌ 功能测试失败")
                if result.stderr:
                    print(f"错误信息: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 功能测试过程出错: {e}")
            return False
    
    def execute_packaging(self):
        """执行打包"""
        self.print_step(4, "开始打包程序")
        
        try:
            # 检查PyInstaller是否安装
            try:
                import PyInstaller
                print("✅ PyInstaller已安装")
            except ImportError:
                print("📦 正在安装PyInstaller...")
                subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"])
                print("✅ PyInstaller安装完成")
            
            # 运行打包脚本
            print("🔨 开始打包...")
            result = subprocess.run([
                sys.executable, "build_config.py"
            ], cwd=self.project_root, capture_output=True, text=True)
            
            print(result.stdout)
            
            if result.returncode == 0:
                print("✅ 打包完成")
                return True
            else:
                print("❌ 打包失败")
                if result.stderr:
                    print(f"错误信息: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 打包过程出错: {e}")
            return False
    
    def verify_package_result(self):
        """验证打包结果"""
        self.print_step(5, "验证打包结果")
        
        try:
            if self.dist_path.exists():
                # 获取文件大小
                file_size = self.dist_path.stat().st_size
                size_mb = file_size / (1024 * 1024)
                
                print(f"✅ 找到打包文件: {self.dist_path}")
                print(f"📏 文件大小: {size_mb:.1f} MB")
                
                return True
            else:
                print(f"❌ 未找到打包文件: {self.dist_path}")
                print("💡 可能的原因:")
                print("   - 打包过程失败")
                print("   - 输出路径不正确")
                print("   - 权限问题")
                return False
                
        except Exception as e:
            print(f"❌ 验证打包结果时出错: {e}")
            return False
    
    def test_packaged_program(self):
        """测试打包后的程序"""
        print("\n🧪 测试打包后的程序")
        print("=" * 50)
        
        if not self.dist_path.exists():
            print("❌ 打包文件不存在，无法测试")
            return False
        
        try:
            response = input("是否启动打包后的程序进行测试? (y/n): ").strip().lower()
            if response == 'y':
                print("🚀 启动测试程序...")
                print("⚠️  注意: 程序将在新窗口中启动")
                print("⚠️  请手动测试各项功能，测试完成后关闭程序")
                
                # 启动程序
                if os.name == 'nt':  # Windows
                    subprocess.Popen([str(self.dist_path)], shell=True)
                else:  # Linux/Mac
                    subprocess.Popen([str(self.dist_path)])
                
                print("💡 程序已启动，请在新窗口中测试功能")
                input("测试完成后按Enter键继续...")
                return True
            else:
                print("⏭️  跳过程序测试")
                return True
                
        except Exception as e:
            print(f"❌ 测试程序启动失败: {e}")
            return False
    
    def open_output_directory(self):
        """打开输出目录"""
        try:
            response = input("是否打开输出目录? (y/n): ").strip().lower()
            if response == 'y':
                dist_dir = self.project_root / "dist"
                if os.name == 'nt':  # Windows
                    os.startfile(dist_dir)
                elif os.name == 'posix':  # Linux/Mac
                    subprocess.run(['open' if sys.platform == 'darwin' else 'xdg-open', str(dist_dir)])
                print(f"📁 已打开目录: {dist_dir}")
            else:
                print("⏭️  跳过打开目录")
        except Exception as e:
            print(f"⚠️  打开目录失败: {e}")
    
    def run_complete_process(self):
        """运行完整的打包流程"""
        print("🚀 Instagram任务管理器 - 完整打包和测试流程")
        print("=" * 60)
        
        steps = [
            ("环境检查", self.check_python_environment),
            ("依赖安装", self.install_dependencies),
            ("功能测试", self.run_functionality_test),
            ("程序打包", self.execute_packaging),
            ("结果验证", self.verify_package_result),
        ]
        
        # 执行所有步骤
        for step_name, step_func in steps:
            success = step_func()
            if not success:
                print(f"\n💥 {step_name}失败，打包流程中断")
                print("💡 请解决上述问题后重新运行")
                return False
        
        # 可选步骤
        self.test_packaged_program()
        self.open_output_directory()
        
        # 显示最终结果
        print("\n🎉 打包流程完成！")
        print("=" * 60)
        print(f"📁 输出文件: {self.dist_path}")
        print("💡 使用提示:")
        print("   - 可以将exe文件复制到其他电脑运行")
        print("   - 确保目标电脑有必要的运行环境")
        print("   - 建议在不同系统版本上测试兼容性")
        print("   - 程序包含所有main.py的功能")
        
        return True


def main():
    """主函数"""
    try:
        manager = PackageManager()
        success = manager.run_complete_process()
        
        if success:
            print("\n✅ 所有步骤完成，打包成功！")
        else:
            print("\n❌ 打包过程中出现问题，请检查错误信息")
        
        input("\n按Enter键退出...")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n💥 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
