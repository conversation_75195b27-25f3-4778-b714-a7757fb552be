# -*- coding: utf-8 -*-
"""
🔐 卡密验证模块
功能：一机一码验证，时间控制，防破解
作者：Augment Agent
"""

import hashlib
import platform
import subprocess
import requests
import json
import time
import os
import sys
from typing import Tuple, Dict, Any

class MachineFingerprint:
    """机器指纹生成器"""
    
    @staticmethod
    def get_fingerprint() -> str:
        """生成唯一机器指纹"""
        try:
            # 获取CPU信息
            cpu_info = platform.processor() or "unknown"
            
            # 获取系统信息
            system_info = f"{platform.system()}-{platform.release()}"
            
            # 获取主板序列号
            motherboard = MachineFingerprint._get_motherboard_serial()
            
            # 获取硬盘序列号
            disk_serial = MachineFingerprint._get_disk_serial()
            
            # 获取网卡MAC地址
            mac_address = MachineFingerprint._get_mac_address()
            
            # 组合所有信息
            machine_info = f"{cpu_info}|{system_info}|{motherboard}|{disk_serial}|{mac_address}"
            
            # 生成SHA256指纹
            fingerprint = hashlib.sha256(machine_info.encode('utf-8')).hexdigest()
            return fingerprint
            
        except Exception as e:
            # 备用方案：使用系统基本信息
            fallback_info = f"{platform.node()}-{platform.machine()}-{platform.system()}"
            return hashlib.sha256(fallback_info.encode('utf-8')).hexdigest()
    
    @staticmethod
    def _get_motherboard_serial() -> str:
        """获取主板序列号"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(
                    ['wmic', 'baseboard', 'get', 'serialnumber'],
                    capture_output=True, text=True, timeout=5
                )
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:
                    return lines[1].strip()
            elif platform.system() == "Linux":
                result = subprocess.run(
                    ['sudo', 'dmidecode', '-s', 'baseboard-serial-number'],
                    capture_output=True, text=True, timeout=5
                )
                return result.stdout.strip()
        except:
            pass
        return "unknown"
    
    @staticmethod
    def _get_disk_serial() -> str:
        """获取硬盘序列号"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(
                    ['wmic', 'diskdrive', 'get', 'serialnumber'],
                    capture_output=True, text=True, timeout=5
                )
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:
                    return lines[1].strip()
            elif platform.system() == "Linux":
                result = subprocess.run(
                    ['lsblk', '-d', '-o', 'serial'],
                    capture_output=True, text=True, timeout=5
                )
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:
                    return lines[1].strip()
        except:
            pass
        return "unknown"
    
    @staticmethod
    def _get_mac_address() -> str:
        """获取MAC地址"""
        try:
            import uuid
            mac = uuid.getnode()
            return ':'.join(('%012X' % mac)[i:i+2] for i in range(0, 12, 2))
        except:
            return "unknown"


class SecurityChecker:
    """安全检查器"""
    
    @staticmethod
    def anti_debug_check() -> bool:
        """反调试检测"""
        try:
            # 检测Python调试器
            if sys.gettrace() is not None:
                return False
            
            # 检测危险进程
            dangerous_processes = [
                'ollydbg.exe', 'ida.exe', 'ida64.exe', 'windbg.exe',
                'x64dbg.exe', 'cheatengine.exe', 'processhacker.exe',
                'wireshark.exe', 'fiddler.exe'
            ]
            
            try:
                import psutil
                for proc in psutil.process_iter(['name']):
                    if proc.info['name'] and proc.info['name'].lower() in dangerous_processes:
                        return False
            except ImportError:
                pass
            
            return True
        except:
            return True
    
    @staticmethod
    def integrity_check() -> bool:
        """完整性检查"""
        try:
            # 检查关键文件是否被修改
            current_file = __file__
            if os.path.exists(current_file):
                with open(current_file, 'rb') as f:
                    content = f.read()
                    # 简单的完整性检查
                    if b"license_validator" in content and b"MachineFingerprint" in content:
                        return True
            return False
        except:
            return False


class LicenseValidator:
    """卡密验证器"""
    
    def __init__(self, server_url: str = None, app_key: str = None):
        self.server_url = server_url or "https://your-license-server.com/api"
        self.app_key = app_key or "your-app-secret-key"
        self.cache_file = os.path.join(os.path.expanduser("~"), ".app_license_cache")
        self.last_check_time = 0
        self.check_interval = 3600  # 1小时检查一次
    
    def validate_license(self, license_key: str) -> Tuple[bool, str]:
        """验证卡密"""
        try:
            # 安全检查
            if not SecurityChecker.anti_debug_check():
                return False, "检测到调试环境"
            
            if not SecurityChecker.integrity_check():
                return False, "程序完整性验证失败"
            
            # 获取机器指纹
            machine_id = MachineFingerprint.get_fingerprint()
            
            # 检查本地缓存
            current_time = int(time.time())
            if current_time - self.last_check_time < self.check_interval:
                cache_result = self._check_local_cache(machine_id)
                if cache_result[0]:
                    return cache_result
            
            # 在线验证
            online_result = self._validate_online(license_key, machine_id)
            if online_result[0]:
                self.last_check_time = current_time
                self._save_cache(online_result[1], machine_id)
            
            return online_result
            
        except Exception as e:
            return False, f"验证过程出错: {str(e)}"
    
    def _validate_online(self, license_key: str, machine_id: str) -> Tuple[bool, Dict[str, Any]]:
        """在线验证"""
        try:
            # 构建验证数据
            timestamp = int(time.time())
            data = {
                "license_key": license_key,
                "machine_id": machine_id,
                "app_key": self.app_key,
                "timestamp": timestamp,
                "version": "1.0.0"
            }
            
            # 发送验证请求
            response = requests.post(
                f"{self.server_url}/validate",
                json=data,
                timeout=10,
                headers={
                    "User-Agent": "LicenseValidator/1.0",
                    "Content-Type": "application/json"
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("status") == "success":
                    return True, result.get("data", {})
                else:
                    return False, result.get("message", "验证失败")
            else:
                return False, f"服务器响应错误: {response.status_code}"
                
        except requests.RequestException as e:
            return False, f"网络连接失败: {str(e)}"
        except Exception as e:
            return False, f"在线验证出错: {str(e)}"
    
    def _check_local_cache(self, machine_id: str) -> Tuple[bool, str]:
        """检查本地缓存"""
        try:
            if not os.path.exists(self.cache_file):
                return False, "无本地缓存"
            
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 验证机器ID
            if cache_data.get("machine_id") != machine_id:
                return False, "设备不匹配"
            
            # 检查过期时间
            expire_time = cache_data.get("expire_time", 0)
            if expire_time <= int(time.time()):
                return False, "许可证已过期"
            
            return True, "缓存验证通过"
            
        except Exception as e:
            return False, f"缓存检查失败: {str(e)}"
    
    def _save_cache(self, license_data: Dict[str, Any], machine_id: str):
        """保存验证缓存"""
        try:
            cache_data = {
                "machine_id": machine_id,
                "expire_time": license_data.get("expire_time", 0),
                "license_info": license_data,
                "cache_time": int(time.time())
            }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f)
                
        except Exception as e:
            pass  # 缓存保存失败不影响主流程
    
    def get_license_info(self) -> Dict[str, Any]:
        """获取许可证信息"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                return cache_data.get("license_info", {})
        except:
            pass
        return {}


# 全局验证器实例
_license_validator = None

def get_license_validator() -> LicenseValidator:
    """获取全局许可证验证器实例"""
    global _license_validator
    if _license_validator is None:
        _license_validator = LicenseValidator()
    return _license_validator


def quick_license_check(license_key: str) -> bool:
    """快速许可证检查"""
    validator = get_license_validator()
    result, message = validator.validate_license(license_key)
    return result
