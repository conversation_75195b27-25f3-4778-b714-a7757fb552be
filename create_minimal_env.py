# -*- coding: utf-8 -*-
"""
🎯 创建最小打包环境
功能：自动创建一个干净的虚拟环境，只安装必要的包，用于小体积打包
"""

import os
import sys
import subprocess
from pathlib import Path

class MinimalEnvironmentCreator:
    """最小环境创建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.venv_name = "venv_minimal"
        self.venv_path = self.project_root / self.venv_name
        
    def create_virtual_environment(self):
        """创建虚拟环境"""
        print("🎯 步骤1: 创建虚拟环境")
        print("=" * 40)
        
        if self.venv_path.exists():
            print(f"⚠️  虚拟环境已存在: {self.venv_path}")
            response = input("是否删除并重新创建? (y/n): ").strip().lower()
            if response == 'y':
                import shutil
                shutil.rmtree(self.venv_path)
                print("✅ 已删除旧环境")
            else:
                print("⏭️  跳过创建，使用现有环境")
                return True
        
        try:
            print(f"📦 正在创建虚拟环境: {self.venv_name}")
            subprocess.run([
                sys.executable, "-m", "venv", str(self.venv_path)
            ], check=True)
            print("✅ 虚拟环境创建成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 虚拟环境创建失败: {e}")
            return False
    
    def get_venv_python(self):
        """获取虚拟环境的Python路径"""
        if os.name == 'nt':  # Windows
            return self.venv_path / "Scripts" / "python.exe"
        else:  # Linux/Mac
            return self.venv_path / "bin" / "python"
    
    def get_venv_pip(self):
        """获取虚拟环境的pip路径"""
        if os.name == 'nt':  # Windows
            return self.venv_path / "Scripts" / "pip.exe"
        else:  # Linux/Mac
            return self.venv_path / "bin" / "pip"
    
    def install_minimal_packages(self):
        """安装最小依赖包"""
        print("\n🎯 步骤2: 安装最小依赖包")
        print("=" * 40)
        
        # 最小依赖列表
        packages = [
            "PyQt5>=5.15.0",      # GUI框架（比PyQt6小）
            "requests>=2.28.0",   # HTTP请求
            "psutil>=5.9.0",      # 系统进程管理
            "pyinstaller>=5.0.0", # 打包工具
        ]
        
        pip_path = self.get_venv_pip()
        
        try:
            # 升级pip
            print("📦 升级pip...")
            subprocess.run([
                str(pip_path), "install", "--upgrade", "pip"
            ], check=True)
            
            # 安装包
            for package in packages:
                print(f"📦 安装 {package}...")
                subprocess.run([
                    str(pip_path), "install", package
                ], check=True)
            
            print("✅ 所有依赖包安装完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 包安装失败: {e}")
            return False
    
    def verify_installation(self):
        """验证安装"""
        print("\n🎯 步骤3: 验证安装")
        print("=" * 40)
        
        python_path = self.get_venv_python()
        
        # 测试导入
        test_imports = [
            "PyQt5.QtCore",
            "PyQt5.QtGui", 
            "PyQt5.QtWidgets",
            "requests",
            "psutil",
            "PyInstaller",
        ]
        
        failed_imports = []
        
        for module in test_imports:
            try:
                result = subprocess.run([
                    str(python_path), "-c", f"import {module}; print('✅ {module}')"
                ], capture_output=True, text=True, check=True)
                print(result.stdout.strip())
            except subprocess.CalledProcessError:
                print(f"❌ {module}")
                failed_imports.append(module)
        
        if failed_imports:
            print(f"\n❌ 导入失败的模块: {', '.join(failed_imports)}")
            return False
        else:
            print("\n✅ 所有模块导入成功")
            return True
    
    def create_activation_script(self):
        """创建激活脚本"""
        print("\n🎯 步骤4: 创建激活脚本")
        print("=" * 40)
        
        if os.name == 'nt':  # Windows
            activate_script = f"""@echo off
echo 🎯 激活最小打包环境
echo ========================
call "{self.venv_path}\\Scripts\\activate.bat"
echo ✅ 环境已激活
echo 💡 现在可以运行: python quick_package.py
cmd /k
"""
            script_name = "activate_minimal.bat"
        else:  # Linux/Mac
            activate_script = f"""#!/bin/bash
echo "🎯 激活最小打包环境"
echo "========================"
source "{self.venv_path}/bin/activate"
echo "✅ 环境已激活"
echo "💡 现在可以运行: python quick_package.py"
bash
"""
            script_name = "activate_minimal.sh"
        
        try:
            script_path = self.project_root / script_name
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(activate_script)
            
            if os.name != 'nt':
                os.chmod(script_path, 0o755)  # 添加执行权限
            
            print(f"✅ 已创建激活脚本: {script_name}")
            return script_path
        except Exception as e:
            print(f"❌ 创建激活脚本失败: {e}")
            return None
    
    def update_main_for_pyqt5(self):
        """更新main.py以使用PyQt5"""
        print("\n🎯 步骤5: 检查main.py兼容性")
        print("=" * 40)
        
        main_file = self.project_root / "main.py"
        if not main_file.exists():
            print("⚠️  main.py不存在，跳过更新")
            return True
        
        try:
            with open(main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "PyQt6" in content:
                print("⚠️  检测到main.py使用PyQt6")
                print("💡 建议: 手动将PyQt6改为PyQt5以减小文件大小")
                print("   - PyQt6.QtWidgets -> PyQt5.QtWidgets")
                print("   - PyQt6.QtCore -> PyQt5.QtCore")
                print("   - PyQt6.QtGui -> PyQt5.QtGui")
                
                response = input("是否自动替换? (y/n): ").strip().lower()
                if response == 'y':
                    # 自动替换
                    content = content.replace("PyQt6", "PyQt5")
                    
                    # 备份原文件
                    backup_file = self.project_root / "main.py.backup"
                    with open(backup_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    # 写入新内容
                    with open(main_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print("✅ 已自动替换PyQt6为PyQt5")
                    print(f"📄 原文件备份为: {backup_file}")
                else:
                    print("⏭️  跳过自动替换，请手动修改")
            else:
                print("✅ main.py兼容性检查通过")
            
            return True
        except Exception as e:
            print(f"❌ 检查main.py失败: {e}")
            return False
    
    def show_usage_instructions(self, script_path):
        """显示使用说明"""
        print("\n🎉 最小环境创建完成！")
        print("=" * 50)
        
        print("📋 使用步骤:")
        if script_path:
            if os.name == 'nt':
                print(f"  1. 双击运行: {script_path.name}")
            else:
                print(f"  1. 运行: ./{script_path.name}")
        else:
            if os.name == 'nt':
                print(f"  1. 手动激活: {self.venv_path}\\Scripts\\activate.bat")
            else:
                print(f"  1. 手动激活: source {self.venv_path}/bin/activate")
        
        print("  2. 运行打包: python quick_package.py")
        print("  3. 预期大小: 40-60MB")
        
        print("\n💡 优势:")
        print("  - 干净的环境，只有必要的包")
        print("  - 使用PyQt5替代PyQt6（减少70MB）")
        print("  - 文件大小显著减小")
        
        print(f"\n📁 环境位置: {self.venv_path}")
    
    def create_minimal_environment(self):
        """创建完整的最小环境"""
        print("🎯 创建最小打包环境")
        print("目的: 大幅减小打包文件大小")
        print("=" * 60)
        
        steps = [
            ("创建虚拟环境", self.create_virtual_environment),
            ("安装依赖包", self.install_minimal_packages),
            ("验证安装", self.verify_installation),
            ("检查兼容性", self.update_main_for_pyqt5),
        ]
        
        for step_name, step_func in steps:
            success = step_func()
            if not success:
                print(f"\n💥 {step_name}失败，创建过程中断")
                return False
        
        script_path = self.create_activation_script()
        self.show_usage_instructions(script_path)
        
        return True

def main():
    """主函数"""
    try:
        creator = MinimalEnvironmentCreator()
        success = creator.create_minimal_environment()
        
        if success:
            print("\n✅ 最小环境创建成功！")
        else:
            print("\n❌ 最小环境创建失败")
        
        input("\n按Enter键退出...")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n💥 程序出错: {e}")

if __name__ == "__main__":
    main()
