# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('e:\\VScodexiangmu\\insleidian2\\img', 'img'), ('e:\\VScodexiangmu\\insleidian2\\app_config.json', '.')],
    hiddenimports=['PyQt6.QtCore', 'PyQt6.QtGui', 'PyQt6.QtWidgets', 'requests', 'psutil', 'sqlite3'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib', 'pandas', 'scipy', 'sklearn', 'tensorflow', 'torch', 'jupyter', 'IPython', 'notebook', 'sympy', 'statsmodels', 'seaborn', 'plotly', 'bokeh', 'PyQt6.QtWebEngine', 'PyQt6.QtWebEngineWidgets', 'PyQt6.QtWebEngineCore', 'PyQt6.QtMultimedia', 'PyQt6.QtMultimediaWidgets', 'PyQt6.Qt3D', 'PyQt6.QtCharts', 'PyQt6.QtDataVisualization', 'PyQt6.QtDesigner', 'PyQt6.QtHelp', 'PyQt6.QtLocation', 'PyQt6.QtNfc', 'PyQt6.QtPositioning', 'PyQt6.QtQuick', 'PyQt6.QtQuickWidgets', 'PyQt6.QtRemoteObjects', 'PyQt6.QtSensors', 'PyQt6.QtSerialPort', 'PyQt6.QtSql', 'PyQt6.QtSvg', 'PyQt6.QtTest', 'PyQt6.QtWebChannel', 'PyQt6.QtWebSockets', 'cv2.aruco', 'cv2.bgsegm', 'cv2.bioinspired', 'cv2.ccalib', 'cv2.datasets', 'cv2.dnn_superres', 'cv2.face', 'cv2.freetype', 'cv2.fuzzy', 'cv2.hdf', 'cv2.hfs', 'cv2.img_hash', 'cv2.intensity_transform', 'cv2.line_descriptor', 'cv2.mcc', 'cv2.optflow', 'cv2.phase_unwrapping', 'cv2.plot', 'cv2.quality', 'cv2.rapid', 'cv2.reg', 'cv2.rgbd', 'cv2.saliency', 'cv2.stereo', 'cv2.structured_light', 'cv2.superres', 'cv2.surface_matching', 'cv2.text', 'cv2.tracking', 'cv2.videoio', 'cv2.videostab', 'cv2.wechat_qrcode', 'cv2.xfeatures2d', 'cv2.ximgproc', 'cv2.xobjdetect', 'cv2.xphoto', 'numpy.f2py', 'numpy.distutils', 'numpy.testing'],
    noarchive=False,
    optimize=2,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [('O', None, 'OPTION'), ('O', None, 'OPTION')],
    name='ins雷电中控_优化版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
