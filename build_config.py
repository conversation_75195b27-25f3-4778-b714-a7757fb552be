# -*- coding: utf-8 -*-
"""
🚀 程序打包配置文件
功能：自动化打包Instagram任务管理器
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

class BuildConfig:
    """打包配置管理"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.app_name = "Instagram任务管理器"
        self.version = "1.0.0"
        self.author = "Your Name"
        self.description = "Instagram自动化任务管理工具"
        
        # 打包配置
        self.main_script = "main.py"  # 主程序入口
        self.icon_file = "resources/icon.ico"  # 图标文件
        self.output_dir = "dist"
        self.build_dir = "build"
        
        # 需要包含的数据文件
        self.data_files = [
            ("img", "img"),  # 图片资源
            ("config", "config"),  # 配置文件
            ("data", "data"),  # 数据目录
            ("logs", "logs"),  # 日志目录
            ("screenshots", "screenshots"),  # 截图目录
            ("app_config.json", "."),  # 主配置文件
        ]
        
        # 需要包含的隐藏导入
        self.hidden_imports = [
            # PyQt6 核心模块
            "PyQt6.QtCore",
            "PyQt6.QtGui",
            "PyQt6.QtWidgets",
            "PyQt6.QtNetwork",

            # 网络和系统
            "requests",
            "requests.adapters",
            "requests.auth",
            "requests.cookies",
            "urllib3",
            "psutil",

            # 数据库
            "sqlite3",

            # XML处理
            "xml.etree.ElementTree",
            "xml.etree.cElementTree",

            # 异步和线程
            "asyncio",
            "threading",
            "subprocess",
            "concurrent.futures",

            # 项目核心模块
            "core.async_bridge",
            "core.unified_emulator_manager",
            "core.instagram_task",
            "core.instagram_follow_task",
            "core.logger_manager",
            "core.simple_config",
            "core.heartbeat_manager",
            "core.screenshot_manager",
            "core.status_converter",
            "core.window_arrangement_manager",
            "core.config_hot_reload",

            # UI模块
            "ui.main_window_v2",
            "ui.instagram_dm_ui",
            "ui.instagram_follow_ui",
            "ui.basic_config_ui",
            "ui.settings_ui",
            "ui.style_manager",
            "ui.styled_widgets",
            "ui.ui_service_layer",

            # 数据模块
            "data.database_manager",

            # 雷电API
            "core.leidianapi",
            "core.native",
        ]
        
        # 排除的模块（大幅减小文件大小）
        self.excludes = [
            # GUI框架
            "tkinter", "tkinter.ttk", "tkinter.constants",

            # 科学计算库
            "numpy", "pandas", "scipy", "matplotlib", "seaborn",
            "sklearn", "tensorflow", "torch", "cv2", "PIL",

            # 开发工具
            "jupyter", "IPython", "notebook", "pytest", "unittest",
            "pdb", "doctest", "profile", "cProfile",

            # 网络服务
            "http.server", "socketserver", "wsgiref",
            "email", "smtplib", "poplib", "imaplib",

            # 多媒体
            "wave", "audioop", "sunau", "aifc",

            # 文档处理
            "docx", "openpyxl", "xlrd", "xlwt",

            # 其他大型库
            "cryptography", "certifi", "urllib3.contrib",
            "requests_oauthlib", "oauthlib",

            # PyQt6不需要的模块
            "PyQt6.QtWebEngine", "PyQt6.QtWebEngineWidgets",
            "PyQt6.QtMultimedia", "PyQt6.QtMultimediaWidgets",
            "PyQt6.QtCharts", "PyQt6.QtDataVisualization",
            "PyQt6.Qt3D", "PyQt6.QtQuick", "PyQt6.QtQml",
            "PyQt6.QtSql", "PyQt6.QtTest", "PyQt6.QtHelp",
            "PyQt6.QtDesigner", "PyQt6.QtUiTools",
        ]
    
    def create_spec_file(self):
        """创建PyInstaller spec文件"""
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件配置
datas = {self.data_files}

# 分析配置
a = Analysis(
    ['{self.main_script}'],
    pathex=['{self.project_root}'],
    binaries=[],
    datas=datas,
    hiddenimports={self.hidden_imports},
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes={self.excludes},
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# PYZ配置
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# EXE配置（优化版）
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.app_name}',
    debug=False,                        # 关闭调试信息
    bootloader_ignore_signals=False,
    strip=True,                         # 🎯 移除符号表，减小文件大小
    upx=True,                          # 🎯 启用UPX压缩
    upx_exclude=[                      # 🎯 排除不适合压缩的文件
        "vcruntime140.dll",
        "python3.dll",
        "python312.dll",
    ],
    runtime_tmpdir=None,
    console=False,                     # 🎯 隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='{self.icon_file}',
    version='version_info.txt',
    # 🎯 额外优化选项
    optimize=2,                        # Python字节码优化
)
'''
        
        spec_file = self.project_root / f"{self.app_name}.spec"
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        return spec_file
    
    def create_version_info(self):
        """创建版本信息文件"""
        version_info = f'''# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=({self.version.replace('.', ',')},0),
    prodvers=({self.version.replace('.', ',')},0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'080404B0',
          [
            StringStruct(u'CompanyName', u'{self.author}'),
            StringStruct(u'FileDescription', u'{self.description}'),
            StringStruct(u'FileVersion', u'{self.version}'),
            StringStruct(u'InternalName', u'{self.app_name}'),
            StringStruct(u'LegalCopyright', u'Copyright © 2025 {self.author}'),
            StringStruct(u'OriginalFilename', u'{self.app_name}.exe'),
            StringStruct(u'ProductName', u'{self.app_name}'),
            StringStruct(u'ProductVersion', u'{self.version}')
          ]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
'''
        
        version_file = self.project_root / "version_info.txt"
        with open(version_file, 'w', encoding='utf-8') as f:
            f.write(version_info)
        
        return version_file
    
    def clean_build(self):
        """清理构建文件"""
        dirs_to_clean = [self.build_dir, self.output_dir, "__pycache__"]
        
        for dir_name in dirs_to_clean:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"✅ 已清理: {dir_path}")
    
    def check_dependencies(self):
        """检查依赖是否安装"""
        required_packages = [
            "PyQt5", "requests", "psutil", "asyncio"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
            print("请运行: pip install " + " ".join(missing_packages))
            return False
        
        print("✅ 所有依赖包已安装")
        return True
    
    def build(self, clean_first=True):
        """执行打包"""
        print(f"🚀 开始打包 {self.app_name} v{self.version}")
        
        # 检查依赖
        if not self.check_dependencies():
            return False
        
        # 清理旧文件
        if clean_first:
            self.clean_build()
        
        # 创建配置文件
        spec_file = self.create_spec_file()
        version_file = self.create_version_info()
        
        print(f"📝 已创建配置文件: {spec_file}")
        print(f"📝 已创建版本信息: {version_file}")
        
        # 执行打包
        try:
            cmd = ["pyinstaller", str(spec_file)]
            print(f"🔨 执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 打包成功!")
                
                # 显示输出文件信息
                exe_file = self.project_root / self.output_dir / f"{self.app_name}.exe"
                if exe_file.exists():
                    size_mb = exe_file.stat().st_size / (1024 * 1024)
                    print(f"📦 输出文件: {exe_file}")
                    print(f"📏 文件大小: {size_mb:.1f} MB")
                
                return True
            else:
                print("❌ 打包失败!")
                print("错误信息:")
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ 打包过程出错: {e}")
            return False


def main():
    """主函数"""
    builder = BuildConfig()
    
    # 检查主程序文件是否存在
    main_file = builder.project_root / builder.main_script
    if not main_file.exists():
        print(f"❌ 找不到主程序文件: {main_file}")
        print("请确保main.py文件存在，或修改build_config.py中的main_script配置")
        return
    
    # 执行打包
    success = builder.build()
    
    if success:
        print("\n🎉 打包完成!")
        print(f"📁 输出目录: {builder.project_root / builder.output_dir}")
        print("💡 提示: 可以将dist目录中的文件分发给用户")
    else:
        print("\n💥 打包失败，请检查错误信息")


if __name__ == "__main__":
    main()
