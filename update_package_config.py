# -*- coding: utf-8 -*-
"""
🔧 更新打包配置脚本
功能：将当前项目的配置应用到打包后的程序中，作为默认配置
"""

import json
import shutil
from pathlib import Path

class PackageConfigUpdater:
    """打包配置更新器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.source_config = self.project_root / "app_config.json"
        self.dist_config = self.project_root / "dist" / "app_config.json"
        self.backup_config = self.project_root / "dist" / "app_config_backup.json"
    
    def backup_current_dist_config(self):
        """备份当前dist中的配置"""
        if self.dist_config.exists():
            try:
                shutil.copy2(self.dist_config, self.backup_config)
                print(f"✅ 已备份原配置: {self.backup_config}")
                return True
            except Exception as e:
                print(f"⚠️  备份配置失败: {e}")
                return False
        else:
            print("ℹ️  dist中没有现有配置文件")
            return True
    
    def copy_current_config_to_dist(self):
        """将当前配置复制到dist目录"""
        if not self.source_config.exists():
            print(f"❌ 源配置文件不存在: {self.source_config}")
            return False
        
        if not self.dist_config.parent.exists():
            print(f"❌ dist目录不存在: {self.dist_config.parent}")
            return False
        
        try:
            shutil.copy2(self.source_config, self.dist_config)
            print(f"✅ 已更新dist配置: {self.dist_config}")
            return True
        except Exception as e:
            print(f"❌ 复制配置失败: {e}")
            return False
    
    def show_config_comparison(self):
        """显示配置对比"""
        print("\n📋 配置对比")
        print("=" * 50)
        
        try:
            # 读取源配置
            with open(self.source_config, 'r', encoding='utf-8') as f:
                source_data = json.load(f)
            
            # 读取备份配置（如果存在）
            if self.backup_config.exists():
                with open(self.backup_config, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)
                
                # 对比关键配置
                key_configs = [
                    ("max_concurrent_tasks", "最大并发任务数"),
                    ("start_interval", "启动间隔(秒)"),
                    ("start_timeout", "启动超时(秒)"),
                    ("instagram_dm.message_count", "私信数量"),
                    ("instagram_dm.delay_min", "最小延迟(ms)"),
                    ("instagram_dm.delay_max", "最大延迟(ms)"),
                    ("rest_config.enabled", "休息模式"),
                    ("instagram_follow.direct_follow_count", "直接关注数量"),
                ]
                
                print("配置项对比 (原配置 -> 新配置):")
                for key_path, description in key_configs:
                    old_val = self._get_nested_value(backup_data, key_path)
                    new_val = self._get_nested_value(source_data, key_path)
                    
                    if old_val != new_val:
                        print(f"  📝 {description}: {old_val} -> {new_val}")
                    else:
                        print(f"  ✅ {description}: {new_val} (无变化)")
            else:
                print("ℹ️  没有备份配置，显示当前配置:")
                print(f"  📝 最大并发任务数: {source_data.get('max_concurrent_tasks', 'N/A')}")
                print(f"  📝 启动间隔: {source_data.get('start_interval', 'N/A')}秒")
                print(f"  📝 私信数量: {source_data.get('instagram_dm', {}).get('message_count', 'N/A')}")
                print(f"  📝 休息模式: {source_data.get('rest_config', {}).get('enabled', 'N/A')}")
        
        except Exception as e:
            print(f"❌ 配置对比失败: {e}")
    
    def _get_nested_value(self, data, key_path):
        """获取嵌套字典的值"""
        keys = key_path.split('.')
        value = data
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return "N/A"
    
    def verify_update(self):
        """验证更新结果"""
        print("\n🔍 验证更新结果")
        print("=" * 50)
        
        if not self.dist_config.exists():
            print("❌ dist配置文件不存在")
            return False
        
        try:
            with open(self.dist_config, 'r', encoding='utf-8') as f:
                dist_data = json.load(f)
            
            with open(self.source_config, 'r', encoding='utf-8') as f:
                source_data = json.load(f)
            
            # 检查关键配置是否一致
            key_configs = [
                "max_concurrent_tasks",
                "start_interval", 
                "start_timeout",
                "emulator_path"
            ]
            
            all_match = True
            for key in key_configs:
                if dist_data.get(key) == source_data.get(key):
                    print(f"  ✅ {key}: {dist_data.get(key)}")
                else:
                    print(f"  ❌ {key}: {dist_data.get(key)} != {source_data.get(key)}")
                    all_match = False
            
            if all_match:
                print("\n✅ 配置更新验证成功")
                return True
            else:
                print("\n⚠️  配置更新可能有问题")
                return False
                
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False
    
    def update_package_config(self):
        """更新打包配置"""
        print("🔧 更新打包后的默认配置")
        print("目的: 让打包程序使用当前项目配置作为默认值")
        print("=" * 60)
        
        # 检查文件
        if not self.source_config.exists():
            print(f"❌ 源配置文件不存在: {self.source_config}")
            return False
        
        if not self.dist_config.parent.exists():
            print(f"❌ dist目录不存在，请先运行打包")
            return False
        
        # 执行更新步骤
        steps = [
            ("备份原配置", self.backup_current_dist_config),
            ("复制当前配置", self.copy_current_config_to_dist),
            ("验证更新", self.verify_update),
        ]
        
        for step_name, step_func in steps:
            print(f"\n📋 {step_name}...")
            success = step_func()
            if not success:
                print(f"💥 {step_name}失败")
                return False
        
        # 显示对比
        self.show_config_comparison()
        
        print("\n🎉 配置更新完成！")
        print("💡 现在打包后的程序将使用你当前的配置作为默认值")
        
        return True

def main():
    """主函数"""
    try:
        updater = PackageConfigUpdater()
        success = updater.update_package_config()
        
        if success:
            print("\n✅ 配置更新成功！")
            print("💡 提示:")
            print("   - 打包后的程序现在使用你的当前配置")
            print("   - 用户首次运行时会看到你设置的参数")
            print("   - 如需重新打包，配置会自动包含")
        else:
            print("\n❌ 配置更新失败")
            print("💡 建议:")
            print("   - 检查dist目录是否存在")
            print("   - 确保已经成功打包过程序")
        
        input("\n按Enter键退出...")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n💥 程序出错: {e}")

if __name__ == "__main__":
    main()
