#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行时任务统计管理器
========================================
功能描述: 管理本次程序运行期间的所有任务统计信息
主要功能:
- 记录任务完成/失败数量
- 计算成功率
- 提供统计查询接口
- 发送统计更新信号
注意事项: 
- 只在内存中存储，程序重启后重置
- 线程安全的统计操作
- 实时更新UI显示
========================================
"""

import threading
from dataclasses import dataclass
from typing import Dict, Any
from PyQt6.QtCore import QObject, pyqtSignal
from core.logger_manager import log_info, log_error


@dataclass
class TaskStatistics:
    """任务统计数据结构"""
    total_tasks: int = 0          # 总任务数
    completed_tasks: int = 0      # 成功完成的任务数
    failed_tasks: int = 0         # 失败的任务数
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.total_tasks == 0:
            return 0.0
        return (self.completed_tasks / self.total_tasks) * 100
    
    @property
    def failure_rate(self) -> float:
        """计算失败率"""
        if self.total_tasks == 0:
            return 0.0
        return (self.failed_tasks / self.total_tasks) * 100


class RuntimeTaskStatisticsManager(QObject):
    """运行时任务统计管理器"""
    
    # 统计更新信号
    statistics_updated = pyqtSignal(dict)  # 发送统计数据字典
    
    def __init__(self):
        super().__init__()
        
        # 线程安全锁
        self._lock = threading.Lock()
        
        # 统计数据
        self._stats = TaskStatistics()
        
        # 任务类型统计（可扩展）
        self._task_type_stats: Dict[str, TaskStatistics] = {
            'instagram_dm': TaskStatistics(),      # Instagram私信任务
            'instagram_follow': TaskStatistics(),  # Instagram关注任务
        }
        
        log_info("运行时任务统计管理器初始化完成", component="RuntimeTaskStatistics")
    
    def record_task_completed(self, task_type: str = "instagram_dm", emulator_id: int = None):
        """记录任务完成"""
        with self._lock:
            # 更新总统计
            self._stats.total_tasks += 1
            self._stats.completed_tasks += 1
            
            # 更新任务类型统计
            if task_type in self._task_type_stats:
                type_stats = self._task_type_stats[task_type]
                type_stats.total_tasks += 1
                type_stats.completed_tasks += 1
            
            # 记录日志
            emulator_info = f"模拟器{emulator_id}" if emulator_id else "未知模拟器"
            log_info(f"任务完成记录: {emulator_info} - {task_type} | 总计: {self._stats.total_tasks}, 成功: {self._stats.completed_tasks}", 
                    component="RuntimeTaskStatistics")
            
            # 发送更新信号
            self._emit_statistics_update()
    
    def record_task_failed(self, task_type: str = "instagram_dm", emulator_id: int = None, error: str = ""):
        """记录任务失败"""
        with self._lock:
            # 更新总统计
            self._stats.total_tasks += 1
            self._stats.failed_tasks += 1
            
            # 更新任务类型统计
            if task_type in self._task_type_stats:
                type_stats = self._task_type_stats[task_type]
                type_stats.total_tasks += 1
                type_stats.failed_tasks += 1
            
            # 记录日志
            emulator_info = f"模拟器{emulator_id}" if emulator_id else "未知模拟器"
            error_info = f" - 错误: {error}" if error else ""
            log_info(f"任务失败记录: {emulator_info} - {task_type}{error_info} | 总计: {self._stats.total_tasks}, 失败: {self._stats.failed_tasks}", 
                    component="RuntimeTaskStatistics")
            
            # 发送更新信号
            self._emit_statistics_update()
    
    def get_overall_statistics(self) -> Dict[str, Any]:
        """获取总体统计信息"""
        with self._lock:
            return {
                'total_tasks': self._stats.total_tasks,
                'completed_tasks': self._stats.completed_tasks,
                'failed_tasks': self._stats.failed_tasks,
                'success_rate': self._stats.success_rate,
                'failure_rate': self._stats.failure_rate
            }
    
    def get_task_type_statistics(self, task_type: str) -> Dict[str, Any]:
        """获取特定任务类型的统计信息"""
        with self._lock:
            if task_type not in self._task_type_stats:
                return {
                    'total_tasks': 0,
                    'completed_tasks': 0,
                    'failed_tasks': 0,
                    'success_rate': 0.0,
                    'failure_rate': 0.0
                }
            
            stats = self._task_type_stats[task_type]
            return {
                'total_tasks': stats.total_tasks,
                'completed_tasks': stats.completed_tasks,
                'failed_tasks': stats.failed_tasks,
                'success_rate': stats.success_rate,
                'failure_rate': stats.failure_rate
            }
    
    def get_all_statistics(self) -> Dict[str, Any]:
        """获取所有统计信息"""
        with self._lock:
            result = {
                'overall': self.get_overall_statistics(),
                'by_type': {}
            }
            
            for task_type in self._task_type_stats:
                result['by_type'][task_type] = self.get_task_type_statistics(task_type)
            
            return result
    
    def reset_statistics(self):
        """重置所有统计信息"""
        with self._lock:
            self._stats = TaskStatistics()
            for task_type in self._task_type_stats:
                self._task_type_stats[task_type] = TaskStatistics()
            
            log_info("运行时任务统计已重置", component="RuntimeTaskStatistics")
            self._emit_statistics_update()
    
    def _emit_statistics_update(self):
        """发送统计更新信号"""
        try:
            stats_data = self.get_all_statistics()
            self.statistics_updated.emit(stats_data)
        except Exception as e:
            log_error(f"发送统计更新信号失败: {e}", component="RuntimeTaskStatistics")


# 全局统计管理器实例
_runtime_statistics_manager = None

def get_runtime_statistics_manager() -> RuntimeTaskStatisticsManager:
    """获取全局运行时统计管理器实例"""
    global _runtime_statistics_manager
    if _runtime_statistics_manager is None:
        _runtime_statistics_manager = RuntimeTaskStatisticsManager()
    return _runtime_statistics_manager
