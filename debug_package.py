# -*- coding: utf-8 -*-
"""
🔍 调试打包问题
功能：检查为什么img文件夹没有被正确打包
"""

import os
import sys
import subprocess
from pathlib import Path

def check_img_directory():
    """检查img目录"""
    print("🔍 检查img目录")
    print("=" * 40)
    
    project_root = Path(__file__).parent
    img_dir = project_root / "img"
    
    if not img_dir.exists():
        print("❌ img目录不存在")
        return False
    
    print(f"✅ img目录存在: {img_dir}")
    
    # 列出所有图片文件
    png_files = list(img_dir.glob("*.png"))
    print(f"📸 找到 {len(png_files)} 个PNG文件:")
    for png_file in png_files:
        size_kb = png_file.stat().st_size / 1024
        print(f"  📷 {png_file.name} ({size_kb:.1f}KB)")
    
    return True

def test_pyinstaller_command():
    """测试PyInstaller命令"""
    print("\n🔍 测试PyInstaller命令")
    print("=" * 40)
    
    project_root = Path(__file__).parent
    
    # 构建命令
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed", 
        "--name=test_img_package",
        "--add-data=img;img",
        "--clean",  # 清理缓存
        "main.py"
    ]
    
    print("🔨 执行命令:")
    print(" ".join(cmd))
    
    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        print(f"\n📊 返回码: {result.returncode}")
        
        if result.stdout:
            print("📝 标准输出:")
            print(result.stdout[-1000:])  # 显示最后1000字符
        
        if result.stderr:
            print("⚠️  错误输出:")
            print(result.stderr[-1000:])  # 显示最后1000字符
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 命令执行失败: {e}")
        return False

def check_dist_after_test():
    """检查测试打包后的dist目录"""
    print("\n🔍 检查测试打包结果")
    print("=" * 40)
    
    project_root = Path(__file__).parent
    dist_dir = project_root / "dist"
    
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    print(f"✅ dist目录存在: {dist_dir}")
    
    # 检查是否有img目录
    dist_img = dist_dir / "img"
    if dist_img.exists():
        print(f"✅ img目录已打包: {dist_img}")
        
        # 列出打包的图片
        png_files = list(dist_img.glob("*.png"))
        print(f"📸 打包了 {len(png_files)} 个PNG文件:")
        for png_file in png_files:
            size_kb = png_file.stat().st_size / 1024
            print(f"  📷 {png_file.name} ({size_kb:.1f}KB)")
        
        return True
    else:
        print("❌ img目录未被打包")
        
        # 列出dist目录内容
        items = list(dist_dir.iterdir())
        print(f"📁 dist目录内容 ({len(items)}个项目):")
        for item in items:
            if item.is_file():
                size_mb = item.stat().st_size / (1024 * 1024)
                print(f"  📄 {item.name} ({size_mb:.1f}MB)")
            else:
                print(f"  📁 {item.name}/")
        
        return False

def check_current_working_directory():
    """检查当前工作目录"""
    print("\n🔍 检查工作目录")
    print("=" * 40)
    
    cwd = Path.cwd()
    script_dir = Path(__file__).parent
    
    print(f"当前工作目录: {cwd}")
    print(f"脚本所在目录: {script_dir}")
    
    if cwd != script_dir:
        print("⚠️  工作目录与脚本目录不同，可能影响相对路径")
        return False
    else:
        print("✅ 工作目录正确")
        return True

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 解决方案建议")
    print("=" * 40)
    
    print("🎯 可能的解决方案:")
    print("1. 使用绝对路径:")
    print("   --add-data=E:\\VScodexiangmu\\insleidian2\\img;img")
    
    print("\n2. 检查路径分隔符:")
    print("   Windows: --add-data=img;img")
    print("   Linux/Mac: --add-data=img:img")
    
    print("\n3. 使用spec文件:")
    print("   创建.spec文件，明确指定datas参数")
    
    print("\n4. 检查权限:")
    print("   确保img目录有读取权限")
    
    print("\n5. 清理缓存:")
    print("   删除build和dist目录，重新打包")

def main():
    """主函数"""
    print("🔍 调试打包问题 - img文件夹缺失")
    print("=" * 50)
    
    # 执行检查步骤
    steps = [
        ("检查img目录", check_img_directory),
        ("检查工作目录", check_current_working_directory),
        ("测试PyInstaller", test_pyinstaller_command),
        ("检查打包结果", check_dist_after_test),
    ]
    
    results = []
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        success = step_func()
        results.append((step_name, success))
    
    # 显示结果总结
    print("\n" + "=" * 50)
    print("📊 检查结果总结:")
    for step_name, success in results:
        status = "✅" if success else "❌"
        print(f"  {status} {step_name}")
    
    # 建议解决方案
    suggest_solutions()
    
    input("\n按Enter键退出...")

if __name__ == "__main__":
    main()
