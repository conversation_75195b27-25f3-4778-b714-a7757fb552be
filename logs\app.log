2025-07-28 08:04:35 - root - INFO - 简化日志系统初始化完成
2025-07-28 08:04:35 - main - INFO - 应用程序启动
2025-07-28 08:04:35 - __main__ - INFO - Qt应用程序已创建
2025-07-28 08:04:35 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 08:04:35 - __main__ - INFO - 统一配置管理器已创建
2025-07-28 08:04:35 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-28 08:04:35 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-28 08:04:35 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-28 08:04:35 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-28 08:04:35 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-28 08:04:35 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-28 08:04:35 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-28 08:04:35 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-28 08:04:35 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-28 08:04:35 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-28 08:04:35 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-28 08:04:35 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 08:04:35 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-28 08:04:35 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-28 08:04:35 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-28 08:04:35 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-28 08:04:35 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-28 08:04:35 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-28 08:04:35 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-28 08:04:35 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-28 08:04:35 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-28 08:04:35 - __main__ - INFO - UI主窗口已创建
2025-07-28 08:04:35 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-28 08:04:36 - __main__ - INFO - 主窗口已显示
2025-07-28 08:04:36 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-28 08:04:36 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-28 08:04:36 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-28 08:04:36 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-28 08:04:36 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 08:04:36 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-28 08:04:36 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 08:04:36 - __main__ - INFO - UI层和业务层已连接
2025-07-28 08:04:36 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 08:04:36 - __main__ - INFO - 启动Qt事件循环
2025-07-28 08:04:36 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 08:04:36 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 08:04:36 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-28 08:04:36 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 08:04:36 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 08:04:36 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 08:04:36 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 08:04:36 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 08:04:36 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-28 08:04:36 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 08:04:36 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 08:04:36 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 08:04:36 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 08:04:36 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.26s | count: 1229
2025-07-28 08:04:36 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 08:04:36 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 08:04:36 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 08:04:36 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 08:04:36 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-28 08:04:36 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 08:04:36 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 08:04:36 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 08:04:36 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 08:04:36 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.34s | count: 1229
2025-07-28 08:04:36 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 08:04:36 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 08:04:36 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 08:04:36 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 08:04:37 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-28 08:04:37 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-28 08:04:37 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-28 08:04:37 - __main__ - INFO - 后台服务已启动
2025-07-28 08:04:37 - __main__ - INFO - 延迟启动服务完成
2025-07-28 08:04:45 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 666 -> 1
2025-07-28 08:04:45 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 08:04:45 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 08:04:45 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 1
2025-07-28 08:04:46 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 1 -> 13
2025-07-28 08:04:46 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 08:04:46 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 08:04:46 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 13
2025-07-28 08:04:47 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-28 08:04:47 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-28 08:04:47 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 08:04:47 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-28 08:04:50 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_direct_task
2025-07-28 08:04:50 - MainWindowV2 - INFO - Instagram直接关注任务已启动，涉及5个模拟器
2025-07-28 08:04:50 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram直接关注任务请求
2025-07-28 08:04:50 - MainWindowV2 - INFO - 用户启动Instagram直接关注任务，模拟器数量: 5
2025-07-28 08:04:50 - FixedAsyncBridge - INFO - 开始处理Instagram直接关注任务（线程池模式），模拟器数量: 5
2025-07-28 08:04:50 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [3, 4, 5, 6, 7]
2025-07-28 08:04:50 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 5
2025-07-28 08:04:50 - StartupManager - INFO - 批量启动请求 | count: 5
2025-07-28 08:04:50 - StartupManager - INFO - 启动调度器已启动
2025-07-28 08:04:50 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-28 08:04:50 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-28 08:04:50 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-28 08:04:50 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 2
2025-07-28 08:04:50 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: direct
2025-07-28 08:04:50 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 5
2025-07-28 08:04:50 - InstagramFollowTaskThread - INFO - [模拟器3] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 08:04:50 - InstagramFollowTaskManager - INFO - 为模拟器 3 创建Instagram关注任务线程，模式: direct
2025-07-28 08:04:50 - InstagramFollowTaskThread - INFO - [模拟器4] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 08:04:50 - InstagramFollowTaskManager - INFO - 为模拟器 4 创建Instagram关注任务线程，模式: direct
2025-07-28 08:04:50 - InstagramFollowTaskThread - INFO - [模拟器5] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 08:04:50 - InstagramFollowTaskManager - INFO - 为模拟器 5 创建Instagram关注任务线程，模式: direct
2025-07-28 08:04:50 - InstagramFollowTaskThread - INFO - [模拟器6] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 08:04:50 - InstagramFollowTaskManager - INFO - 为模拟器 6 创建Instagram关注任务线程，模式: direct
2025-07-28 08:04:50 - InstagramFollowTaskThread - INFO - [模拟器7] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 08:04:50 - MainWindowV2 - INFO - 5个模拟器: 未知 -> 排队中
2025-07-28 08:04:50 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 5 | queued: 5 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 2
2025-07-28 08:04:50 - InstagramFollowTaskManager - INFO - 为模拟器 7 创建Instagram关注任务线程，模式: direct
2025-07-28 08:04:50 - MainWindowV2 - INFO - 启动进度: 排队5个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/2)
2025-07-28 08:04:50 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建5个Instagram线程
2025-07-28 08:04:50 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-28 08:04:50 - FixedAsyncBridge - INFO - 异步桥接器: Instagram直接关注任务请求已处理，状态: started
2025-07-28 08:04:50 - MainWindowV2 - INFO - 批量启动完成: 0/5 成功
2025-07-28 08:04:50 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-28 08:04:50 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 5
2025-07-28 08:04:50 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-28 08:04:50 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 3
2025-07-28 08:04:50 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_direct_task
2025-07-28 08:04:50 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 排队中 | new_state: 启动中
2025-07-28 08:04:50 - MainWindowV2 - INFO - 模拟器3: 排队中 -> 启动中
2025-07-28 08:04:50 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 5 | queued: 4 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 2
2025-07-28 08:04:50 - MainWindowV2 - INFO - 启动进度: 排队4个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/2)
2025-07-28 08:04:51 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 3
2025-07-28 08:04:56 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-28 08:04:56 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 排队中 | new_state: 启动中
2025-07-28 08:04:56 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-28 08:04:56 - MainWindowV2 - INFO - 模拟器4: 排队中 -> 启动中
2025-07-28 08:04:56 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 4
2025-07-28 08:04:56 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 5 | queued: 3 | starting: 2 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 08:04:56 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中2个, 运行0个, 失败0个 (并发槽位:2/2)
2025-07-28 08:04:56 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 4
2025-07-28 08:05:04 - Emulator - INFO - Android系统启动完成 | emulator_id: 3 | elapsed_time: 13.3秒
2025-07-28 08:05:04 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 启动中 -> 运行中
2025-07-28 08:05:04 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 启动中 | new_state: 运行中
2025-07-28 08:05:04 - InstagramFollowTaskManager - INFO - 启动模拟器3的Instagram关注任务线程 - 当前并发: 1/2
2025-07-28 08:05:04 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已添加到任务活动监控，失败计数: 0
2025-07-28 08:05:04 - Emulator - INFO - 模拟器启动成功 | emulator_id: 3 | running_count: 1
2025-07-28 08:05:04 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务start
2025-07-28 08:05:04 - MainWindowV2 - INFO - 模拟器3启动成功
2025-07-28 08:05:04 - InstagramTaskThread - INFO - [模拟器3] 开始等待启动完成
2025-07-28 08:05:04 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 08:05:04 - InstagramTaskThread - INFO - [模拟器3] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 08:05:04 - WindowArrangementManager - INFO - 模拟器3启动完成，立即触发窗口排列
2025-07-28 08:05:04 - InstagramTaskThread - INFO - [模拟器3] 开始窗口排列
2025-07-28 08:05:04 - MainWindowV2 - WARNING - 未找到模拟器3，无法更新状态
2025-07-28 08:05:04 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-28 08:05:04 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 5 | queued: 3 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 20.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 08:05:04 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/2)
2025-07-28 08:05:04 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中, PID: 23908
2025-07-28 08:05:04 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-28 08:05:06 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 08:05:06 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 08:05:06 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 08:05:06 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 08:05:06 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 08:05:06 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 08:05:06 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 08:05:06 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 08:05:06 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 08:05:06 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 08:05:06 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 08:05:06 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 08:05:06 - Emulator - INFO - Android系统启动完成 | emulator_id: 4 | elapsed_time: 10.0秒
2025-07-28 08:05:06 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 启动中 -> 运行中
2025-07-28 08:05:06 - InstagramFollowTaskManager - INFO - 启动模拟器4的Instagram关注任务线程 - 当前并发: 2/2
2025-07-28 08:05:06 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 启动中 | new_state: 运行中
2025-07-28 08:05:06 - Emulator - INFO - 模拟器启动成功 | emulator_id: 4 | running_count: 2
2025-07-28 08:05:06 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已添加到任务活动监控，失败计数: 0
2025-07-28 08:05:06 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务start
2025-07-28 08:05:06 - InstagramTaskThread - INFO - [模拟器4] 开始等待启动完成
2025-07-28 08:05:06 - MainWindowV2 - INFO - 模拟器4启动成功
2025-07-28 08:05:06 - InstagramTaskThread - INFO - [模拟器4] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 08:05:06 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 08:05:06 - InstagramTaskThread - INFO - [模拟器4] 开始窗口排列
2025-07-28 08:05:06 - WindowArrangementManager - INFO - 模拟器4启动完成，立即触发窗口排列
2025-07-28 08:05:06 - MainWindowV2 - WARNING - 未找到模拟器4，无法更新状态
2025-07-28 08:05:06 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-28 08:05:06 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 5 | queued: 3 | starting: 0 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 40.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 08:05:06 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中0个, 运行2个, 失败0个 (并发槽位:2/2)
2025-07-28 08:05:06 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中, PID: 4444
2025-07-28 08:05:06 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-28 08:05:08 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 08:05:08 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 08:05:08 - WindowArrangementManager - INFO - 窗口扫描完成，找到 2 个运行中的模拟器窗口
2025-07-28 08:05:08 - WindowArrangementManager - INFO - 找到 2 个正在运行的模拟器窗口
2025-07-28 08:05:08 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 08:05:08 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 08:05:08 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 08:05:08 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已在网格位置 (0, 0)，保持不动
2025-07-28 08:05:08 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 08:05:08 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 08:05:08 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已排列到网格位置 (0, 1): (302, 0) 保持原大小: 302x435
2025-07-28 08:05:08 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/2 个窗口
2025-07-28 08:05:08 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/2 个窗口 (成功: 1/2)
2025-07-28 08:05:12 - InstagramTaskThread - INFO - [模拟器3] 窗口排列完成
2025-07-28 08:05:12 - InstagramFollowTaskThread - INFO - [模拟器3] 开始执行Instagram直接关注任务
2025-07-28 08:05:12 - InstagramDMTask - INFO - [模拟器3] 雷电模拟器API初始化成功
2025-07-28 08:05:12 - InstagramDMTask - INFO - [模拟器3] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 08:05:12 - InstagramDMTask - INFO - [模拟器3] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 08:05:12 - InstagramDMTask - INFO - [模拟器3] 已设置ld.emulator_id = 3
2025-07-28 08:05:12 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 08:05:12 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置热加载观察者已注册
2025-07-28 08:05:12 - InstagramDMTask - INFO - [模拟器3] Instagram私信任务执行器初始化完成
2025-07-28 08:05:12 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务配置加载完成
2025-07-28 08:05:12 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务执行器初始化完成
2025-07-28 08:05:12 - InstagramFollowTask - INFO - [模拟器3] 关注模式已设置为: direct
2025-07-28 08:05:12 - InstagramFollowTask - INFO - [模拟器3] 开始执行Instagram关注任务
2025-07-28 08:05:12 - InstagramFollowTask - WARNING - [模拟器3] 任务开始时间未由线程传递，在此设置
2025-07-28 08:05:12 - InstagramFollowTask - INFO - [模拟器3] 任务超时设置: 13秒，已运行: 0.00秒
2025-07-28 08:05:12 - InstagramDMTask - INFO - [模拟器3] 模拟器Android系统运行正常，桌面稳定
2025-07-28 08:05:12 - InstagramDMTask - INFO - [模拟器3] 开始检查应用安装状态
2025-07-28 08:05:12 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 08:05:12 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 08:05:12 - InstagramDMTask - INFO - [模拟器3] 📊 应用安装状态检测结果:
2025-07-28 08:05:12 - InstagramDMTask - INFO - [模拟器3] ✅ 所有必要应用已安装
2025-07-28 08:05:12 - InstagramDMTask - INFO - [模拟器3] 开始启动V2Ray应用
2025-07-28 08:05:13 - InstagramDMTask - INFO - [模拟器3] V2Ray启动命令执行成功，等待应用加载
2025-07-28 08:05:13 - InstagramDMTask - INFO - [模拟器3] V2Ray应用启动结果: 成功
2025-07-28 08:05:14 - InstagramTaskThread - INFO - [模拟器4] 窗口排列完成
2025-07-28 08:05:14 - InstagramFollowTaskThread - INFO - [模拟器4] 开始执行Instagram直接关注任务
2025-07-28 08:05:14 - InstagramDMTask - INFO - [模拟器4] 雷电模拟器API初始化成功
2025-07-28 08:05:14 - InstagramDMTask - INFO - [模拟器4] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 08:05:14 - InstagramDMTask - INFO - [模拟器4] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 08:05:14 - InstagramDMTask - INFO - [模拟器4] 已设置ld.emulator_id = 4
2025-07-28 08:05:14 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 08:05:14 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置热加载观察者已注册
2025-07-28 08:05:14 - InstagramDMTask - INFO - [模拟器4] Instagram私信任务执行器初始化完成
2025-07-28 08:05:14 - InstagramFollowTask - INFO - [模拟器4] Instagram关注任务配置加载完成
2025-07-28 08:05:14 - InstagramFollowTask - INFO - [模拟器4] Instagram关注任务执行器初始化完成
2025-07-28 08:05:14 - InstagramFollowTask - INFO - [模拟器4] 关注模式已设置为: direct
2025-07-28 08:05:14 - InstagramFollowTask - INFO - [模拟器4] 开始执行Instagram关注任务
2025-07-28 08:05:14 - InstagramFollowTask - WARNING - [模拟器4] 任务开始时间未由线程传递，在此设置
2025-07-28 08:05:14 - InstagramFollowTask - INFO - [模拟器4] 任务超时设置: 13秒，已运行: 0.00秒
2025-07-28 08:05:14 - InstagramDMTask - INFO - [模拟器4] 模拟器Android系统运行正常，桌面稳定
2025-07-28 08:05:14 - InstagramDMTask - INFO - [模拟器4] 开始检查应用安装状态
2025-07-28 08:05:14 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 08:05:14 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-28 08:05:14 - InstagramDMTask - INFO - [模拟器4] 📊 应用安装状态检测结果:
2025-07-28 08:05:14 - InstagramDMTask - INFO - [模拟器4] ✅ 所有必要应用已安装
2025-07-28 08:05:14 - InstagramDMTask - INFO - [模拟器4] 开始启动V2Ray应用
2025-07-28 08:05:15 - InstagramDMTask - INFO - [模拟器4] V2Ray启动命令执行成功，等待应用加载
2025-07-28 08:05:15 - InstagramDMTask - INFO - [模拟器4] V2Ray应用启动结果: 成功
2025-07-28 08:05:15 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 08:05:15 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:05:15 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 08:05:15 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 08:05:15 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 08:05:15 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 08:05:15 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:05:16 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray应用启动成功
2025-07-28 08:05:16 - InstagramDMTask - INFO - [模拟器3] 开始检查V2Ray节点列表状态
2025-07-28 08:05:17 - InstagramDMTask - INFO - [模拟器3] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 08:05:17 - InstagramDMTask - INFO - [模拟器3] 开始连接V2Ray节点
2025-07-28 08:05:18 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray应用启动成功
2025-07-28 08:05:18 - InstagramDMTask - INFO - [模拟器4] 开始检查V2Ray节点列表状态
2025-07-28 08:05:18 - InstagramDMTask - INFO - [模拟器3] 当前连接状态: 未连接
2025-07-28 08:05:18 - InstagramDMTask - INFO - [模拟器3] V2Ray节点未连接，开始连接
2025-07-28 08:05:19 - InstagramDMTask - INFO - [模拟器4] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 08:05:19 - InstagramDMTask - INFO - [模拟器4] 开始连接V2Ray节点
2025-07-28 08:05:20 - InstagramDMTask - INFO - [模拟器3] 已点击连接按钮，等待连接完成
2025-07-28 08:05:20 - InstagramDMTask - INFO - [模拟器4] 当前连接状态: 未连接
2025-07-28 08:05:20 - InstagramDMTask - INFO - [模拟器4] V2Ray节点未连接，开始连接
2025-07-28 08:05:21 - InstagramDMTask - INFO - [模拟器4] 已点击连接按钮，等待连接完成
2025-07-28 08:05:22 - InstagramDMTask - INFO - [模拟器3] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 08:05:22 - InstagramDMTask - INFO - [模拟器3] V2Ray节点连接成功
2025-07-28 08:05:22 - InstagramDMTask - INFO - [模拟器3] 开始测试V2Ray节点延迟
2025-07-28 08:05:22 - InstagramDMTask - INFO - [模拟器3] 开始V2Ray节点延迟测试
2025-07-28 08:05:23 - InstagramDMTask - INFO - [模拟器3] 当前测试状态: 已连接，点击测试连接
2025-07-28 08:05:23 - InstagramDMTask - INFO - [模拟器3] 点击开始延迟测试
2025-07-28 08:05:23 - InstagramDMTask - INFO - [模拟器3] 已点击测试按钮，等待测试结果
2025-07-28 08:05:24 - InstagramDMTask - INFO - [模拟器4] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 08:05:24 - InstagramDMTask - INFO - [模拟器4] V2Ray节点连接成功
2025-07-28 08:05:24 - InstagramDMTask - INFO - [模拟器4] 开始测试V2Ray节点延迟
2025-07-28 08:05:24 - InstagramDMTask - INFO - [模拟器4] 开始V2Ray节点延迟测试
2025-07-28 08:05:25 - InstagramDMTask - INFO - [模拟器3] 测试状态监控 (1/30): 失败：: io: read/write on closed pipe
2025-07-28 08:05:25 - InstagramDMTask - ERROR - [模拟器3] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: io: read/write on closed pipe
2025-07-28 08:05:25 - InstagramDMTask - INFO - [模拟器3] 点击失败状态重置UI
2025-07-28 08:05:25 - InstagramDMTask - INFO - [模拟器3] 已点击失败状态，等待UI重置
2025-07-28 08:05:25 - InstagramDMTask - INFO - [模拟器4] 当前测试状态: 已连接，点击测试连接
2025-07-28 08:05:25 - InstagramDMTask - INFO - [模拟器4] 点击开始延迟测试
2025-07-28 08:05:25 - InstagramDMTask - INFO - [模拟器4] 已点击测试按钮，等待测试结果
2025-07-28 08:05:25 - InstagramDMTask - INFO - [模拟器3] 继续等待测试结果 (1/3)
2025-07-28 08:05:26 - InstagramDMTask - INFO - [模拟器4] 测试状态监控 (1/30): 测试中…
2025-07-28 08:05:26 - InstagramDMTask - INFO - [模拟器4] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:05:27 - InstagramDMTask - INFO - [模拟器3] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:05:28 - InstagramDMTask - INFO - [模拟器4] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:05:28 - InstagramDMTask - INFO - [模拟器3] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:05:29 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray节点延迟测试成功: 连接成功：延时 3256 毫秒
2025-07-28 08:05:29 - InstagramDMTask - INFO - [模拟器3] 等待5秒后进入下一阶段
2025-07-28 08:05:30 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray节点延迟测试成功: 连接成功：延时 3557 毫秒
2025-07-28 08:05:30 - InstagramDMTask - INFO - [模拟器4] 等待5秒后进入下一阶段
2025-07-28 08:05:34 - InstagramDMTask - INFO - [模拟器3] 开始启动Instagram应用
2025-07-28 08:05:34 - InstagramDMTask - INFO - [模拟器3] Instagram启动命令执行成功，等待应用加载
2025-07-28 08:05:35 - InstagramDMTask - INFO - [模拟器4] 开始启动Instagram应用
2025-07-28 08:05:35 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 08:05:35 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:05:35 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 08:05:35 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 08:05:35 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 08:05:35 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 08:05:35 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:05:35 - InstagramDMTask - INFO - [模拟器4] Instagram启动命令执行成功，等待应用加载
2025-07-28 08:05:37 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram应用启动命令执行完成
2025-07-28 08:05:37 - InstagramDMTask - INFO - [模拟器3] Instagram启动检测 第1/5次
2025-07-28 08:05:37 - InstagramDMTask - INFO - [模拟器3] 总任务超时(13秒)，Instagram启动检测中断，耗时: 25.40秒
2025-07-28 08:05:37 - InstagramDMTask - ERROR - [模拟器3] 检测到异常状态: 异常-总任务超时
2025-07-28 08:05:37 - LeiDianNativeAPI - INFO - 雷电原生API初始化成功: G:/leidian/LDPlayer9
2025-07-28 08:05:37 - NativeScreenshotEngine - INFO - 专业截图引擎初始化成功
2025-07-28 08:05:37 - ScreenshotManager - INFO - 原生截图引擎初始化成功
2025-07-28 08:05:37 - NativeScreenshotEngine - INFO - 开始截取模拟器 3 的截图
2025-07-28 08:05:38 - NativeScreenshotEngine - INFO - 模拟器 3 截图成功: test_screenshots\emulator_3_Instagram_总任务超时_1_20250728_080537.png
2025-07-28 08:05:38 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_3_Instagram_总任务超时_1_20250728_080537.png
2025-07-28 08:05:38 - InstagramDMTask - INFO - [模拟器3] 异常状态截图已保存: 异常-总任务超时
2025-07-28 08:05:38 - InstagramDMTask - INFO - [模拟器3] 任务终止原因: 异常-总任务超时
2025-07-28 08:05:38 - InstagramDMTask - INFO - [模拟器3] 开始关闭模拟器...
2025-07-28 08:05:38 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 3 | remaining_running: 1
2025-07-28 08:05:38 - Emulator - INFO - 模拟器停止成功 | emulator_id: 3
2025-07-28 08:05:38 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务stop
2025-07-28 08:05:38 - InstagramDMTask - INFO - [模拟器3] 模拟器已关闭
2025-07-28 08:05:38 - MainWindowV2 - INFO - 模拟器3停止成功
2025-07-28 08:05:38 - InstagramDMTask - INFO - [模拟器3] 任务状态更新: 失败 - 异常-总任务超时
2025-07-28 08:05:38 - InstagramFollowTask - INFO - [模拟器3] Instagram页面状态检测结果: 任务终止-异常-总任务超时
2025-07-28 08:05:38 - InstagramFollowTask - ERROR - [模拟器3] ❌ Instagram任务终止
2025-07-28 08:05:38 - InstagramFollowTaskThread - ERROR - [模拟器3] Instagram直接关注任务执行失败: 任务终止-异常-总任务超时
2025-07-28 08:05:38 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 08:05:38 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 08:05:38 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram应用启动命令执行完成
2025-07-28 08:05:38 - InstagramDMTask - INFO - [模拟器4] Instagram启动检测 第1/5次
2025-07-28 08:05:38 - InstagramDMTask - INFO - [模拟器4] 总任务超时(13秒)，Instagram启动检测中断，耗时: 23.88秒
2025-07-28 08:05:38 - InstagramDMTask - ERROR - [模拟器4] 检测到异常状态: 异常-总任务超时
2025-07-28 08:05:38 - NativeScreenshotEngine - INFO - 开始截取模拟器 4 的截图
2025-07-28 08:05:38 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 08:05:38 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 排队中 | new_state: 启动中
2025-07-28 08:05:38 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 08:05:38 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 5
2025-07-28 08:05:38 - MainWindowV2 - INFO - 模拟器5: 排队中 -> 启动中
2025-07-28 08:05:38 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 5 | queued: 2 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 20.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 08:05:38 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/2)
2025-07-28 08:05:38 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 5
2025-07-28 08:05:38 - NativeScreenshotEngine - INFO - 模拟器 4 截图成功: test_screenshots\emulator_4_Instagram_总任务超时_1_20250728_080538.png
2025-07-28 08:05:38 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_4_Instagram_总任务超时_1_20250728_080538.png
2025-07-28 08:05:38 - InstagramDMTask - INFO - [模拟器4] 异常状态截图已保存: 异常-总任务超时
2025-07-28 08:05:38 - InstagramDMTask - INFO - [模拟器4] 任务终止原因: 异常-总任务超时
2025-07-28 08:05:38 - InstagramDMTask - INFO - [模拟器4] 开始关闭模拟器...
2025-07-28 08:05:38 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 4 | remaining_running: 0
2025-07-28 08:05:38 - Emulator - INFO - 模拟器停止成功 | emulator_id: 4
2025-07-28 08:05:38 - InstagramDMTask - INFO - [模拟器4] 模拟器已关闭
2025-07-28 08:05:38 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务stop
2025-07-28 08:05:38 - InstagramDMTask - INFO - [模拟器4] 任务状态更新: 失败 - 异常-总任务超时
2025-07-28 08:05:38 - MainWindowV2 - INFO - 模拟器4停止成功
2025-07-28 08:05:38 - InstagramFollowTask - INFO - [模拟器4] Instagram页面状态检测结果: 任务终止-异常-总任务超时
2025-07-28 08:05:38 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 08:05:38 - InstagramFollowTask - ERROR - [模拟器4] ❌ Instagram任务终止
2025-07-28 08:05:38 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 08:05:38 - InstagramFollowTaskThread - ERROR - [模拟器4] Instagram直接关注任务执行失败: 任务终止-异常-总任务超时
2025-07-28 08:05:43 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器6状态变化: 排队中 -> 启动中
2025-07-28 08:05:43 - Emulator - INFO - 模拟器状态变化 | emulator_id: 6 | old_state: 排队中 | new_state: 启动中
2025-07-28 08:05:43 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器6状态变化: 排队中 -> 启动中
2025-07-28 08:05:43 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 6
2025-07-28 08:05:43 - MainWindowV2 - INFO - 模拟器6: 排队中 -> 启动中
2025-07-28 08:05:43 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 5 | queued: 1 | starting: 2 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 08:05:43 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中2个, 运行0个, 失败0个 (并发槽位:2/2)
2025-07-28 08:05:43 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 6
2025-07-28 08:05:49 - Emulator - INFO - Android系统启动完成 | emulator_id: 5 | elapsed_time: 10.4秒
2025-07-28 08:05:49 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-28 08:05:49 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 启动中 | new_state: 运行中
2025-07-28 08:05:49 - InstagramFollowTaskManager - INFO - 启动模拟器5的Instagram关注任务线程 - 当前并发: 3/2
2025-07-28 08:05:49 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已添加到任务活动监控，失败计数: 0
2025-07-28 08:05:49 - Emulator - INFO - 模拟器启动成功 | emulator_id: 5 | running_count: 1
2025-07-28 08:05:49 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务start
2025-07-28 08:05:49 - MainWindowV2 - INFO - 模拟器5启动成功
2025-07-28 08:05:49 - InstagramTaskThread - INFO - [模拟器5] 开始等待启动完成
2025-07-28 08:05:49 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 08:05:49 - InstagramTaskThread - INFO - [模拟器5] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 08:05:49 - InstagramTaskThread - INFO - [模拟器5] 开始窗口排列
2025-07-28 08:05:49 - WindowArrangementManager - INFO - 模拟器5启动完成，立即触发窗口排列
2025-07-28 08:05:49 - MainWindowV2 - WARNING - 未找到模拟器5，无法更新状态
2025-07-28 08:05:49 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 08:05:49 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 5 | queued: 1 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 20.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 08:05:49 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/2)
2025-07-28 08:05:49 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中, PID: 18020
2025-07-28 08:05:49 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 08:05:51 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 08:05:51 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 08:05:51 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 08:05:51 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 08:05:51 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 08:05:51 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 08:05:51 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 08:05:51 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 08:05:51 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 08:05:51 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 08:05:51 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 08:05:51 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 08:05:53 - Emulator - INFO - Android系统启动完成 | emulator_id: 6 | elapsed_time: 10.0秒
2025-07-28 08:05:53 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器6状态变化: 启动中 -> 运行中
2025-07-28 08:05:53 - Emulator - INFO - 模拟器状态变化 | emulator_id: 6 | old_state: 启动中 | new_state: 运行中
2025-07-28 08:05:53 - InstagramFollowTaskManager - INFO - 启动模拟器6的Instagram关注任务线程 - 当前并发: 4/2
2025-07-28 08:05:53 - TaskActivityHeartbeatManager - INFO - 模拟器 6 已添加到任务活动监控，失败计数: 0
2025-07-28 08:05:53 - Emulator - INFO - 模拟器启动成功 | emulator_id: 6 | running_count: 2
2025-07-28 08:05:53 - MainWindowV2 - INFO - 任务完成: 模拟器6, 任务start
2025-07-28 08:05:53 - MainWindowV2 - INFO - 模拟器6启动成功
2025-07-28 08:05:53 - InstagramTaskThread - INFO - [模拟器6] 开始等待启动完成
2025-07-28 08:05:53 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=6, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 08:05:53 - InstagramTaskThread - INFO - [模拟器6] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 08:05:53 - InstagramTaskThread - INFO - [模拟器6] 开始窗口排列
2025-07-28 08:05:53 - WindowArrangementManager - INFO - 模拟器6启动完成，立即触发窗口排列
2025-07-28 08:05:53 - MainWindowV2 - WARNING - 未找到模拟器6，无法更新状态
2025-07-28 08:05:53 - MainWindowV2 - INFO - 模拟器6: 启动中 -> 运行中
2025-07-28 08:05:53 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 5 | queued: 1 | starting: 0 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 40.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 08:05:53 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行2个, 失败0个 (并发槽位:2/2)
2025-07-28 08:05:53 - MainWindowV2 - INFO - 模拟器6: 启动中 -> 运行中, PID: 17088
2025-07-28 08:05:53 - MainWindowV2 - INFO - 模拟器6: 启动中 -> 运行中
2025-07-28 08:05:55 - TaskActivityHeartbeatManager - INFO - 开始检测 4 个模拟器的心跳状态
2025-07-28 08:05:55 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:05:55 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 08:05:55 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 08:05:55 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 08:05:55 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 08:05:55 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-28 08:05:55 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-28 08:05:55 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 6
2025-07-28 08:05:55 - MainWindowV2 - WARNING - 模拟器6心跳状态更新未产生变化
2025-07-28 08:05:55 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:05:55 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 08:05:55 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 08:05:55 - WindowArrangementManager - INFO - 窗口扫描完成，找到 2 个运行中的模拟器窗口
2025-07-28 08:05:55 - WindowArrangementManager - INFO - 找到 2 个正在运行的模拟器窗口
2025-07-28 08:05:55 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 08:05:55 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 08:05:55 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-6' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 08:05:55 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 已在网格位置 (0, 0)，保持不动
2025-07-28 08:05:55 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 08:05:55 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 08:05:55 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-6' 已排列到网格位置 (0, 1): (302, 0) 保持原大小: 302x435
2025-07-28 08:05:55 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/2 个窗口
2025-07-28 08:05:55 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/2 个窗口 (成功: 1/2)
2025-07-28 08:05:57 - InstagramTaskThread - INFO - [模拟器5] 窗口排列完成
2025-07-28 08:05:57 - InstagramFollowTaskThread - INFO - [模拟器5] 开始执行Instagram直接关注任务
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] 雷电模拟器API初始化成功
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] 已设置ld.emulator_id = 5
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置热加载观察者已注册
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] Instagram私信任务执行器初始化完成
2025-07-28 08:05:57 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务配置加载完成
2025-07-28 08:05:57 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务执行器初始化完成
2025-07-28 08:05:57 - InstagramFollowTask - INFO - [模拟器5] 关注模式已设置为: direct
2025-07-28 08:05:57 - InstagramFollowTask - INFO - [模拟器5] 开始执行Instagram关注任务
2025-07-28 08:05:57 - InstagramFollowTask - WARNING - [模拟器5] 任务开始时间未由线程传递，在此设置
2025-07-28 08:05:57 - InstagramFollowTask - INFO - [模拟器5] 任务超时设置: 13秒，已运行: 0.00秒
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] 模拟器Android系统运行正常，桌面稳定
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] 开始检查应用安装状态
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] 📊 应用安装状态检测结果:
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] ✅ 所有必要应用已安装
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] 开始启动V2Ray应用
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] V2Ray启动命令执行成功，等待应用加载
2025-07-28 08:05:57 - InstagramDMTask - INFO - [模拟器5] V2Ray应用启动结果: 成功
2025-07-28 08:06:00 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray应用启动成功
2025-07-28 08:06:00 - InstagramDMTask - INFO - [模拟器5] 开始检查V2Ray节点列表状态
2025-07-28 08:06:01 - InstagramTaskThread - INFO - [模拟器6] 窗口排列完成
2025-07-28 08:06:01 - InstagramFollowTaskThread - INFO - [模拟器6] 开始执行Instagram直接关注任务
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器6] 雷电模拟器API初始化成功
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器6] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器6] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器6] 已设置ld.emulator_id = 6
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器6] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器6] Instagram任务配置热加载观察者已注册
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器6] Instagram私信任务执行器初始化完成
2025-07-28 08:06:01 - InstagramFollowTask - INFO - [模拟器6] Instagram关注任务配置加载完成
2025-07-28 08:06:01 - InstagramFollowTask - INFO - [模拟器6] Instagram关注任务执行器初始化完成
2025-07-28 08:06:01 - InstagramFollowTask - INFO - [模拟器6] 关注模式已设置为: direct
2025-07-28 08:06:01 - InstagramFollowTask - INFO - [模拟器6] 开始执行Instagram关注任务
2025-07-28 08:06:01 - InstagramFollowTask - WARNING - [模拟器6] 任务开始时间未由线程传递，在此设置
2025-07-28 08:06:01 - InstagramFollowTask - INFO - [模拟器6] 任务超时设置: 13秒，已运行: 0.00秒
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器6] 模拟器Android系统运行正常，桌面稳定
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器6] 开始检查应用安装状态
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器6] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器5] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器5] 开始连接V2Ray节点
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器6] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器6] 📊 应用安装状态检测结果:
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器6] ✅ 所有必要应用已安装
2025-07-28 08:06:01 - InstagramDMTask - INFO - [模拟器6] 开始启动V2Ray应用
2025-07-28 08:06:02 - InstagramDMTask - INFO - [模拟器6] V2Ray启动命令执行成功，等待应用加载
2025-07-28 08:06:02 - InstagramDMTask - INFO - [模拟器6] V2Ray应用启动结果: 成功
2025-07-28 08:06:03 - InstagramDMTask - INFO - [模拟器5] 当前连接状态: 未连接
2025-07-28 08:06:03 - InstagramDMTask - INFO - [模拟器5] V2Ray节点未连接，开始连接
2025-07-28 08:06:03 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 08:06:03 - __main__ - INFO - 开始清理资源...
2025-07-28 08:06:03 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 08:06:03 - __main__ - INFO - 配置已保存
2025-07-28 08:06:03 - __main__ - ERROR - 异步桥梁关闭失败: 'FixedAsyncBridge' object has no attribute 'shutdown'
2025-07-28 08:06:03 - __main__ - INFO - 应用程序已退出
