2025-07-28 08:08:53 - root - INFO - 简化日志系统初始化完成
2025-07-28 08:08:53 - main - INFO - 应用程序启动
2025-07-28 08:08:53 - __main__ - INFO - Qt应用程序已创建
2025-07-28 08:08:53 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 08:08:53 - __main__ - INFO - 统一配置管理器已创建
2025-07-28 08:08:53 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-28 08:08:53 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-28 08:08:53 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-28 08:08:53 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-28 08:08:53 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-28 08:08:53 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-28 08:08:53 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-28 08:08:53 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-28 08:08:53 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-28 08:08:53 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-28 08:08:53 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-28 08:08:53 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 08:08:53 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-28 08:08:53 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-28 08:08:53 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-28 08:08:53 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-28 08:08:53 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-28 08:08:53 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-28 08:08:53 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-28 08:08:53 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-28 08:08:53 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-28 08:08:53 - __main__ - INFO - UI主窗口已创建
2025-07-28 08:08:53 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-28 08:08:53 - __main__ - INFO - 主窗口已显示
2025-07-28 08:08:54 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-28 08:08:54 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-28 08:08:54 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-28 08:08:54 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-28 08:08:54 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 08:08:54 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-28 08:08:54 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 08:08:54 - __main__ - INFO - UI层和业务层已连接
2025-07-28 08:08:54 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 08:08:54 - __main__ - INFO - 启动Qt事件循环
2025-07-28 08:08:54 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 08:08:54 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 08:08:54 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-28 08:08:54 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 08:08:54 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 08:08:54 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 08:08:54 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 08:08:54 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 08:08:54 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-28 08:08:54 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 08:08:54 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 08:08:54 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 08:08:54 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 08:08:54 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.25s | count: 1229
2025-07-28 08:08:54 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 08:08:54 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 08:08:54 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 08:08:54 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 08:08:54 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-28 08:08:54 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 08:08:54 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 08:08:54 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 08:08:54 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 08:08:54 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.24s | count: 1229
2025-07-28 08:08:54 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 08:08:54 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 08:08:54 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 08:08:54 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 08:08:55 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-28 08:08:55 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-28 08:08:55 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-28 08:08:55 - __main__ - INFO - 后台服务已启动
2025-07-28 08:08:55 - __main__ - INFO - 延迟启动服务完成
2025-07-28 08:08:57 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_direct_task
2025-07-28 08:08:57 - MainWindowV2 - INFO - Instagram直接关注任务已启动，涉及2个模拟器
2025-07-28 08:08:57 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram直接关注任务请求
2025-07-28 08:08:57 - MainWindowV2 - INFO - 用户启动Instagram直接关注任务，模拟器数量: 2
2025-07-28 08:08:57 - FixedAsyncBridge - INFO - 开始处理Instagram直接关注任务（线程池模式），模拟器数量: 2
2025-07-28 08:08:57 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [25, 26]
2025-07-28 08:08:57 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 2
2025-07-28 08:08:57 - StartupManager - INFO - 批量启动请求 | count: 2
2025-07-28 08:08:57 - StartupManager - INFO - 启动调度器已启动
2025-07-28 08:08:57 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-28 08:08:57 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-28 08:08:57 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-28 08:08:57 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 1
2025-07-28 08:08:57 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: direct
2025-07-28 08:08:57 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 2
2025-07-28 08:08:57 - InstagramFollowTaskThread - INFO - [模拟器25] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 08:08:57 - InstagramFollowTaskManager - INFO - 为模拟器 25 创建Instagram关注任务线程，模式: direct
2025-07-28 08:08:57 - InstagramFollowTaskThread - INFO - [模拟器26] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 08:08:57 - InstagramFollowTaskManager - INFO - 为模拟器 26 创建Instagram关注任务线程，模式: direct
2025-07-28 08:08:57 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建2个Instagram线程
2025-07-28 08:08:57 - FixedAsyncBridge - INFO - 异步桥接器: Instagram直接关注任务请求已处理，状态: started
2025-07-28 08:08:57 - MainWindowV2 - INFO - 2个模拟器: 未知 -> 排队中
2025-07-28 08:08:57 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器25状态变化: 排队中 -> 启动中
2025-07-28 08:08:57 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 2 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 1
2025-07-28 08:08:57 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器25状态变化: 排队中 -> 启动中
2025-07-28 08:08:57 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/1)
2025-07-28 08:08:57 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 25
2025-07-28 08:08:57 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-28 08:08:57 - MainWindowV2 - INFO - 批量启动完成: 0/2 成功
2025-07-28 08:08:57 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 2
2025-07-28 08:08:57 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_direct_task
2025-07-28 08:08:57 - Emulator - INFO - 模拟器状态变化 | emulator_id: 25 | old_state: 排队中 | new_state: 启动中
2025-07-28 08:08:57 - MainWindowV2 - INFO - 模拟器25: 排队中 -> 启动中
2025-07-28 08:08:57 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 1 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 08:08:57 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-28 08:08:58 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 25
2025-07-28 08:09:11 - Emulator - INFO - Android系统启动完成 | emulator_id: 25 | elapsed_time: 13.1秒
2025-07-28 08:09:11 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器25状态变化: 启动中 -> 运行中
2025-07-28 08:09:11 - Emulator - INFO - 模拟器状态变化 | emulator_id: 25 | old_state: 启动中 | new_state: 运行中
2025-07-28 08:09:11 - InstagramFollowTaskManager - INFO - 启动模拟器25的Instagram关注任务线程 - 当前并发: 1/1
2025-07-28 08:09:11 - TaskActivityHeartbeatManager - INFO - 模拟器 25 已添加到任务活动监控，失败计数: 0
2025-07-28 08:09:11 - Emulator - INFO - 模拟器启动成功 | emulator_id: 25 | running_count: 1
2025-07-28 08:09:11 - MainWindowV2 - INFO - 任务完成: 模拟器25, 任务start
2025-07-28 08:09:11 - MainWindowV2 - INFO - 模拟器25启动成功
2025-07-28 08:09:11 - InstagramTaskThread - INFO - [模拟器25] 开始等待启动完成
2025-07-28 08:09:11 - InstagramTaskThread - INFO - [模拟器25] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 08:09:11 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=25, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 08:09:11 - InstagramTaskThread - INFO - [模拟器25] 开始窗口排列
2025-07-28 08:09:11 - WindowArrangementManager - INFO - 模拟器25启动完成，立即触发窗口排列
2025-07-28 08:09:11 - MainWindowV2 - WARNING - 未找到模拟器25，无法更新状态
2025-07-28 08:09:11 - MainWindowV2 - INFO - 模拟器25: 启动中 -> 运行中
2025-07-28 08:09:11 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 1 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 50.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 08:09:11 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-28 08:09:11 - MainWindowV2 - INFO - 模拟器25: 启动中 -> 运行中, PID: 14396
2025-07-28 08:09:11 - MainWindowV2 - INFO - 模拟器25: 启动中 -> 运行中
2025-07-28 08:09:13 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 08:09:13 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 08:09:13 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 08:09:13 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 08:09:13 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 08:09:13 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 582x995
2025-07-28 08:09:13 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-25' 不在网格位置 (当前位置: 1009, 203)，需要排列
2025-07-28 08:09:13 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 08:09:13 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 08:09:13 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-25' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 582x995
2025-07-28 08:09:13 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 08:09:13 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 08:09:13 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 08:09:13 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:09:13 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 25
2025-07-28 08:09:13 - MainWindowV2 - WARNING - 模拟器25心跳状态更新未产生变化
2025-07-28 08:09:13 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:09:19 - InstagramTaskThread - INFO - [模拟器25] 窗口排列完成
2025-07-28 08:09:19 - InstagramFollowTaskThread - INFO - [模拟器25] 开始执行Instagram直接关注任务
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] 雷电模拟器API初始化成功
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] 已设置ld.emulator_id = 25
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] Instagram任务配置热加载观察者已注册
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] Instagram私信任务执行器初始化完成
2025-07-28 08:09:19 - InstagramFollowTask - INFO - [模拟器25] Instagram关注任务配置加载完成
2025-07-28 08:09:19 - InstagramFollowTask - INFO - [模拟器25] Instagram关注任务执行器初始化完成
2025-07-28 08:09:19 - InstagramFollowTask - INFO - [模拟器25] 关注模式已设置为: direct
2025-07-28 08:09:19 - InstagramFollowTask - INFO - [模拟器25] 开始执行Instagram关注任务
2025-07-28 08:09:19 - InstagramFollowTask - WARNING - [模拟器25] 任务开始时间未由线程传递，在此设置
2025-07-28 08:09:19 - InstagramFollowTask - INFO - [模拟器25] 任务超时设置: 777秒，已运行: 0.00秒
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] 模拟器Android系统运行正常，桌面稳定
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] 开始检查应用安装状态
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] 📊 应用安装状态检测结果:
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] ✅ 所有必要应用已安装
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] 开始启动V2Ray应用
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] V2Ray启动命令执行成功，等待应用加载
2025-07-28 08:09:19 - InstagramDMTask - INFO - [模拟器25] V2Ray应用启动结果: 成功
2025-07-28 08:09:22 - InstagramDMTask - INFO - [模拟器25] ✅ V2Ray应用启动成功
2025-07-28 08:09:22 - InstagramDMTask - INFO - [模拟器25] 开始检查V2Ray节点列表状态
2025-07-28 08:09:24 - InstagramDMTask - INFO - [模拟器25] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 08:09:24 - InstagramDMTask - INFO - [模拟器25] 开始连接V2Ray节点
2025-07-28 08:09:25 - InstagramDMTask - INFO - [模拟器25] 当前连接状态: 未连接
2025-07-28 08:09:25 - InstagramDMTask - INFO - [模拟器25] V2Ray节点未连接，开始连接
2025-07-28 08:09:26 - InstagramDMTask - INFO - [模拟器25] 已点击连接按钮，等待连接完成
2025-07-28 08:09:28 - InstagramDMTask - INFO - [模拟器25] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 08:09:28 - InstagramDMTask - INFO - [模拟器25] V2Ray节点连接成功
2025-07-28 08:09:28 - InstagramDMTask - INFO - [模拟器25] 开始测试V2Ray节点延迟
2025-07-28 08:09:28 - InstagramDMTask - INFO - [模拟器25] 开始V2Ray节点延迟测试
2025-07-28 08:09:30 - InstagramDMTask - INFO - [模拟器25] 当前测试状态: 已连接，点击测试连接
2025-07-28 08:09:30 - InstagramDMTask - INFO - [模拟器25] 点击开始延迟测试
2025-07-28 08:09:30 - InstagramDMTask - INFO - [模拟器25] 已点击测试按钮，等待测试结果
2025-07-28 08:09:31 - InstagramDMTask - INFO - [模拟器25] 测试状态监控 (1/30): 测试中…
2025-07-28 08:09:31 - InstagramDMTask - INFO - [模拟器25] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:09:32 - InstagramDMTask - INFO - [模拟器25] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:09:33 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 08:09:33 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:09:33 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 25
2025-07-28 08:09:33 - MainWindowV2 - WARNING - 模拟器25心跳状态更新未产生变化
2025-07-28 08:09:33 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:09:34 - InstagramDMTask - INFO - [模拟器25] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:09:35 - InstagramDMTask - INFO - [模拟器25] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:09:36 - InstagramDMTask - INFO - [模拟器25] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:09:38 - InstagramDMTask - INFO - [模拟器25] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:09:39 - InstagramDMTask - INFO - [模拟器25] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:09:40 - InstagramDMTask - INFO - [模拟器25] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:09:42 - InstagramDMTask - INFO - [模拟器25] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:09:45 - InstagramDMTask - INFO - [模拟器25] 测试状态监控 (2/30): 已连接，点击测试连接
2025-07-28 08:09:52 - InstagramDMTask - INFO - [模拟器25] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:09:53 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 08:09:53 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:09:53 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 25
2025-07-28 08:09:53 - MainWindowV2 - WARNING - 模拟器25心跳状态更新未产生变化
2025-07-28 08:09:53 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:09:53 - InstagramDMTask - INFO - [模拟器25] ✅ V2Ray节点延迟测试成功: 连接成功：延时 1250 毫秒
2025-07-28 08:09:53 - InstagramDMTask - INFO - [模拟器25] 等待5秒后进入下一阶段
2025-07-28 08:09:58 - InstagramDMTask - INFO - [模拟器25] 开始启动Instagram应用
2025-07-28 08:09:58 - InstagramDMTask - INFO - [模拟器25] Instagram启动命令执行成功，等待应用加载
2025-07-28 08:10:01 - InstagramDMTask - INFO - [模拟器25] ✅ Instagram应用启动命令执行完成
2025-07-28 08:10:01 - InstagramDMTask - INFO - [模拟器25] Instagram启动检测 第1/5次
2025-07-28 08:10:06 - InstagramDMTask - INFO - [模拟器25] ❌ 批量验证失败
2025-07-28 08:10:06 - InstagramDMTask - INFO - [模拟器25] ❌ 验证失败
2025-07-28 08:10:08 - InstagramDMTask - INFO - [模拟器25] Instagram启动检测中... 已等待6.3秒
2025-07-28 08:10:10 - InstagramDMTask - INFO - [模拟器25] ❌ 批量验证失败
2025-07-28 08:10:10 - InstagramDMTask - INFO - [模拟器25] ❌ 验证失败
2025-07-28 08:10:10 - InstagramDMTask - ERROR - [模拟器25] 检测到异常状态: 异常-需要登录
2025-07-28 08:10:10 - LeiDianNativeAPI - INFO - 雷电原生API初始化成功: G:/leidian/LDPlayer9
2025-07-28 08:10:10 - NativeScreenshotEngine - INFO - 专业截图引擎初始化成功
2025-07-28 08:10:10 - ScreenshotManager - INFO - 原生截图引擎初始化成功
2025-07-28 08:10:10 - NativeScreenshotEngine - INFO - 开始截取模拟器 25 的截图
2025-07-28 08:10:10 - NativeScreenshotEngine - INFO - 模拟器 25 截图成功: test_screenshots\emulator_25_Instagram_需要登录_1_20250728_081010.png
2025-07-28 08:10:10 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_25_Instagram_需要登录_1_20250728_081010.png
2025-07-28 08:10:10 - InstagramDMTask - INFO - [模拟器25] 异常状态截图已保存: 异常-需要登录
2025-07-28 08:10:10 - InstagramDMTask - INFO - [模拟器25] 任务终止原因: 异常-需要登录
2025-07-28 08:10:10 - InstagramDMTask - INFO - [模拟器25] 开始关闭模拟器...
2025-07-28 08:10:10 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 25 | remaining_running: 0
2025-07-28 08:10:10 - Emulator - INFO - 模拟器停止成功 | emulator_id: 25
2025-07-28 08:10:10 - InstagramDMTask - INFO - [模拟器25] 模拟器已关闭
2025-07-28 08:10:10 - InstagramDMTask - INFO - [模拟器25] 任务状态更新: 失败 - 异常-需要登录
2025-07-28 08:10:10 - InstagramFollowTask - INFO - [模拟器25] Instagram页面状态检测结果: 任务终止-异常-需要登录
2025-07-28 08:10:10 - InstagramFollowTask - ERROR - [模拟器25] ❌ Instagram任务终止
2025-07-28 08:10:10 - InstagramFollowTaskThread - ERROR - [模拟器25] Instagram直接关注任务执行失败: 任务终止-异常-需要登录
2025-07-28 08:10:10 - MainWindowV2 - INFO - 任务完成: 模拟器25, 任务stop
2025-07-28 08:10:10 - MainWindowV2 - INFO - 模拟器25停止成功
2025-07-28 08:10:10 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=25, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 08:10:10 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 08:10:11 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器26状态变化: 排队中 -> 启动中
2025-07-28 08:10:11 - Emulator - INFO - 模拟器状态变化 | emulator_id: 26 | old_state: 排队中 | new_state: 启动中
2025-07-28 08:10:11 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器26状态变化: 排队中 -> 启动中
2025-07-28 08:10:11 - MainWindowV2 - INFO - 模拟器26: 排队中 -> 启动中
2025-07-28 08:10:11 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 26
2025-07-28 08:10:11 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 0 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 08:10:11 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/1)
2025-07-28 08:10:11 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 26
2025-07-28 08:10:13 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 08:10:13 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:10:13 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 25
2025-07-28 08:10:13 - MainWindowV2 - WARNING - 模拟器25心跳状态更新未产生变化
2025-07-28 08:10:13 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:10:20 - Emulator - INFO - Android系统启动完成 | emulator_id: 26 | elapsed_time: 9.8秒
2025-07-28 08:10:20 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器26状态变化: 启动中 -> 运行中
2025-07-28 08:10:20 - Emulator - INFO - 模拟器状态变化 | emulator_id: 26 | old_state: 启动中 | new_state: 运行中
2025-07-28 08:10:20 - InstagramFollowTaskManager - INFO - 启动模拟器26的Instagram关注任务线程 - 当前并发: 2/1
2025-07-28 08:10:20 - TaskActivityHeartbeatManager - INFO - 模拟器 26 已添加到任务活动监控，失败计数: 0
2025-07-28 08:10:20 - Emulator - INFO - 模拟器启动成功 | emulator_id: 26 | running_count: 1
2025-07-28 08:10:20 - MainWindowV2 - INFO - 任务完成: 模拟器26, 任务start
2025-07-28 08:10:20 - MainWindowV2 - INFO - 模拟器26启动成功
2025-07-28 08:10:20 - InstagramTaskThread - INFO - [模拟器26] 开始等待启动完成
2025-07-28 08:10:20 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=26, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 08:10:20 - InstagramTaskThread - INFO - [模拟器26] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 08:10:20 - InstagramTaskThread - INFO - [模拟器26] 开始窗口排列
2025-07-28 08:10:20 - WindowArrangementManager - INFO - 模拟器26启动完成，立即触发窗口排列
2025-07-28 08:10:20 - MainWindowV2 - WARNING - 未找到模拟器26，无法更新状态
2025-07-28 08:10:20 - MainWindowV2 - INFO - 模拟器26: 启动中 -> 运行中
2025-07-28 08:10:20 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 0 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 50.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 08:10:20 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-28 08:10:20 - StartupManager - INFO - 调度器已停止
2025-07-28 08:10:21 - MainWindowV2 - INFO - 模拟器26: 启动中 -> 运行中, PID: 24096
2025-07-28 08:10:21 - MainWindowV2 - INFO - 模拟器26: 启动中 -> 运行中
2025-07-28 08:10:22 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 08:10:22 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 08:10:22 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 08:10:22 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 08:10:22 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 08:10:22 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 08:10:22 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-26' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 08:10:22 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 08:10:22 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 08:10:22 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-26' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 08:10:23 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 08:10:23 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 08:10:28 - InstagramTaskThread - INFO - [模拟器26] 窗口排列完成
2025-07-28 08:10:28 - InstagramFollowTaskThread - INFO - [模拟器26] 开始执行Instagram直接关注任务
2025-07-28 08:10:28 - InstagramDMTask - INFO - [模拟器26] 雷电模拟器API初始化成功
2025-07-28 08:10:28 - InstagramDMTask - INFO - [模拟器26] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 08:10:28 - InstagramDMTask - INFO - [模拟器26] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 08:10:28 - InstagramDMTask - INFO - [模拟器26] 已设置ld.emulator_id = 26
2025-07-28 08:10:28 - InstagramDMTask - INFO - [模拟器26] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 08:10:28 - InstagramDMTask - INFO - [模拟器26] Instagram任务配置热加载观察者已注册
2025-07-28 08:10:28 - InstagramDMTask - INFO - [模拟器26] Instagram私信任务执行器初始化完成
2025-07-28 08:10:28 - InstagramFollowTask - INFO - [模拟器26] Instagram关注任务配置加载完成
2025-07-28 08:10:28 - InstagramFollowTask - INFO - [模拟器26] Instagram关注任务执行器初始化完成
2025-07-28 08:10:28 - InstagramFollowTask - INFO - [模拟器26] 关注模式已设置为: direct
2025-07-28 08:10:28 - InstagramFollowTask - INFO - [模拟器26] 开始执行Instagram关注任务
2025-07-28 08:10:28 - InstagramFollowTask - WARNING - [模拟器26] 任务开始时间未由线程传递，在此设置
2025-07-28 08:10:28 - InstagramFollowTask - INFO - [模拟器26] 任务超时设置: 777秒，已运行: 0.00秒
2025-07-28 08:10:29 - InstagramDMTask - INFO - [模拟器26] 模拟器Android系统运行正常，桌面稳定
2025-07-28 08:10:29 - InstagramDMTask - INFO - [模拟器26] 开始检查应用安装状态
2025-07-28 08:10:29 - InstagramDMTask - INFO - [模拟器26] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 08:10:29 - InstagramDMTask - INFO - [模拟器26] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 08:10:29 - InstagramDMTask - INFO - [模拟器26] 📊 应用安装状态检测结果:
2025-07-28 08:10:29 - InstagramDMTask - INFO - [模拟器26] ✅ 所有必要应用已安装
2025-07-28 08:10:29 - InstagramDMTask - INFO - [模拟器26] 开始启动V2Ray应用
2025-07-28 08:10:29 - InstagramDMTask - INFO - [模拟器26] V2Ray启动命令执行成功，等待应用加载
2025-07-28 08:10:29 - InstagramDMTask - INFO - [模拟器26] V2Ray应用启动结果: 成功
2025-07-28 08:10:32 - InstagramDMTask - INFO - [模拟器26] ✅ V2Ray应用启动成功
2025-07-28 08:10:32 - InstagramDMTask - INFO - [模拟器26] 开始检查V2Ray节点列表状态
2025-07-28 08:10:33 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 08:10:33 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:10:33 - TaskActivityHeartbeatManager - INFO - 模拟器 25 疑似心跳异常，进入观察期 | 无活动时间: 22.3秒
2025-07-28 08:10:33 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 26
2025-07-28 08:10:33 - MainWindowV2 - WARNING - 模拟器26心跳状态更新未产生变化
2025-07-28 08:10:33 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:10:33 - InstagramDMTask - INFO - [模拟器26] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 08:10:33 - InstagramDMTask - INFO - [模拟器26] 开始连接V2Ray节点
2025-07-28 08:10:35 - InstagramDMTask - INFO - [模拟器26] 当前连接状态: 未连接
2025-07-28 08:10:35 - InstagramDMTask - INFO - [模拟器26] V2Ray节点未连接，开始连接
2025-07-28 08:10:36 - InstagramDMTask - INFO - [模拟器26] 已点击连接按钮，等待连接完成
2025-07-28 08:10:38 - InstagramDMTask - INFO - [模拟器26] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 08:10:38 - InstagramDMTask - INFO - [模拟器26] V2Ray节点连接成功
2025-07-28 08:10:38 - InstagramDMTask - INFO - [模拟器26] 开始测试V2Ray节点延迟
2025-07-28 08:10:38 - InstagramDMTask - INFO - [模拟器26] 开始V2Ray节点延迟测试
2025-07-28 08:10:39 - InstagramDMTask - INFO - [模拟器26] 当前测试状态: 已连接，点击测试连接
2025-07-28 08:10:39 - InstagramDMTask - INFO - [模拟器26] 点击开始延迟测试
2025-07-28 08:10:39 - InstagramDMTask - INFO - [模拟器26] 已点击测试按钮，等待测试结果
2025-07-28 08:10:41 - InstagramDMTask - INFO - [模拟器26] 测试状态监控 (1/30): 测试中…
2025-07-28 08:10:41 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:10:42 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:10:43 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:10:45 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:10:46 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:10:47 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:10:49 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:10:50 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:10:51 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:10:53 - InstagramDMTask - ERROR - [模拟器26] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: context deadline exceeded
2025-07-28 08:10:53 - InstagramDMTask - INFO - [模拟器26] 点击失败状态重置UI
2025-07-28 08:10:53 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 08:10:53 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 08:10:53 - TaskActivityHeartbeatManager - ERROR - 模拟器 25 确认心跳异常 | 总无活动时间: 42.3秒
2025-07-28 08:10:53 - TaskActivityHeartbeatManager - INFO - 心跳异常处理开始，失败次数: 1/1 | emulator_id: 25
2025-07-28 08:10:53 - NativeScreenshotEngine - INFO - 开始截取模拟器 25 的截图
2025-07-28 08:10:53 - UnifiedEmulatorManager - INFO - 心跳检测失败: 心跳异常, 失败次数: 1 | emulator_id: 25
2025-07-28 08:10:53 - MainWindowV2 - INFO - 模拟器25心跳异常: 心跳异常 (失败次数: 1)
2025-07-28 08:10:53 - MainWindowV2 - WARNING - 模拟器25心跳检测失败: 心跳异常, 失败次数: 1
2025-07-28 08:10:53 - MainWindowV2 - WARNING - 模拟器25心跳状态更新未产生变化
2025-07-28 08:10:53 - InstagramDMTask - INFO - [模拟器26] 已点击失败状态，等待UI重置
2025-07-28 08:10:53 - NativeScreenshotEngine - ERROR - 模拟器 25 绑定句柄无效: 0
2025-07-28 08:10:53 - ScreenshotManager - ERROR - 原生截图引擎截图失败
2025-07-28 08:10:53 - TaskActivityHeartbeatManager - INFO - 开始熔断处理 | emulator_id: 25
2025-07-28 08:10:53 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器25状态变化: 运行中 -> 异常
2025-07-28 08:10:53 - Emulator - INFO - 模拟器状态变化 | emulator_id: 25 | old_state: 运行中 | new_state: 异常
2025-07-28 08:10:53 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器25状态变化: 运行中 -> 异常
2025-07-28 08:10:53 - InstagramTaskManager - INFO - 清理模拟器25的运行中Instagram线程，原因: 模拟器状态变为异常
2025-07-28 08:10:53 - MainWindowV2 - INFO - 模拟器25: 运行中 -> 异常
2025-07-28 08:10:53 - InstagramTaskManager - INFO - 模拟器25的Instagram线程已清理完成，当前并发: 1/1
2025-07-28 08:10:53 - TaskActivityHeartbeatManager - INFO - 模拟器已标记为异常并清理状态 | emulator_id: 25 | removed_from_running: False | removed_from_active: False | total_running: 1 | total_active: 0
2025-07-28 08:10:53 - TaskActivityHeartbeatManager - INFO - 模拟器 25 已从任务活动监控移除
2025-07-28 08:10:53 - TaskActivityHeartbeatManager - INFO - 没有排队等待接力的模拟器
2025-07-28 08:10:53 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 2 | queued: 0 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 50.0 | concurrent_slots_used: 1 | max_concurrent: 1
2025-07-28 08:10:53 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/1)
2025-07-28 08:10:53 - UnifiedEmulatorManager - INFO - 模拟器已切换: 25 -> -1
2025-07-28 08:10:53 - MainWindowV2 - INFO - 模拟器自动切换: 25 -> -1
2025-07-28 08:10:53 - MainWindowV2 - INFO - 模拟器已自动切换: 25 -> -1
2025-07-28 08:10:53 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 26
2025-07-28 08:10:53 - MainWindowV2 - WARNING - 模拟器26心跳状态更新未产生变化
2025-07-28 08:10:53 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 08:10:53 - InstagramDMTask - INFO - [模拟器26] 继续等待测试结果 (1/3)
2025-07-28 08:10:55 - InstagramDMTask - INFO - [模拟器26] 测试状态监控 (2/30): 测试中…
2025-07-28 08:10:55 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:10:56 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:10:57 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:10:58 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:11:00 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:11:01 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:11:02 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:11:04 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:11:06 - InstagramDMTask - ERROR - [模拟器26] ❌ V2Ray节点延迟测试失败 (2/3): 失败：: net/http: request canceled (Client.Timeout exceeded while awaiting headers)
2025-07-28 08:11:06 - InstagramDMTask - INFO - [模拟器26] 点击失败状态重置UI
2025-07-28 08:11:06 - InstagramDMTask - INFO - [模拟器26] 已点击失败状态，等待UI重置
2025-07-28 08:11:07 - InstagramDMTask - INFO - [模拟器26] 继续等待测试结果 (2/3)
2025-07-28 08:11:08 - InstagramDMTask - INFO - [模拟器26] 🔄 延迟测试进行中，继续等待...
2025-07-28 08:11:08 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 08:11:08 - __main__ - INFO - 开始清理资源...
2025-07-28 08:11:08 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 08:11:08 - __main__ - INFO - 配置已保存
2025-07-28 08:11:08 - __main__ - ERROR - 异步桥梁关闭失败: 'FixedAsyncBridge' object has no attribute 'shutdown'
2025-07-28 08:11:08 - __main__ - INFO - 应用程序已退出
