2025-07-28 06:56:43 - root - INFO - 简化日志系统初始化完成
2025-07-28 06:56:43 - main - INFO - 应用程序启动
2025-07-28 06:56:43 - __main__ - INFO - Qt应用程序已创建
2025-07-28 06:56:43 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 06:56:43 - __main__ - INFO - 统一配置管理器已创建
2025-07-28 06:56:43 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-28 06:56:43 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-28 06:56:43 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-28 06:56:43 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-28 06:56:43 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-28 06:56:43 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-28 06:56:43 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-28 06:56:43 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-28 06:56:43 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-28 06:56:43 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-28 06:56:43 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-28 06:56:43 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 06:56:43 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-28 06:56:43 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-28 06:56:43 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-28 06:56:43 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-28 06:56:43 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-28 06:56:43 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-28 06:56:43 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-28 06:56:43 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-28 06:56:43 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-28 06:56:43 - __main__ - INFO - UI主窗口已创建
2025-07-28 06:56:43 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-28 06:56:43 - __main__ - INFO - 主窗口已显示
2025-07-28 06:56:44 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-28 06:56:44 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-28 06:56:44 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-28 06:56:44 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-28 06:56:44 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 06:56:44 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-28 06:56:44 - __main__ - INFO - UI层和业务层已连接
2025-07-28 06:56:44 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 06:56:44 - __main__ - INFO - 启动Qt事件循环
2025-07-28 06:56:44 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 06:56:44 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 06:56:44 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 06:56:44 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-28 06:56:44 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 06:56:44 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 06:56:44 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 06:56:44 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 06:56:44 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 06:56:44 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-28 06:56:44 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 06:56:44 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 06:56:44 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 06:56:44 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 06:56:44 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.28s | count: 1229
2025-07-28 06:56:44 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 06:56:44 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 06:56:44 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 06:56:44 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 06:56:44 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-28 06:56:44 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 06:56:44 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 06:56:44 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 06:56:44 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 06:56:44 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.26s | count: 1229
2025-07-28 06:56:44 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 06:56:44 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 06:56:44 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 06:56:44 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 06:56:45 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-28 06:56:45 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-28 06:56:45 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-28 06:56:45 - __main__ - INFO - 后台服务已启动
2025-07-28 06:56:45 - __main__ - INFO - 延迟启动服务完成
2025-07-28 06:56:46 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 666 -> 1
2025-07-28 06:56:46 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 06:56:46 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 06:56:46 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 1
2025-07-28 06:56:46 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 1 -> 15
2025-07-28 06:56:46 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 06:56:46 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 06:56:46 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 15
2025-07-28 06:56:46 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 15 -> 154
2025-07-28 06:56:46 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 06:56:46 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 06:56:46 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 154
2025-07-28 06:56:47 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-28 06:56:47 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-28 06:56:47 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 06:56:47 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-28 06:56:48 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 154 -> 1
2025-07-28 06:56:48 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 06:56:48 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 06:56:48 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 1
2025-07-28 06:56:48 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 1 -> 14
2025-07-28 06:56:48 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 06:56:48 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 06:56:48 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 14
2025-07-28 06:56:49 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-28 06:56:49 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-28 06:56:49 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 06:56:49 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-28 06:56:55 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 06:56:57 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-28 06:56:57 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-28 06:56:57 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 06:56:57 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-28 06:56:57 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_direct_task
2025-07-28 06:56:57 - MainWindowV2 - INFO - Instagram直接关注任务已启动，涉及3个模拟器
2025-07-28 06:56:57 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram直接关注任务请求
2025-07-28 06:56:57 - MainWindowV2 - INFO - 用户启动Instagram直接关注任务，模拟器数量: 3
2025-07-28 06:56:57 - FixedAsyncBridge - INFO - 开始处理Instagram直接关注任务（线程池模式），模拟器数量: 3
2025-07-28 06:56:57 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [3, 4, 5]
2025-07-28 06:56:57 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 3
2025-07-28 06:56:57 - StartupManager - INFO - 批量启动请求 | count: 3
2025-07-28 06:56:57 - StartupManager - INFO - 启动调度器已启动
2025-07-28 06:56:57 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-28 06:56:57 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-28 06:56:57 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-28 06:56:57 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 2
2025-07-28 06:56:57 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: direct
2025-07-28 06:56:57 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 3
2025-07-28 06:56:57 - InstagramFollowTaskThread - INFO - [模拟器3] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 06:56:57 - InstagramFollowTaskManager - INFO - 为模拟器 3 创建Instagram关注任务线程，模式: direct
2025-07-28 06:56:57 - InstagramFollowTaskThread - INFO - [模拟器4] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 06:56:57 - InstagramFollowTaskManager - INFO - 为模拟器 4 创建Instagram关注任务线程，模式: direct
2025-07-28 06:56:57 - InstagramFollowTaskThread - INFO - [模拟器5] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 06:56:57 - InstagramFollowTaskManager - INFO - 为模拟器 5 创建Instagram关注任务线程，模式: direct
2025-07-28 06:56:57 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建3个Instagram线程
2025-07-28 06:56:57 - FixedAsyncBridge - INFO - 异步桥接器: Instagram直接关注任务请求已处理，状态: started
2025-07-28 06:56:57 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-28 06:56:57 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-28 06:56:57 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 3
2025-07-28 06:56:57 - MainWindowV2 - INFO - 3个模拟器: 未知 -> 排队中
2025-07-28 06:56:57 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 3 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 2
2025-07-28 06:56:57 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/2)
2025-07-28 06:56:57 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-28 06:56:57 - MainWindowV2 - INFO - 批量启动完成: 0/3 成功
2025-07-28 06:56:57 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 3
2025-07-28 06:56:57 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_direct_task
2025-07-28 06:56:57 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 排队中 | new_state: 启动中
2025-07-28 06:56:57 - MainWindowV2 - INFO - 模拟器3: 排队中 -> 启动中
2025-07-28 06:56:57 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 2 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 2
2025-07-28 06:56:57 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/2)
2025-07-28 06:56:57 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 3
2025-07-28 06:57:02 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-28 06:57:02 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 排队中 | new_state: 启动中
2025-07-28 06:57:02 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-28 06:57:02 - MainWindowV2 - INFO - 模拟器4: 排队中 -> 启动中
2025-07-28 06:57:02 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 4
2025-07-28 06:57:02 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 2 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 06:57:02 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中2个, 运行0个, 失败0个 (并发槽位:2/2)
2025-07-28 06:57:02 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 4
2025-07-28 06:57:07 - Emulator - INFO - Android系统启动完成 | emulator_id: 3 | elapsed_time: 9.9秒
2025-07-28 06:57:07 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 启动中 -> 运行中
2025-07-28 06:57:07 - InstagramFollowTaskManager - INFO - 启动模拟器3的Instagram关注任务线程 - 当前并发: 1/2
2025-07-28 06:57:07 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 启动中 | new_state: 运行中
2025-07-28 06:57:07 - Emulator - INFO - 模拟器启动成功 | emulator_id: 3 | running_count: 1
2025-07-28 06:57:07 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已添加到任务活动监控，失败计数: 0
2025-07-28 06:57:07 - InstagramTaskThread - INFO - [模拟器3] 开始等待启动完成
2025-07-28 06:57:07 - InstagramTaskThread - INFO - [模拟器3] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 06:57:07 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务start
2025-07-28 06:57:07 - InstagramTaskThread - INFO - [模拟器3] 开始窗口排列
2025-07-28 06:57:07 - MainWindowV2 - INFO - 模拟器3启动成功
2025-07-28 06:57:07 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 06:57:07 - WindowArrangementManager - INFO - 模拟器3启动完成，立即触发窗口排列
2025-07-28 06:57:07 - MainWindowV2 - WARNING - 未找到模拟器3，无法更新状态
2025-07-28 06:57:07 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-28 06:57:07 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 06:57:07 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/2)
2025-07-28 06:57:07 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中, PID: 24052
2025-07-28 06:57:07 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-28 06:57:09 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 06:57:09 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 06:57:09 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 06:57:09 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 06:57:09 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 06:57:09 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 06:57:09 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 06:57:09 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 06:57:09 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 06:57:09 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 06:57:09 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 06:57:09 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 06:57:12 - Emulator - INFO - Android系统启动完成 | emulator_id: 4 | elapsed_time: 10.0秒
2025-07-28 06:57:12 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 启动中 -> 运行中
2025-07-28 06:57:12 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 启动中 | new_state: 运行中
2025-07-28 06:57:12 - InstagramFollowTaskManager - INFO - 启动模拟器4的Instagram关注任务线程 - 当前并发: 2/2
2025-07-28 06:57:12 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已添加到任务活动监控，失败计数: 0
2025-07-28 06:57:12 - Emulator - INFO - 模拟器启动成功 | emulator_id: 4 | running_count: 2
2025-07-28 06:57:12 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务start
2025-07-28 06:57:12 - MainWindowV2 - INFO - 模拟器4启动成功
2025-07-28 06:57:12 - InstagramTaskThread - INFO - [模拟器4] 开始等待启动完成
2025-07-28 06:57:12 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 06:57:12 - InstagramTaskThread - INFO - [模拟器4] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 06:57:12 - InstagramTaskThread - INFO - [模拟器4] 开始窗口排列
2025-07-28 06:57:12 - WindowArrangementManager - INFO - 模拟器4启动完成，立即触发窗口排列
2025-07-28 06:57:12 - MainWindowV2 - WARNING - 未找到模拟器4，无法更新状态
2025-07-28 06:57:12 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-28 06:57:12 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 0 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 66.66666666666666 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 06:57:12 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行2个, 失败0个 (并发槽位:2/2)
2025-07-28 06:57:13 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中, PID: 13732
2025-07-28 06:57:13 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-28 06:57:14 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 06:57:14 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 06:57:14 - WindowArrangementManager - INFO - 窗口扫描完成，找到 2 个运行中的模拟器窗口
2025-07-28 06:57:14 - WindowArrangementManager - INFO - 找到 2 个正在运行的模拟器窗口
2025-07-28 06:57:14 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 06:57:14 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 06:57:14 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 06:57:14 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已在网格位置 (0, 0)，保持不动
2025-07-28 06:57:14 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 06:57:14 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 06:57:14 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已排列到网格位置 (0, 1): (302, 0) 保持原大小: 302x435
2025-07-28 06:57:14 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/2 个窗口
2025-07-28 06:57:14 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/2 个窗口 (成功: 1/2)
2025-07-28 06:57:15 - InstagramTaskThread - INFO - [模拟器3] 窗口排列完成
2025-07-28 06:57:15 - InstagramFollowTaskThread - INFO - [模拟器3] 开始执行Instagram直接关注任务
2025-07-28 06:57:15 - InstagramDMTask - INFO - [模拟器3] 雷电模拟器API初始化成功
2025-07-28 06:57:15 - InstagramDMTask - INFO - [模拟器3] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 06:57:15 - InstagramDMTask - INFO - [模拟器3] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 06:57:15 - InstagramDMTask - INFO - [模拟器3] 已设置ld.emulator_id = 3
2025-07-28 06:57:15 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 06:57:15 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置热加载观察者已注册
2025-07-28 06:57:15 - InstagramDMTask - INFO - [模拟器3] Instagram私信任务执行器初始化完成
2025-07-28 06:57:15 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务配置加载完成
2025-07-28 06:57:15 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务执行器初始化完成
2025-07-28 06:57:15 - InstagramFollowTask - INFO - [模拟器3] 关注模式已设置为: direct
2025-07-28 06:57:15 - InstagramFollowTask - INFO - [模拟器3] 开始执行Instagram关注任务
2025-07-28 06:57:15 - InstagramFollowTask - WARNING - [模拟器3] 任务开始时间未由线程传递，在此设置
2025-07-28 06:57:15 - InstagramFollowTask - INFO - [模拟器3] 任务超时设置: 14秒，已运行: 0.00秒
2025-07-28 06:57:16 - InstagramDMTask - INFO - [模拟器3] 模拟器Android系统运行正常，桌面稳定
2025-07-28 06:57:16 - InstagramDMTask - INFO - [模拟器3] 开始检查应用安装状态
2025-07-28 06:57:16 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 06:57:16 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 06:57:16 - InstagramDMTask - INFO - [模拟器3] 📊 应用安装状态检测结果:
2025-07-28 06:57:16 - InstagramDMTask - INFO - [模拟器3] ✅ 所有必要应用已安装
2025-07-28 06:57:16 - InstagramDMTask - INFO - [模拟器3] 开始启动V2Ray应用
2025-07-28 06:57:16 - InstagramDMTask - INFO - [模拟器3] V2Ray启动命令执行成功，等待应用加载
2025-07-28 06:57:16 - InstagramDMTask - INFO - [模拟器3] V2Ray应用启动结果: 成功
2025-07-28 06:57:19 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray应用启动成功
2025-07-28 06:57:19 - InstagramDMTask - INFO - [模拟器3] 开始检查V2Ray节点列表状态
2025-07-28 06:57:20 - InstagramDMTask - INFO - [模拟器3] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 06:57:20 - InstagramDMTask - INFO - [模拟器3] 开始连接V2Ray节点
2025-07-28 06:57:20 - InstagramTaskThread - INFO - [模拟器4] 窗口排列完成
2025-07-28 06:57:20 - InstagramFollowTaskThread - INFO - [模拟器4] 开始执行Instagram直接关注任务
2025-07-28 06:57:20 - InstagramDMTask - INFO - [模拟器4] 雷电模拟器API初始化成功
2025-07-28 06:57:20 - InstagramDMTask - INFO - [模拟器4] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 06:57:20 - InstagramDMTask - INFO - [模拟器4] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 06:57:20 - InstagramDMTask - INFO - [模拟器4] 已设置ld.emulator_id = 4
2025-07-28 06:57:20 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 06:57:20 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置热加载观察者已注册
2025-07-28 06:57:20 - InstagramDMTask - INFO - [模拟器4] Instagram私信任务执行器初始化完成
2025-07-28 06:57:20 - InstagramFollowTask - INFO - [模拟器4] Instagram关注任务配置加载完成
2025-07-28 06:57:20 - InstagramFollowTask - INFO - [模拟器4] Instagram关注任务执行器初始化完成
2025-07-28 06:57:20 - InstagramFollowTask - INFO - [模拟器4] 关注模式已设置为: direct
2025-07-28 06:57:20 - InstagramFollowTask - INFO - [模拟器4] 开始执行Instagram关注任务
2025-07-28 06:57:20 - InstagramFollowTask - WARNING - [模拟器4] 任务开始时间未由线程传递，在此设置
2025-07-28 06:57:20 - InstagramFollowTask - INFO - [模拟器4] 任务超时设置: 14秒，已运行: 0.00秒
2025-07-28 06:57:21 - InstagramDMTask - INFO - [模拟器4] 模拟器Android系统运行正常，桌面稳定
2025-07-28 06:57:21 - InstagramDMTask - INFO - [模拟器4] 开始检查应用安装状态
2025-07-28 06:57:21 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 06:57:21 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-28 06:57:21 - InstagramDMTask - INFO - [模拟器4] 📊 应用安装状态检测结果:
2025-07-28 06:57:21 - InstagramDMTask - INFO - [模拟器4] ✅ 所有必要应用已安装
2025-07-28 06:57:21 - InstagramDMTask - INFO - [模拟器4] 开始启动V2Ray应用
2025-07-28 06:57:21 - InstagramDMTask - INFO - [模拟器4] V2Ray启动命令执行成功，等待应用加载
2025-07-28 06:57:21 - InstagramDMTask - INFO - [模拟器4] V2Ray应用启动结果: 成功
2025-07-28 06:57:21 - InstagramDMTask - INFO - [模拟器3] 当前连接状态: 未连接
2025-07-28 06:57:21 - InstagramDMTask - INFO - [模拟器3] V2Ray节点未连接，开始连接
2025-07-28 06:57:23 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 06:57:23 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 06:57:23 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 06:57:23 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 06:57:23 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 06:57:23 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 06:57:23 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 06:57:23 - InstagramDMTask - INFO - [模拟器3] 已点击连接按钮，等待连接完成
2025-07-28 06:57:24 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray应用启动成功
2025-07-28 06:57:24 - InstagramDMTask - INFO - [模拟器4] 开始检查V2Ray节点列表状态
2025-07-28 06:57:25 - InstagramDMTask - INFO - [模拟器3] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 06:57:25 - InstagramDMTask - INFO - [模拟器3] V2Ray节点连接成功
2025-07-28 06:57:25 - InstagramDMTask - INFO - [模拟器3] 开始测试V2Ray节点延迟
2025-07-28 06:57:25 - InstagramDMTask - INFO - [模拟器3] 开始V2Ray节点延迟测试
2025-07-28 06:57:25 - InstagramDMTask - INFO - [模拟器4] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 06:57:25 - InstagramDMTask - INFO - [模拟器4] 开始连接V2Ray节点
2025-07-28 06:57:26 - InstagramDMTask - INFO - [模拟器3] 当前测试状态: 已连接，点击测试连接
2025-07-28 06:57:26 - InstagramDMTask - INFO - [模拟器3] 点击开始延迟测试
2025-07-28 06:57:26 - InstagramDMTask - INFO - [模拟器4] 当前连接状态: 未连接
2025-07-28 06:57:26 - InstagramDMTask - INFO - [模拟器4] V2Ray节点未连接，开始连接
2025-07-28 06:57:26 - InstagramDMTask - INFO - [模拟器3] 已点击测试按钮，等待测试结果
2025-07-28 06:57:28 - InstagramDMTask - INFO - [模拟器3] 测试状态监控 (1/30): 连接成功：延时 224 毫秒
2025-07-28 06:57:28 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray节点延迟测试成功: 连接成功：延时 224 毫秒
2025-07-28 06:57:28 - InstagramDMTask - INFO - [模拟器3] 等待5秒后进入下一阶段
2025-07-28 06:57:28 - InstagramDMTask - INFO - [模拟器4] 已点击连接按钮，等待连接完成
2025-07-28 06:57:30 - InstagramDMTask - INFO - [模拟器4] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 06:57:30 - InstagramDMTask - INFO - [模拟器4] V2Ray节点连接成功
2025-07-28 06:57:30 - InstagramDMTask - INFO - [模拟器4] 开始测试V2Ray节点延迟
2025-07-28 06:57:30 - InstagramDMTask - INFO - [模拟器4] 开始V2Ray节点延迟测试
2025-07-28 06:57:31 - InstagramDMTask - INFO - [模拟器4] 当前测试状态: 已连接，点击测试连接
2025-07-28 06:57:31 - InstagramDMTask - INFO - [模拟器4] 点击开始延迟测试
2025-07-28 06:57:31 - InstagramDMTask - INFO - [模拟器4] 已点击测试按钮，等待测试结果
2025-07-28 06:57:33 - InstagramDMTask - INFO - [模拟器3] 开始启动Instagram应用
2025-07-28 06:57:33 - InstagramDMTask - INFO - [模拟器4] 测试状态监控 (1/30): 失败：: io: read/write on closed pipe
2025-07-28 06:57:33 - InstagramDMTask - ERROR - [模拟器4] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: io: read/write on closed pipe
2025-07-28 06:57:33 - InstagramDMTask - INFO - [模拟器4] 点击失败状态重置UI
2025-07-28 06:57:33 - InstagramDMTask - INFO - [模拟器3] Instagram启动命令执行成功，等待应用加载
2025-07-28 06:57:33 - InstagramDMTask - INFO - [模拟器4] 已点击失败状态，等待UI重置
2025-07-28 06:57:33 - InstagramDMTask - INFO - [模拟器4] 继续等待测试结果 (1/3)
2025-07-28 06:57:35 - InstagramDMTask - ERROR - [模拟器4] ❌ V2Ray节点延迟测试失败 (2/3): 失败：: io: read/write on closed pipe
2025-07-28 06:57:35 - InstagramDMTask - INFO - [模拟器4] 点击失败状态重置UI
2025-07-28 06:57:35 - InstagramDMTask - INFO - [模拟器4] 已点击失败状态，等待UI重置
2025-07-28 06:57:35 - InstagramDMTask - INFO - [模拟器4] 继续等待测试结果 (2/3)
2025-07-28 06:57:36 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram应用启动命令执行完成
2025-07-28 06:57:36 - InstagramDMTask - INFO - [模拟器3] Instagram启动检测 第1/3次
2025-07-28 06:57:36 - InstagramDMTask - INFO - [模拟器3] 总任务超时(14秒)，Instagram启动检测中断，耗时: 20.46秒
2025-07-28 06:57:36 - InstagramDMTask - ERROR - [模拟器3] 检测到异常状态: 异常-总任务超时
2025-07-28 06:57:36 - LeiDianNativeAPI - INFO - 雷电原生API初始化成功: G:/leidian/LDPlayer9
2025-07-28 06:57:36 - NativeScreenshotEngine - INFO - 专业截图引擎初始化成功
2025-07-28 06:57:36 - ScreenshotManager - INFO - 原生截图引擎初始化成功
2025-07-28 06:57:36 - NativeScreenshotEngine - INFO - 开始截取模拟器 3 的截图
2025-07-28 06:57:36 - NativeScreenshotEngine - INFO - 模拟器 3 截图成功: test_screenshots\emulator_3_Instagram_总任务超时_1_20250728_065736.png
2025-07-28 06:57:36 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_3_Instagram_总任务超时_1_20250728_065736.png
2025-07-28 06:57:36 - InstagramDMTask - INFO - [模拟器3] 异常状态截图已保存: 异常-总任务超时
2025-07-28 06:57:36 - InstagramDMTask - INFO - [模拟器3] 任务终止原因: 异常-总任务超时
2025-07-28 06:57:36 - InstagramDMTask - INFO - [模拟器3] 开始关闭模拟器...
2025-07-28 06:57:36 - InstagramDMTask - INFO - [模拟器3] 模拟器已关闭
2025-07-28 06:57:36 - InstagramDMTask - INFO - [模拟器3] 任务状态更新: 失败 - 异常-总任务超时
2025-07-28 06:57:36 - InstagramFollowTask - INFO - [模拟器3] Instagram页面状态检测结果: 任务终止-异常-总任务超时
2025-07-28 06:57:36 - InstagramFollowTask - ERROR - [模拟器3] ❌ Instagram任务终止
2025-07-28 06:57:36 - InstagramFollowTaskThread - ERROR - [模拟器3] Instagram直接关注任务执行失败: 任务终止-异常-总任务超时
2025-07-28 06:57:37 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray节点延迟测试成功: 连接成功：延时 227 毫秒
2025-07-28 06:57:37 - InstagramDMTask - INFO - [模拟器4] 等待5秒后进入下一阶段
2025-07-28 06:57:42 - InstagramDMTask - INFO - [模拟器4] 开始启动Instagram应用
2025-07-28 06:57:42 - InstagramDMTask - INFO - [模拟器4] Instagram启动命令执行成功，等待应用加载
2025-07-28 06:57:43 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 06:57:43 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 06:57:43 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 06:57:43 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 06:57:43 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 06:57:43 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 06:57:43 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 06:57:45 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram应用启动命令执行完成
2025-07-28 06:57:45 - InstagramDMTask - INFO - [模拟器4] Instagram启动检测 第1/3次
2025-07-28 06:57:45 - InstagramDMTask - INFO - [模拟器4] 总任务超时(14秒)，Instagram启动检测中断，耗时: 24.48秒
2025-07-28 06:57:45 - InstagramDMTask - ERROR - [模拟器4] 检测到异常状态: 异常-总任务超时
2025-07-28 06:57:45 - NativeScreenshotEngine - INFO - 开始截取模拟器 4 的截图
2025-07-28 06:57:45 - NativeScreenshotEngine - INFO - 模拟器 4 截图成功: test_screenshots\emulator_4_Instagram_总任务超时_1_20250728_065745.png
2025-07-28 06:57:45 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_4_Instagram_总任务超时_1_20250728_065745.png
2025-07-28 06:57:45 - InstagramDMTask - INFO - [模拟器4] 异常状态截图已保存: 异常-总任务超时
2025-07-28 06:57:45 - InstagramDMTask - INFO - [模拟器4] 任务终止原因: 异常-总任务超时
2025-07-28 06:57:45 - InstagramDMTask - INFO - [模拟器4] 开始关闭模拟器...
2025-07-28 06:57:45 - InstagramDMTask - INFO - [模拟器4] 模拟器已关闭
2025-07-28 06:57:45 - InstagramDMTask - INFO - [模拟器4] 任务状态更新: 失败 - 异常-总任务超时
2025-07-28 06:57:45 - InstagramFollowTask - INFO - [模拟器4] Instagram页面状态检测结果: 任务终止-异常-总任务超时
2025-07-28 06:57:45 - InstagramFollowTask - ERROR - [模拟器4] ❌ Instagram任务终止
2025-07-28 06:57:45 - InstagramFollowTaskThread - ERROR - [模拟器4] Instagram直接关注任务执行失败: 任务终止-异常-总任务超时
2025-07-28 06:58:03 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 06:58:03 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 06:58:03 - TaskActivityHeartbeatManager - INFO - 模拟器 3 疑似心跳异常，进入观察期 | 无活动时间: 26.4秒
2025-07-28 06:58:03 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 06:58:03 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 06:58:03 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 06:58:23 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 06:58:23 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 06:58:23 - TaskActivityHeartbeatManager - ERROR - 模拟器 3 确认心跳异常 | 总无活动时间: 46.4秒
2025-07-28 06:58:23 - TaskActivityHeartbeatManager - INFO - 心跳异常处理开始，失败次数: 1/1 | emulator_id: 3
2025-07-28 06:58:23 - NativeScreenshotEngine - INFO - 开始截取模拟器 3 的截图
2025-07-28 06:58:23 - UnifiedEmulatorManager - INFO - 心跳检测失败: 心跳异常, 失败次数: 1 | emulator_id: 3
2025-07-28 06:58:23 - MainWindowV2 - INFO - 模拟器3心跳异常: 心跳异常 (失败次数: 1)
2025-07-28 06:58:23 - MainWindowV2 - WARNING - 模拟器3心跳检测失败: 心跳异常, 失败次数: 1
2025-07-28 06:58:23 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 06:58:23 - NativeScreenshotEngine - ERROR - 模拟器 3 绑定句柄无效: 0
2025-07-28 06:58:23 - ScreenshotManager - ERROR - 原生截图引擎截图失败
2025-07-28 06:58:23 - TaskActivityHeartbeatManager - INFO - 开始熔断处理 | emulator_id: 3
2025-07-28 06:58:23 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 3 | remaining_running: 1
2025-07-28 06:58:23 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 运行中 -> 异常
2025-07-28 06:58:23 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 运行中 | new_state: 异常
2025-07-28 06:58:23 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 运行中 -> 异常
2025-07-28 06:58:23 - MainWindowV2 - INFO - 模拟器3: 运行中 -> 异常
2025-07-28 06:58:23 - InstagramTaskManager - INFO - 清理模拟器3的运行中Instagram线程，原因: 模拟器状态变为异常
2025-07-28 06:58:23 - InstagramTaskManager - INFO - 模拟器3的Instagram线程已清理完成，当前并发: 1/2
2025-07-28 06:58:23 - InstagramTaskManager - INFO - 为接力排队模拟器5创建Instagram任务线程
2025-07-28 06:58:23 - InstagramFollowTaskThread - INFO - [模拟器5] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 06:58:23 - InstagramFollowTaskManager - INFO - 为接力模拟器5创建并启动Instagram关注任务线程 - 当前并发: 2/2
2025-07-28 06:58:23 - InstagramFollowTaskManager - INFO - 为模拟器 5 创建Instagram关注任务线程，模式: direct
2025-07-28 06:58:23 - InstagramTaskThread - INFO - [模拟器5] 开始等待启动完成
2025-07-28 06:58:23 - TaskActivityHeartbeatManager - INFO - 模拟器已标记为异常并清理状态 | emulator_id: 3 | removed_from_running: True | removed_from_active: False | total_running: 1 | total_active: 0
2025-07-28 06:58:23 - InstagramTaskThread - ERROR - [模拟器5] ❌ StartupManager已完成处理但未成功，状态: 排队中
2025-07-28 06:58:23 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已从任务活动监控移除
2025-07-28 06:58:23 - TaskActivityHeartbeatManager - INFO - 发现排队模拟器 5，开始任务接力
2025-07-28 06:58:23 - TaskActivityHeartbeatManager - INFO - 模拟器 4 疑似心跳异常，进入观察期 | 无活动时间: 37.5秒
2025-07-28 06:58:23 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 1 | max_concurrent: 2
2025-07-28 06:58:23 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/2)
2025-07-28 06:58:23 - UnifiedEmulatorManager - INFO - 模拟器已切换: 3 -> -1
2025-07-28 06:58:23 - MainWindowV2 - INFO - 模拟器自动切换: 3 -> -1
2025-07-28 06:58:23 - MainWindowV2 - INFO - 模拟器已自动切换: 3 -> -1
2025-07-28 06:58:23 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 06:58:23 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 06:58:23 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 排队中 | new_state: 启动中
2025-07-28 06:58:23 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 06:58:23 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 5
2025-07-28 06:58:23 - MainWindowV2 - INFO - 模拟器5: 排队中 -> 启动中
2025-07-28 06:58:23 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 06:58:23 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/2)
2025-07-28 06:58:23 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 5
2025-07-28 06:58:33 - Emulator - INFO - Android系统启动完成 | emulator_id: 5 | elapsed_time: 9.9秒
2025-07-28 06:58:33 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-28 06:58:33 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 启动中 | new_state: 运行中
2025-07-28 06:58:33 - InstagramFollowTaskManager - INFO - 启动模拟器5的Instagram关注任务线程 - 当前并发: 3/2
2025-07-28 06:58:33 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已添加到任务活动监控，失败计数: 0
2025-07-28 06:58:33 - Emulator - INFO - 模拟器启动成功 | emulator_id: 5 | running_count: 2
2025-07-28 06:58:33 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务start
2025-07-28 06:58:33 - MainWindowV2 - INFO - 模拟器5启动成功
2025-07-28 06:58:33 - InstagramTaskThread - INFO - [模拟器5] 开始等待启动完成
2025-07-28 06:58:33 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 06:58:33 - InstagramTaskThread - INFO - [模拟器5] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 06:58:33 - InstagramTaskThread - INFO - [模拟器5] 开始窗口排列
2025-07-28 06:58:33 - WindowArrangementManager - INFO - 模拟器5启动完成，立即触发窗口排列
2025-07-28 06:58:33 - MainWindowV2 - WARNING - 未找到模拟器5，无法更新状态
2025-07-28 06:58:33 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 06:58:33 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 0 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 66.66666666666666 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 06:58:33 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行2个, 失败0个 (并发槽位:2/2)
2025-07-28 06:58:33 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中, PID: 20328
2025-07-28 06:58:33 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 06:58:34 - StartupManager - INFO - 调度器已停止
2025-07-28 06:58:35 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 06:58:35 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 06:58:35 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 06:58:35 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 06:58:35 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 06:58:35 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 06:58:35 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 06:58:35 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 06:58:35 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 06:58:35 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 06:58:35 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 06:58:35 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 06:58:41 - InstagramTaskThread - INFO - [模拟器5] 窗口排列完成
2025-07-28 06:58:41 - InstagramFollowTaskThread - INFO - [模拟器5] 开始执行Instagram直接关注任务
2025-07-28 06:58:41 - InstagramDMTask - INFO - [模拟器5] 雷电模拟器API初始化成功
2025-07-28 06:58:41 - InstagramDMTask - INFO - [模拟器5] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 06:58:41 - InstagramDMTask - INFO - [模拟器5] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 06:58:41 - InstagramDMTask - INFO - [模拟器5] 已设置ld.emulator_id = 5
2025-07-28 06:58:41 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 06:58:41 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置热加载观察者已注册
2025-07-28 06:58:41 - InstagramDMTask - INFO - [模拟器5] Instagram私信任务执行器初始化完成
2025-07-28 06:58:41 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务配置加载完成
2025-07-28 06:58:41 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务执行器初始化完成
2025-07-28 06:58:41 - InstagramFollowTask - INFO - [模拟器5] 关注模式已设置为: direct
2025-07-28 06:58:41 - InstagramFollowTask - INFO - [模拟器5] 开始执行Instagram关注任务
2025-07-28 06:58:41 - InstagramFollowTask - WARNING - [模拟器5] 任务开始时间未由线程传递，在此设置
2025-07-28 06:58:41 - InstagramFollowTask - INFO - [模拟器5] 任务超时设置: 14秒，已运行: 0.00秒
2025-07-28 06:58:41 - InstagramDMTask - INFO - [模拟器5] 模拟器Android系统运行正常，桌面稳定
2025-07-28 06:58:41 - InstagramDMTask - INFO - [模拟器5] 开始检查应用安装状态
2025-07-28 06:58:41 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 06:58:42 - InstagramDMTask - INFO - [模拟器5] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 06:58:42 - InstagramDMTask - INFO - [模拟器5] 📊 应用安装状态检测结果:
2025-07-28 06:58:42 - InstagramDMTask - INFO - [模拟器5] ✅ 所有必要应用已安装
2025-07-28 06:58:42 - InstagramDMTask - INFO - [模拟器5] 开始启动V2Ray应用
2025-07-28 06:58:42 - InstagramDMTask - INFO - [模拟器5] V2Ray启动命令执行成功，等待应用加载
2025-07-28 06:58:42 - InstagramDMTask - INFO - [模拟器5] V2Ray应用启动结果: 成功
2025-07-28 06:58:43 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 06:58:43 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 06:58:43 - TaskActivityHeartbeatManager - ERROR - 模拟器 4 确认心跳异常 | 总无活动时间: 57.5秒
2025-07-28 06:58:43 - TaskActivityHeartbeatManager - INFO - 心跳异常处理开始，失败次数: 1/1 | emulator_id: 4
2025-07-28 06:58:43 - UnifiedEmulatorManager - INFO - 心跳检测失败: 心跳异常, 失败次数: 1 | emulator_id: 4
2025-07-28 06:58:43 - NativeScreenshotEngine - INFO - 开始截取模拟器 4 的截图
2025-07-28 06:58:43 - MainWindowV2 - INFO - 模拟器4心跳异常: 心跳异常 (失败次数: 1)
2025-07-28 06:58:43 - MainWindowV2 - WARNING - 模拟器4心跳检测失败: 心跳异常, 失败次数: 1
2025-07-28 06:58:43 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 06:58:43 - NativeScreenshotEngine - ERROR - 模拟器 4 绑定句柄无效: 0
2025-07-28 06:58:43 - ScreenshotManager - ERROR - 原生截图引擎截图失败
2025-07-28 06:58:43 - TaskActivityHeartbeatManager - INFO - 开始熔断处理 | emulator_id: 4
2025-07-28 06:58:43 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 4 | remaining_running: 1
2025-07-28 06:58:43 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 运行中 -> 异常
2025-07-28 06:58:43 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 运行中 | new_state: 异常
2025-07-28 06:58:43 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 运行中 -> 异常
2025-07-28 06:58:43 - MainWindowV2 - INFO - 模拟器4: 运行中 -> 异常
2025-07-28 06:58:43 - InstagramTaskManager - INFO - 清理模拟器4的运行中Instagram线程，原因: 模拟器状态变为异常
2025-07-28 06:58:43 - InstagramTaskManager - INFO - 模拟器4的Instagram线程已清理完成，当前并发: 2/2
2025-07-28 06:58:43 - TaskActivityHeartbeatManager - INFO - 模拟器已标记为异常并清理状态 | emulator_id: 4 | removed_from_running: True | removed_from_active: False | total_running: 1 | total_active: 0
2025-07-28 06:58:43 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 1 | max_concurrent: 2
2025-07-28 06:58:43 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已从任务活动监控移除
2025-07-28 06:58:43 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/2)
2025-07-28 06:58:43 - TaskActivityHeartbeatManager - INFO - 没有排队等待接力的模拟器
2025-07-28 06:58:43 - UnifiedEmulatorManager - INFO - 模拟器已切换: 4 -> -1
2025-07-28 06:58:43 - MainWindowV2 - INFO - 模拟器自动切换: 4 -> -1
2025-07-28 06:58:43 - MainWindowV2 - INFO - 模拟器已自动切换: 4 -> -1
2025-07-28 06:58:43 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-28 06:58:43 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-28 06:58:43 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 06:58:45 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray应用启动成功
2025-07-28 06:58:45 - InstagramDMTask - INFO - [模拟器5] 开始检查V2Ray节点列表状态
2025-07-28 06:58:46 - InstagramDMTask - INFO - [模拟器5] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 06:58:46 - InstagramDMTask - INFO - [模拟器5] 开始连接V2Ray节点
2025-07-28 06:58:47 - InstagramDMTask - INFO - [模拟器5] 当前连接状态: 未连接
2025-07-28 06:58:47 - InstagramDMTask - INFO - [模拟器5] V2Ray节点未连接，开始连接
2025-07-28 06:58:49 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 06:58:49 - InstagramDMTask - INFO - [模拟器5] 已点击连接按钮，等待连接完成
2025-07-28 06:58:49 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-28 06:58:49 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-28 06:58:49 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 06:58:49 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-28 06:58:49 - __main__ - INFO - 开始清理资源...
2025-07-28 06:58:49 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 06:58:49 - __main__ - INFO - 配置已保存
2025-07-28 06:58:49 - __main__ - ERROR - 异步桥梁关闭失败: 'FixedAsyncBridge' object has no attribute 'shutdown'
2025-07-28 06:58:49 - __main__ - INFO - 应用程序已退出
