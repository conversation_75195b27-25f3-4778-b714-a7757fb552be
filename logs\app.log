2025-07-28 07:41:05 - root - INFO - 简化日志系统初始化完成
2025-07-28 07:41:05 - main - INFO - 应用程序启动
2025-07-28 07:41:05 - __main__ - INFO - Qt应用程序已创建
2025-07-28 07:41:05 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 07:41:05 - __main__ - INFO - 统一配置管理器已创建
2025-07-28 07:41:05 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-28 07:41:05 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-28 07:41:05 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-28 07:41:05 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-28 07:41:05 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-28 07:41:05 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-28 07:41:05 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-28 07:41:05 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-28 07:41:05 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-28 07:41:05 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-28 07:41:05 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-28 07:41:06 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 07:41:06 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-28 07:41:06 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-28 07:41:06 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-28 07:41:06 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-28 07:41:06 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-28 07:41:06 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-28 07:41:06 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-28 07:41:06 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-28 07:41:06 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-28 07:41:06 - __main__ - INFO - UI主窗口已创建
2025-07-28 07:41:06 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-28 07:41:06 - __main__ - INFO - 主窗口已显示
2025-07-28 07:41:06 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-28 07:41:06 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-28 07:41:06 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-28 07:41:06 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-28 07:41:06 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 07:41:06 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-28 07:41:06 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 07:41:06 - __main__ - INFO - UI层和业务层已连接
2025-07-28 07:41:06 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 07:41:06 - __main__ - INFO - 启动Qt事件循环
2025-07-28 07:41:06 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 07:41:06 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 07:41:06 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-28 07:41:06 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 07:41:06 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 07:41:06 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 07:41:06 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 07:41:06 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 07:41:07 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-28 07:41:07 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 07:41:07 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 07:41:07 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 07:41:07 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 07:41:07 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.27s | count: 1229
2025-07-28 07:41:07 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 07:41:07 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 07:41:07 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 07:41:07 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 07:41:07 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-28 07:41:07 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 07:41:07 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 07:41:07 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 07:41:07 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 07:41:07 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.24s | count: 1229
2025-07-28 07:41:07 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 07:41:07 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 07:41:07 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 07:41:07 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 07:41:07 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-28 07:41:07 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-28 07:41:07 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-28 07:41:07 - __main__ - INFO - 后台服务已启动
2025-07-28 07:41:07 - __main__ - INFO - 延迟启动服务完成
2025-07-28 07:41:17 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_direct_task
2025-07-28 07:41:17 - MainWindowV2 - INFO - Instagram直接关注任务已启动，涉及3个模拟器
2025-07-28 07:41:17 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram直接关注任务请求
2025-07-28 07:41:17 - MainWindowV2 - INFO - 用户启动Instagram直接关注任务，模拟器数量: 3
2025-07-28 07:41:17 - FixedAsyncBridge - INFO - 开始处理Instagram直接关注任务（线程池模式），模拟器数量: 3
2025-07-28 07:41:17 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [4, 5, 15]
2025-07-28 07:41:17 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 3
2025-07-28 07:41:17 - StartupManager - INFO - 批量启动请求 | count: 3
2025-07-28 07:41:17 - StartupManager - INFO - 启动调度器已启动
2025-07-28 07:41:17 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-28 07:41:17 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-28 07:41:17 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-28 07:41:17 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 2
2025-07-28 07:41:17 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: direct
2025-07-28 07:41:17 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 3
2025-07-28 07:41:17 - InstagramFollowTaskThread - INFO - [模拟器4] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 07:41:17 - InstagramFollowTaskManager - INFO - 为模拟器 4 创建Instagram关注任务线程，模式: direct
2025-07-28 07:41:17 - InstagramFollowTaskThread - INFO - [模拟器5] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 07:41:17 - InstagramFollowTaskManager - INFO - 为模拟器 5 创建Instagram关注任务线程，模式: direct
2025-07-28 07:41:17 - InstagramFollowTaskThread - INFO - [模拟器15] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 07:41:17 - InstagramFollowTaskManager - INFO - 为模拟器 15 创建Instagram关注任务线程，模式: direct
2025-07-28 07:41:17 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建3个Instagram线程
2025-07-28 07:41:17 - MainWindowV2 - INFO - 3个模拟器: 未知 -> 排队中
2025-07-28 07:41:17 - FixedAsyncBridge - INFO - 异步桥接器: Instagram直接关注任务请求已处理，状态: started
2025-07-28 07:41:17 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-28 07:41:17 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 3 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 2
2025-07-28 07:41:17 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-28 07:41:17 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/2)
2025-07-28 07:41:17 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 4
2025-07-28 07:41:17 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-28 07:41:17 - MainWindowV2 - INFO - 批量启动完成: 0/3 成功
2025-07-28 07:41:17 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 3
2025-07-28 07:41:17 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_direct_task
2025-07-28 07:41:17 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 排队中 | new_state: 启动中
2025-07-28 07:41:17 - MainWindowV2 - INFO - 模拟器4: 排队中 -> 启动中
2025-07-28 07:41:17 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 2 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 2
2025-07-28 07:41:17 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/2)
2025-07-28 07:41:17 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 4
2025-07-28 07:41:22 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 07:41:22 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 排队中 | new_state: 启动中
2025-07-28 07:41:22 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 07:41:22 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 5
2025-07-28 07:41:22 - MainWindowV2 - INFO - 模拟器5: 排队中 -> 启动中
2025-07-28 07:41:22 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 2 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 07:41:22 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中2个, 运行0个, 失败0个 (并发槽位:2/2)
2025-07-28 07:41:22 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 5
2025-07-28 07:41:27 - Emulator - INFO - Android系统启动完成 | emulator_id: 4 | elapsed_time: 9.9秒
2025-07-28 07:41:27 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 启动中 -> 运行中
2025-07-28 07:41:27 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 启动中 | new_state: 运行中
2025-07-28 07:41:27 - InstagramFollowTaskManager - INFO - 启动模拟器4的Instagram关注任务线程 - 当前并发: 1/2
2025-07-28 07:41:27 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已添加到任务活动监控，失败计数: 0
2025-07-28 07:41:27 - Emulator - INFO - 模拟器启动成功 | emulator_id: 4 | running_count: 1
2025-07-28 07:41:27 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务start
2025-07-28 07:41:27 - MainWindowV2 - INFO - 模拟器4启动成功
2025-07-28 07:41:27 - InstagramTaskThread - INFO - [模拟器4] 开始等待启动完成
2025-07-28 07:41:27 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 07:41:27 - InstagramTaskThread - INFO - [模拟器4] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 07:41:27 - InstagramTaskThread - INFO - [模拟器4] 开始窗口排列
2025-07-28 07:41:27 - WindowArrangementManager - INFO - 模拟器4启动完成，立即触发窗口排列
2025-07-28 07:41:27 - MainWindowV2 - WARNING - 未找到模拟器4，无法更新状态
2025-07-28 07:41:27 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-28 07:41:27 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 07:41:27 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/2)
2025-07-28 07:41:27 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中, PID: 12440
2025-07-28 07:41:27 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-28 07:41:29 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 07:41:29 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 07:41:29 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 07:41:29 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 07:41:29 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 07:41:29 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 07:41:29 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 07:41:29 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 07:41:29 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 07:41:29 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 07:41:29 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 07:41:29 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 07:41:32 - Emulator - INFO - Android系统启动完成 | emulator_id: 5 | elapsed_time: 10.0秒
2025-07-28 07:41:32 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-28 07:41:32 - InstagramFollowTaskManager - INFO - 启动模拟器5的Instagram关注任务线程 - 当前并发: 2/2
2025-07-28 07:41:32 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 启动中 | new_state: 运行中
2025-07-28 07:41:32 - Emulator - INFO - 模拟器启动成功 | emulator_id: 5 | running_count: 2
2025-07-28 07:41:32 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已添加到任务活动监控，失败计数: 0
2025-07-28 07:41:32 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务start
2025-07-28 07:41:32 - MainWindowV2 - INFO - 模拟器5启动成功
2025-07-28 07:41:32 - InstagramTaskThread - INFO - [模拟器5] 开始等待启动完成
2025-07-28 07:41:32 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 07:41:32 - InstagramTaskThread - INFO - [模拟器5] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 07:41:32 - InstagramTaskThread - INFO - [模拟器5] 开始窗口排列
2025-07-28 07:41:32 - WindowArrangementManager - INFO - 模拟器5启动完成，立即触发窗口排列
2025-07-28 07:41:32 - MainWindowV2 - WARNING - 未找到模拟器5，无法更新状态
2025-07-28 07:41:32 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 07:41:32 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 0 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 66.66666666666666 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 07:41:32 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行2个, 失败0个 (并发槽位:2/2)
2025-07-28 07:41:32 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中, PID: 5064
2025-07-28 07:41:32 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 07:41:34 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 07:41:34 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 07:41:34 - WindowArrangementManager - INFO - 窗口扫描完成，找到 2 个运行中的模拟器窗口
2025-07-28 07:41:34 - WindowArrangementManager - INFO - 找到 2 个正在运行的模拟器窗口
2025-07-28 07:41:34 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 07:41:34 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 07:41:34 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 07:41:34 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已在网格位置 (0, 0)，保持不动
2025-07-28 07:41:34 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 07:41:34 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 07:41:34 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 已排列到网格位置 (0, 1): (302, 0) 保持原大小: 302x435
2025-07-28 07:41:34 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/2 个窗口
2025-07-28 07:41:34 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/2 个窗口 (成功: 1/2)
2025-07-28 07:41:35 - InstagramTaskThread - INFO - [模拟器4] 窗口排列完成
2025-07-28 07:41:35 - InstagramFollowTaskThread - INFO - [模拟器4] 开始执行Instagram直接关注任务
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] 雷电模拟器API初始化成功
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] 已设置ld.emulator_id = 4
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置热加载观察者已注册
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] Instagram私信任务执行器初始化完成
2025-07-28 07:41:35 - InstagramFollowTask - INFO - [模拟器4] Instagram关注任务配置加载完成
2025-07-28 07:41:35 - InstagramFollowTask - INFO - [模拟器4] Instagram关注任务执行器初始化完成
2025-07-28 07:41:35 - InstagramFollowTask - INFO - [模拟器4] 关注模式已设置为: direct
2025-07-28 07:41:35 - InstagramFollowTask - INFO - [模拟器4] 开始执行Instagram关注任务
2025-07-28 07:41:35 - InstagramFollowTask - WARNING - [模拟器4] 任务开始时间未由线程传递，在此设置
2025-07-28 07:41:35 - InstagramFollowTask - INFO - [模拟器4] 任务超时设置: 14秒，已运行: 0.00秒
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] 模拟器Android系统运行正常，桌面稳定
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] 开始检查应用安装状态
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] 📊 应用安装状态检测结果:
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] ✅ 所有必要应用已安装
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] 开始启动V2Ray应用
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] V2Ray启动命令执行成功，等待应用加载
2025-07-28 07:41:35 - InstagramDMTask - INFO - [模拟器4] V2Ray应用启动结果: 成功
2025-07-28 07:41:38 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray应用启动成功
2025-07-28 07:41:38 - InstagramDMTask - INFO - [模拟器4] 开始检查V2Ray节点列表状态
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器4] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器4] 开始连接V2Ray节点
2025-07-28 07:41:40 - InstagramTaskThread - INFO - [模拟器5] 窗口排列完成
2025-07-28 07:41:40 - InstagramFollowTaskThread - INFO - [模拟器5] 开始执行Instagram直接关注任务
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] 雷电模拟器API初始化成功
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] 已设置ld.emulator_id = 5
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置热加载观察者已注册
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] Instagram私信任务执行器初始化完成
2025-07-28 07:41:40 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务配置加载完成
2025-07-28 07:41:40 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务执行器初始化完成
2025-07-28 07:41:40 - InstagramFollowTask - INFO - [模拟器5] 关注模式已设置为: direct
2025-07-28 07:41:40 - InstagramFollowTask - INFO - [模拟器5] 开始执行Instagram关注任务
2025-07-28 07:41:40 - InstagramFollowTask - WARNING - [模拟器5] 任务开始时间未由线程传递，在此设置
2025-07-28 07:41:40 - InstagramFollowTask - INFO - [模拟器5] 任务超时设置: 14秒，已运行: 0.00秒
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] 模拟器Android系统运行正常，桌面稳定
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] 开始检查应用安装状态
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] 📊 应用安装状态检测结果:
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] ✅ 所有必要应用已安装
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] 开始启动V2Ray应用
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] V2Ray启动命令执行成功，等待应用加载
2025-07-28 07:41:40 - InstagramDMTask - INFO - [模拟器5] V2Ray应用启动结果: 成功
2025-07-28 07:41:41 - InstagramDMTask - INFO - [模拟器4] 当前连接状态: 未连接
2025-07-28 07:41:41 - InstagramDMTask - INFO - [模拟器4] V2Ray节点未连接，开始连接
2025-07-28 07:41:42 - InstagramDMTask - INFO - [模拟器4] 已点击连接按钮，等待连接完成
2025-07-28 07:41:43 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray应用启动成功
2025-07-28 07:41:43 - InstagramDMTask - INFO - [模拟器5] 开始检查V2Ray节点列表状态
2025-07-28 07:41:45 - InstagramDMTask - INFO - [模拟器4] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 07:41:45 - InstagramDMTask - INFO - [模拟器4] V2Ray节点连接成功
2025-07-28 07:41:45 - InstagramDMTask - INFO - [模拟器4] 开始测试V2Ray节点延迟
2025-07-28 07:41:45 - InstagramDMTask - INFO - [模拟器4] 开始V2Ray节点延迟测试
2025-07-28 07:41:45 - InstagramDMTask - INFO - [模拟器5] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 07:41:45 - InstagramDMTask - INFO - [模拟器5] 开始连接V2Ray节点
2025-07-28 07:41:45 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 07:41:45 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 07:41:45 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 07:41:45 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 07:41:45 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-28 07:41:45 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-28 07:41:45 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 07:41:46 - InstagramDMTask - INFO - [模拟器4] 当前测试状态: 已连接，点击测试连接
2025-07-28 07:41:46 - InstagramDMTask - INFO - [模拟器4] 点击开始延迟测试
2025-07-28 07:41:46 - InstagramDMTask - INFO - [模拟器5] 当前连接状态: 未连接
2025-07-28 07:41:46 - InstagramDMTask - INFO - [模拟器5] V2Ray节点未连接，开始连接
2025-07-28 07:41:46 - InstagramDMTask - INFO - [模拟器4] 已点击测试按钮，等待测试结果
2025-07-28 07:41:47 - InstagramDMTask - INFO - [模拟器5] 已点击连接按钮，等待连接完成
2025-07-28 07:41:47 - InstagramDMTask - INFO - [模拟器4] 测试状态监控 (1/30): 失败：: io: read/write on closed pipe
2025-07-28 07:41:47 - InstagramDMTask - ERROR - [模拟器4] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: io: read/write on closed pipe
2025-07-28 07:41:47 - InstagramDMTask - INFO - [模拟器4] 点击失败状态重置UI
2025-07-28 07:41:48 - InstagramDMTask - INFO - [模拟器4] 已点击失败状态，等待UI重置
2025-07-28 07:41:48 - InstagramDMTask - INFO - [模拟器4] 继续等待测试结果 (1/3)
2025-07-28 07:41:49 - InstagramDMTask - ERROR - [模拟器4] ❌ V2Ray节点延迟测试失败 (2/3): 失败：: io: read/write on closed pipe
2025-07-28 07:41:49 - InstagramDMTask - INFO - [模拟器4] 点击失败状态重置UI
2025-07-28 07:41:49 - InstagramDMTask - INFO - [模拟器4] 已点击失败状态，等待UI重置
2025-07-28 07:41:49 - InstagramDMTask - INFO - [模拟器5] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 07:41:49 - InstagramDMTask - INFO - [模拟器5] V2Ray节点连接成功
2025-07-28 07:41:49 - InstagramDMTask - INFO - [模拟器5] 开始测试V2Ray节点延迟
2025-07-28 07:41:49 - InstagramDMTask - INFO - [模拟器5] 开始V2Ray节点延迟测试
2025-07-28 07:41:50 - InstagramDMTask - INFO - [模拟器4] 继续等待测试结果 (2/3)
2025-07-28 07:41:51 - InstagramDMTask - INFO - [模拟器5] 当前测试状态: 已连接，点击测试连接
2025-07-28 07:41:51 - InstagramDMTask - INFO - [模拟器5] 点击开始延迟测试
2025-07-28 07:41:51 - InstagramDMTask - INFO - [模拟器5] 已点击测试按钮，等待测试结果
2025-07-28 07:41:51 - InstagramDMTask - ERROR - [模拟器4] ❌ V2Ray节点延迟测试失败 (3/3): 失败：: io: read/write on closed pipe
2025-07-28 07:41:51 - InstagramDMTask - ERROR - [模拟器4] 达到最大失败次数 (3)，触发节点切换
2025-07-28 07:41:51 - InstagramDMTask - ERROR - [模拟器4] V2Ray节点延迟测试失败或超时
2025-07-28 07:41:51 - InstagramDMTask - ERROR - [模拟器4] V2Ray节点延迟测试失败，开始节点切换 (第1/8次)
2025-07-28 07:41:51 - InstagramDMTask - INFO - [模拟器4] 开始V2Ray节点切换流程
2025-07-28 07:41:51 - InstagramDMTask - INFO - [模拟器4] 开始智能滑动浏览节点列表
2025-07-28 07:41:52 - InstagramDMTask - INFO - [模拟器5] 测试状态监控 (1/30): 失败：: io: read/write on closed pipe
2025-07-28 07:41:52 - InstagramDMTask - ERROR - [模拟器5] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: io: read/write on closed pipe
2025-07-28 07:41:52 - InstagramDMTask - INFO - [模拟器5] 点击失败状态重置UI
2025-07-28 07:41:52 - InstagramDMTask - INFO - [模拟器5] 已点击失败状态，等待UI重置
2025-07-28 07:41:52 - InstagramDMTask - INFO - [模拟器4] 计划执行 3 次随机滑动
2025-07-28 07:41:52 - InstagramDMTask - INFO - [模拟器4] 第 1/3 次滑动: up, 距离比例: 0.50
2025-07-28 07:41:53 - InstagramDMTask - INFO - [模拟器5] 继续等待测试结果 (1/3)
2025-07-28 07:41:53 - InstagramDMTask - INFO - [模拟器4] 第 1 次滑动失败
2025-07-28 07:41:53 - InstagramDMTask - INFO - [模拟器4] 滑动后等待 0.2 秒
2025-07-28 07:41:53 - InstagramDMTask - INFO - [模拟器4] 第 2/3 次滑动: down, 距离比例: 0.32
2025-07-28 07:41:54 - InstagramDMTask - INFO - [模拟器4] 第 2 次滑动失败
2025-07-28 07:41:54 - InstagramDMTask - INFO - [模拟器4] 滑动后等待 0.2 秒
2025-07-28 07:41:54 - InstagramDMTask - INFO - [模拟器4] 第 3/3 次滑动: up, 距离比例: 0.54
2025-07-28 07:41:54 - InstagramDMTask - ERROR - [模拟器5] ❌ V2Ray节点延迟测试失败 (2/3): 失败：: io: read/write on closed pipe
2025-07-28 07:41:54 - InstagramDMTask - INFO - [模拟器5] 点击失败状态重置UI
2025-07-28 07:41:54 - InstagramDMTask - INFO - [模拟器5] 已点击失败状态，等待UI重置
2025-07-28 07:41:54 - InstagramDMTask - INFO - [模拟器4] 第 3 次滑动失败
2025-07-28 07:41:54 - InstagramDMTask - INFO - [模拟器4] 滑动后等待 0.2 秒
2025-07-28 07:41:55 - InstagramDMTask - INFO - [模拟器4] ✅ 智能滑动完成，共执行 3 次
2025-07-28 07:41:55 - InstagramDMTask - INFO - [模拟器4] 开始随机选择V2Ray节点
2025-07-28 07:41:55 - InstagramDMTask - INFO - [模拟器5] 继续等待测试结果 (2/3)
2025-07-28 07:41:56 - InstagramDMTask - INFO - [模拟器4] 找到 6 个可用节点
2025-07-28 07:41:56 - InstagramDMTask - INFO - [模拟器4] 随机选择节点: V4-153|香港|x2.0
2025-07-28 07:41:56 - InstagramDMTask - INFO - [模拟器4] 节点位置: [11,134][88,148]
2025-07-28 07:41:56 - InstagramDMTask - INFO - [模拟器4] ✅ 成功点击节点: V4-153|香港|x2.0
2025-07-28 07:41:56 - InstagramDMTask - INFO - [模拟器4] 等待1秒让节点切换完成
2025-07-28 07:41:56 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray节点延迟测试成功: 连接成功：延时 249 毫秒
2025-07-28 07:41:56 - InstagramDMTask - INFO - [模拟器5] 等待5秒后进入下一阶段
2025-07-28 07:41:57 - InstagramDMTask - INFO - [模拟器4] 开始连接V2Ray节点
2025-07-28 07:41:58 - InstagramDMTask - INFO - [模拟器4] 当前连接状态: 已连接，点击测试连接
2025-07-28 07:41:58 - InstagramDMTask - INFO - [模拟器4] V2Ray节点已连接，无需重复连接
2025-07-28 07:41:58 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray节点切换成功
2025-07-28 07:41:58 - InstagramDMTask - INFO - [模拟器4] 节点切换成功，重新测试延迟
2025-07-28 07:41:58 - InstagramDMTask - INFO - [模拟器4] 开始测试V2Ray节点延迟
2025-07-28 07:41:58 - InstagramDMTask - INFO - [模拟器4] 开始V2Ray节点延迟测试
2025-07-28 07:41:59 - InstagramDMTask - INFO - [模拟器4] 当前测试状态: 已连接，点击测试连接
2025-07-28 07:41:59 - InstagramDMTask - INFO - [模拟器4] 点击开始延迟测试
2025-07-28 07:42:00 - InstagramDMTask - INFO - [模拟器4] 已点击测试按钮，等待测试结果
2025-07-28 07:42:01 - InstagramDMTask - INFO - [模拟器4] 测试状态监控 (1/30): 失败：: io: read/write on closed pipe
2025-07-28 07:42:01 - InstagramDMTask - ERROR - [模拟器4] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: io: read/write on closed pipe
2025-07-28 07:42:01 - InstagramDMTask - INFO - [模拟器4] 点击失败状态重置UI
2025-07-28 07:42:01 - InstagramDMTask - INFO - [模拟器5] 开始启动Instagram应用
2025-07-28 07:42:01 - InstagramDMTask - INFO - [模拟器4] 已点击失败状态，等待UI重置
2025-07-28 07:42:01 - InstagramDMTask - INFO - [模拟器5] Instagram启动命令执行成功，等待应用加载
2025-07-28 07:42:02 - InstagramDMTask - INFO - [模拟器4] 继续等待测试结果 (1/3)
2025-07-28 07:42:03 - InstagramDMTask - ERROR - [模拟器4] ❌ V2Ray节点延迟测试失败 (2/3): 失败：: io: read/write on closed pipe
2025-07-28 07:42:03 - InstagramDMTask - INFO - [模拟器4] 点击失败状态重置UI
2025-07-28 07:42:03 - InstagramDMTask - INFO - [模拟器4] 已点击失败状态，等待UI重置
2025-07-28 07:42:04 - InstagramDMTask - INFO - [模拟器4] 继续等待测试结果 (2/3)
2025-07-28 07:42:04 - InstagramDMTask - INFO - [模拟器5] ✅ Instagram应用启动命令执行完成
2025-07-28 07:42:04 - InstagramDMTask - INFO - [模拟器5] Instagram启动检测 第1/5次
2025-07-28 07:42:04 - InstagramDMTask - INFO - [模拟器5] 总任务超时(14秒)，Instagram启动检测中断，耗时: 24.44秒
2025-07-28 07:42:04 - InstagramDMTask - ERROR - [模拟器5] 检测到异常状态: 异常-总任务超时
2025-07-28 07:42:04 - LeiDianNativeAPI - INFO - 雷电原生API初始化成功: G:/leidian/LDPlayer9
2025-07-28 07:42:04 - NativeScreenshotEngine - INFO - 专业截图引擎初始化成功
2025-07-28 07:42:04 - ScreenshotManager - INFO - 原生截图引擎初始化成功
2025-07-28 07:42:04 - NativeScreenshotEngine - INFO - 开始截取模拟器 5 的截图
2025-07-28 07:42:04 - NativeScreenshotEngine - INFO - 模拟器 5 截图成功: test_screenshots\emulator_5_Instagram_总任务超时_1_20250728_074204.png
2025-07-28 07:42:04 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_5_Instagram_总任务超时_1_20250728_074204.png
2025-07-28 07:42:04 - InstagramDMTask - INFO - [模拟器5] 异常状态截图已保存: 异常-总任务超时
2025-07-28 07:42:04 - InstagramDMTask - INFO - [模拟器5] 任务终止原因: 异常-总任务超时
2025-07-28 07:42:04 - InstagramDMTask - INFO - [模拟器5] 开始关闭模拟器...
2025-07-28 07:42:05 - InstagramDMTask - INFO - [模拟器5] 模拟器已关闭
2025-07-28 07:42:05 - InstagramDMTask - INFO - [模拟器5] 任务状态更新: 失败 - 异常-总任务超时
2025-07-28 07:42:05 - InstagramFollowTask - INFO - [模拟器5] Instagram页面状态检测结果: 任务终止-异常-总任务超时
2025-07-28 07:42:05 - InstagramFollowTask - ERROR - [模拟器5] ❌ Instagram任务终止
2025-07-28 07:42:05 - InstagramFollowTaskThread - ERROR - [模拟器5] Instagram直接关注任务执行失败: 任务终止-异常-总任务超时
2025-07-28 07:42:05 - InstagramDMTask - ERROR - [模拟器4] ❌ V2Ray节点延迟测试失败 (3/3): 失败：: io: read/write on closed pipe
2025-07-28 07:42:05 - InstagramDMTask - ERROR - [模拟器4] 达到最大失败次数 (3)，触发节点切换
2025-07-28 07:42:05 - InstagramDMTask - ERROR - [模拟器4] V2Ray节点延迟测试失败或超时
2025-07-28 07:42:05 - InstagramDMTask - ERROR - [模拟器4] V2Ray节点延迟测试失败，开始节点切换 (第2/8次)
2025-07-28 07:42:05 - InstagramDMTask - INFO - [模拟器4] 开始V2Ray节点切换流程
2025-07-28 07:42:05 - InstagramDMTask - INFO - [模拟器4] 开始智能滑动浏览节点列表
2025-07-28 07:42:05 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 07:42:05 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 07:42:05 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 07:42:05 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 07:42:05 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-28 07:42:05 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-28 07:42:05 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 07:42:06 - InstagramDMTask - INFO - [模拟器4] 计划执行 1 次随机滑动
2025-07-28 07:42:06 - InstagramDMTask - INFO - [模拟器4] 第 1/1 次滑动: down, 距离比例: 0.60
2025-07-28 07:42:07 - InstagramDMTask - INFO - [模拟器4] 第 1 次滑动失败
2025-07-28 07:42:07 - InstagramDMTask - INFO - [模拟器4] 滑动后等待 0.2 秒
2025-07-28 07:42:07 - InstagramDMTask - INFO - [模拟器4] ✅ 智能滑动完成，共执行 1 次
2025-07-28 07:42:07 - InstagramDMTask - INFO - [模拟器4] 开始随机选择V2Ray节点
2025-07-28 07:42:09 - InstagramDMTask - INFO - [模拟器4] 找到 6 个可用节点
2025-07-28 07:42:09 - InstagramDMTask - INFO - [模拟器4] 随机选择节点: V4-222|香港|x1.5
2025-07-28 07:42:09 - InstagramDMTask - INFO - [模拟器4] 节点位置: [11,230][88,244]
2025-07-28 07:42:09 - InstagramDMTask - INFO - [模拟器4] ✅ 成功点击节点: V4-222|香港|x1.5
2025-07-28 07:42:09 - InstagramDMTask - INFO - [模拟器4] 等待1秒让节点切换完成
2025-07-28 07:42:10 - InstagramDMTask - INFO - [模拟器4] 开始连接V2Ray节点
2025-07-28 07:42:11 - InstagramDMTask - INFO - [模拟器4] 当前连接状态: 已连接，点击测试连接
2025-07-28 07:42:11 - InstagramDMTask - INFO - [模拟器4] V2Ray节点已连接，无需重复连接
2025-07-28 07:42:11 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray节点切换成功
2025-07-28 07:42:11 - InstagramDMTask - INFO - [模拟器4] 节点切换成功，重新测试延迟
2025-07-28 07:42:11 - InstagramDMTask - INFO - [模拟器4] 开始测试V2Ray节点延迟
2025-07-28 07:42:11 - InstagramDMTask - INFO - [模拟器4] 开始V2Ray节点延迟测试
2025-07-28 07:42:12 - InstagramDMTask - INFO - [模拟器4] 当前测试状态: 已连接，点击测试连接
2025-07-28 07:42:12 - InstagramDMTask - INFO - [模拟器4] 点击开始延迟测试
2025-07-28 07:42:12 - InstagramDMTask - INFO - [模拟器4] 已点击测试按钮，等待测试结果
2025-07-28 07:42:14 - InstagramDMTask - INFO - [模拟器4] 测试状态监控 (1/30): 测试中…
2025-07-28 07:42:14 - InstagramDMTask - INFO - [模拟器4] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:42:16 - InstagramDMTask - ERROR - [模拟器4] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: io: read/write on closed pipe
2025-07-28 07:42:16 - InstagramDMTask - INFO - [模拟器4] 点击失败状态重置UI
2025-07-28 07:42:16 - InstagramDMTask - INFO - [模拟器4] 已点击失败状态，等待UI重置
2025-07-28 07:42:17 - InstagramDMTask - INFO - [模拟器4] 继续等待测试结果 (1/3)
2025-07-28 07:42:18 - InstagramDMTask - INFO - [模拟器4] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:42:20 - InstagramDMTask - ERROR - [模拟器4] ❌ V2Ray节点延迟测试失败 (2/3): 失败：: io: read/write on closed pipe
2025-07-28 07:42:20 - InstagramDMTask - INFO - [模拟器4] 点击失败状态重置UI
2025-07-28 07:42:20 - InstagramDMTask - INFO - [模拟器4] 已点击失败状态，等待UI重置
2025-07-28 07:42:20 - InstagramDMTask - INFO - [模拟器4] 继续等待测试结果 (2/3)
2025-07-28 07:42:22 - InstagramDMTask - INFO - [模拟器4] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:42:23 - InstagramDMTask - ERROR - [模拟器4] ❌ V2Ray节点延迟测试失败 (3/3): 失败：: io: read/write on closed pipe
2025-07-28 07:42:23 - InstagramDMTask - ERROR - [模拟器4] 达到最大失败次数 (3)，触发节点切换
2025-07-28 07:42:23 - InstagramDMTask - ERROR - [模拟器4] V2Ray节点延迟测试失败或超时
2025-07-28 07:42:23 - InstagramDMTask - ERROR - [模拟器4] V2Ray节点延迟测试失败，开始节点切换 (第3/8次)
2025-07-28 07:42:23 - InstagramDMTask - INFO - [模拟器4] 开始V2Ray节点切换流程
2025-07-28 07:42:23 - InstagramDMTask - INFO - [模拟器4] 开始智能滑动浏览节点列表
2025-07-28 07:42:25 - InstagramDMTask - INFO - [模拟器4] 计划执行 1 次随机滑动
2025-07-28 07:42:25 - InstagramDMTask - INFO - [模拟器4] 第 1/1 次滑动: up, 距离比例: 0.69
2025-07-28 07:42:25 - InstagramDMTask - INFO - [模拟器4] 第 1 次滑动失败
2025-07-28 07:42:25 - InstagramDMTask - INFO - [模拟器4] 滑动后等待 0.2 秒
2025-07-28 07:42:25 - InstagramDMTask - INFO - [模拟器4] ✅ 智能滑动完成，共执行 1 次
2025-07-28 07:42:25 - InstagramDMTask - INFO - [模拟器4] 开始随机选择V2Ray节点
2025-07-28 07:42:25 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 07:42:25 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 07:42:25 - TaskActivityHeartbeatManager - INFO - 模拟器 5 疑似心跳异常，进入观察期 | 无活动时间: 21.0秒
2025-07-28 07:42:25 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 07:42:25 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 07:42:25 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 07:42:27 - InstagramDMTask - INFO - [模拟器4] 找到 7 个可用节点
2025-07-28 07:42:27 - InstagramDMTask - INFO - [模拟器4] 随机选择节点: V4-121|香港|x1.0
2025-07-28 07:42:27 - InstagramDMTask - INFO - [模拟器4] 节点位置: [11,58][88,72]
2025-07-28 07:42:27 - InstagramDMTask - INFO - [模拟器4] ✅ 成功点击节点: V4-121|香港|x1.0
2025-07-28 07:42:27 - InstagramDMTask - INFO - [模拟器4] 等待1秒让节点切换完成
2025-07-28 07:42:28 - InstagramDMTask - INFO - [模拟器4] 开始连接V2Ray节点
2025-07-28 07:42:29 - InstagramDMTask - INFO - [模拟器4] 当前连接状态: 已连接，点击测试连接
2025-07-28 07:42:29 - InstagramDMTask - INFO - [模拟器4] V2Ray节点已连接，无需重复连接
2025-07-28 07:42:29 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray节点切换成功
2025-07-28 07:42:29 - InstagramDMTask - INFO - [模拟器4] 节点切换成功，重新测试延迟
2025-07-28 07:42:29 - InstagramDMTask - INFO - [模拟器4] 开始测试V2Ray节点延迟
2025-07-28 07:42:29 - InstagramDMTask - INFO - [模拟器4] 开始V2Ray节点延迟测试
2025-07-28 07:42:30 - InstagramDMTask - INFO - [模拟器4] 当前测试状态: 已连接，点击测试连接
2025-07-28 07:42:30 - InstagramDMTask - INFO - [模拟器4] 点击开始延迟测试
2025-07-28 07:42:30 - InstagramDMTask - INFO - [模拟器4] 已点击测试按钮，等待测试结果
2025-07-28 07:42:32 - InstagramDMTask - INFO - [模拟器4] 测试状态监控 (1/30): 连接成功：延时 228 毫秒
2025-07-28 07:42:32 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray节点延迟测试成功: 连接成功：延时 228 毫秒
2025-07-28 07:42:32 - InstagramDMTask - INFO - [模拟器4] 等待5秒后进入下一阶段
2025-07-28 07:42:37 - InstagramDMTask - INFO - [模拟器4] 开始启动Instagram应用
2025-07-28 07:42:37 - InstagramDMTask - INFO - [模拟器4] Instagram启动命令执行成功，等待应用加载
2025-07-28 07:42:40 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram应用启动命令执行完成
2025-07-28 07:42:40 - InstagramDMTask - INFO - [模拟器4] Instagram启动检测 第1/5次
2025-07-28 07:42:40 - InstagramDMTask - INFO - [模拟器4] 总任务超时(14秒)，Instagram启动检测中断，耗时: 64.95秒
2025-07-28 07:42:40 - InstagramDMTask - ERROR - [模拟器4] 检测到异常状态: 异常-总任务超时
2025-07-28 07:42:40 - NativeScreenshotEngine - INFO - 开始截取模拟器 4 的截图
2025-07-28 07:42:40 - NativeScreenshotEngine - INFO - 模拟器 4 截图成功: test_screenshots\emulator_4_Instagram_总任务超时_1_20250728_074240.png
2025-07-28 07:42:40 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_4_Instagram_总任务超时_1_20250728_074240.png
2025-07-28 07:42:40 - InstagramDMTask - INFO - [模拟器4] 异常状态截图已保存: 异常-总任务超时
2025-07-28 07:42:40 - InstagramDMTask - INFO - [模拟器4] 任务终止原因: 异常-总任务超时
2025-07-28 07:42:40 - InstagramDMTask - INFO - [模拟器4] 开始关闭模拟器...
2025-07-28 07:42:40 - InstagramDMTask - INFO - [模拟器4] 模拟器已关闭
2025-07-28 07:42:40 - InstagramDMTask - INFO - [模拟器4] 任务状态更新: 失败 - 异常-总任务超时
2025-07-28 07:42:40 - InstagramFollowTask - INFO - [模拟器4] Instagram页面状态检测结果: 任务终止-异常-总任务超时
2025-07-28 07:42:40 - InstagramFollowTask - ERROR - [模拟器4] ❌ Instagram任务终止
2025-07-28 07:42:40 - InstagramFollowTaskThread - ERROR - [模拟器4] Instagram直接关注任务执行失败: 任务终止-异常-总任务超时
2025-07-28 07:42:45 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 07:42:45 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 07:42:45 - TaskActivityHeartbeatManager - ERROR - 模拟器 5 确认心跳异常 | 总无活动时间: 41.0秒
2025-07-28 07:42:45 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 07:42:45 - TaskActivityHeartbeatManager - INFO - 心跳异常处理开始，失败次数: 1/1 | emulator_id: 5
2025-07-28 07:42:45 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 07:42:45 - NativeScreenshotEngine - INFO - 开始截取模拟器 5 的截图
2025-07-28 07:42:45 - UnifiedEmulatorManager - INFO - 心跳检测失败: 心跳异常, 失败次数: 1 | emulator_id: 5
2025-07-28 07:42:45 - MainWindowV2 - INFO - 模拟器5心跳异常: 心跳异常 (失败次数: 1)
2025-07-28 07:42:45 - MainWindowV2 - WARNING - 模拟器5心跳检测失败: 心跳异常, 失败次数: 1
2025-07-28 07:42:45 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-28 07:42:46 - NativeScreenshotEngine - ERROR - 模拟器 5 绑定句柄无效: 0
2025-07-28 07:42:46 - ScreenshotManager - ERROR - 原生截图引擎截图失败
2025-07-28 07:42:46 - TaskActivityHeartbeatManager - INFO - 开始熔断处理 | emulator_id: 5
2025-07-28 07:42:46 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 5 | remaining_running: 1
2025-07-28 07:42:46 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 运行中 -> 异常
2025-07-28 07:42:46 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 运行中 | new_state: 异常
2025-07-28 07:42:46 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 运行中 -> 异常
2025-07-28 07:42:46 - MainWindowV2 - INFO - 模拟器5: 运行中 -> 异常
2025-07-28 07:42:46 - InstagramTaskManager - INFO - 清理模拟器5的运行中Instagram线程，原因: 模拟器状态变为异常
2025-07-28 07:42:46 - InstagramTaskManager - INFO - 模拟器5的Instagram线程已清理完成，当前并发: 1/2
2025-07-28 07:42:46 - InstagramTaskManager - INFO - 为接力排队模拟器15创建Instagram任务线程
2025-07-28 07:42:46 - InstagramFollowTaskThread - INFO - [模拟器15] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 07:42:46 - InstagramFollowTaskManager - INFO - 为接力模拟器15创建并启动Instagram关注任务线程 - 当前并发: 2/2
2025-07-28 07:42:46 - InstagramFollowTaskManager - INFO - 为模拟器 15 创建Instagram关注任务线程，模式: direct
2025-07-28 07:42:46 - TaskActivityHeartbeatManager - INFO - 模拟器已标记为异常并清理状态 | emulator_id: 5 | removed_from_running: True | removed_from_active: False | total_running: 1 | total_active: 0
2025-07-28 07:42:46 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已从任务活动监控移除
2025-07-28 07:42:46 - TaskActivityHeartbeatManager - INFO - 发现排队模拟器 15，开始任务接力
2025-07-28 07:42:46 - InstagramTaskThread - INFO - [模拟器15] 开始等待启动完成
2025-07-28 07:42:46 - InstagramTaskThread - ERROR - [模拟器15] ❌ StartupManager已完成处理但未成功，状态: 排队中
2025-07-28 07:42:46 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 1 | max_concurrent: 2
2025-07-28 07:42:46 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/2)
2025-07-28 07:42:46 - UnifiedEmulatorManager - INFO - 模拟器已切换: 5 -> -1
2025-07-28 07:42:46 - MainWindowV2 - INFO - 模拟器自动切换: 5 -> -1
2025-07-28 07:42:46 - MainWindowV2 - INFO - 模拟器已自动切换: 5 -> -1
2025-07-28 07:42:46 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 07:42:47 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器15状态变化: 排队中 -> 启动中
2025-07-28 07:42:47 - Emulator - INFO - 模拟器状态变化 | emulator_id: 15 | old_state: 排队中 | new_state: 启动中
2025-07-28 07:42:47 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器15状态变化: 排队中 -> 启动中
2025-07-28 07:42:47 - MainWindowV2 - INFO - 模拟器15: 排队中 -> 启动中
2025-07-28 07:42:47 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 15
2025-07-28 07:42:47 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 07:42:47 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/2)
2025-07-28 07:42:47 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 15
2025-07-28 07:43:00 - Emulator - INFO - Android系统启动完成 | emulator_id: 15 | elapsed_time: 13.1秒
2025-07-28 07:43:00 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器15状态变化: 启动中 -> 运行中
2025-07-28 07:43:00 - InstagramFollowTaskManager - INFO - 启动模拟器15的Instagram关注任务线程 - 当前并发: 3/2
2025-07-28 07:43:00 - Emulator - INFO - 模拟器状态变化 | emulator_id: 15 | old_state: 启动中 | new_state: 运行中
2025-07-28 07:43:00 - Emulator - INFO - 模拟器启动成功 | emulator_id: 15 | running_count: 2
2025-07-28 07:43:00 - TaskActivityHeartbeatManager - INFO - 模拟器 15 已添加到任务活动监控，失败计数: 0
2025-07-28 07:43:00 - InstagramTaskThread - INFO - [模拟器15] 开始等待启动完成
2025-07-28 07:43:00 - MainWindowV2 - INFO - 任务完成: 模拟器15, 任务start
2025-07-28 07:43:00 - InstagramTaskThread - INFO - [模拟器15] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 07:43:00 - MainWindowV2 - INFO - 模拟器15启动成功
2025-07-28 07:43:00 - InstagramTaskThread - INFO - [模拟器15] 开始窗口排列
2025-07-28 07:43:00 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=15, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 07:43:00 - WindowArrangementManager - INFO - 模拟器15启动完成，立即触发窗口排列
2025-07-28 07:43:00 - MainWindowV2 - WARNING - 未找到模拟器15，无法更新状态
2025-07-28 07:43:00 - MainWindowV2 - INFO - 模拟器15: 启动中 -> 运行中
2025-07-28 07:43:00 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 0 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 66.66666666666666 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 07:43:00 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行2个, 失败0个 (并发槽位:2/2)
2025-07-28 07:43:00 - MainWindowV2 - INFO - 模拟器15: 启动中 -> 运行中, PID: 21348
2025-07-28 07:43:00 - MainWindowV2 - INFO - 模拟器15: 启动中 -> 运行中
2025-07-28 07:43:01 - StartupManager - INFO - 调度器已停止
2025-07-28 07:43:02 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 07:43:02 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 07:43:02 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 07:43:02 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 07:43:02 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 07:43:02 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 07:43:02 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-15' 已在网格位置 (1, 2)，保持不动
2025-07-28 07:43:02 - WindowArrangementManager - INFO - 所有窗口都已在网格位置，无需排列
2025-07-28 07:43:02 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 07:43:02 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 07:43:05 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 07:43:05 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 07:43:05 - TaskActivityHeartbeatManager - INFO - 模拟器 4 疑似心跳异常，进入观察期 | 无活动时间: 25.4秒
2025-07-28 07:43:05 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 15
2025-07-28 07:43:05 - MainWindowV2 - WARNING - 模拟器15心跳状态更新未产生变化
2025-07-28 07:43:05 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 07:43:08 - InstagramTaskThread - INFO - [模拟器15] 窗口排列完成
2025-07-28 07:43:08 - InstagramFollowTaskThread - INFO - [模拟器15] 开始执行Instagram直接关注任务
2025-07-28 07:43:08 - InstagramDMTask - INFO - [模拟器15] 雷电模拟器API初始化成功
2025-07-28 07:43:08 - InstagramDMTask - INFO - [模拟器15] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 07:43:08 - InstagramDMTask - INFO - [模拟器15] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 07:43:08 - InstagramDMTask - INFO - [模拟器15] 已设置ld.emulator_id = 15
2025-07-28 07:43:08 - InstagramDMTask - INFO - [模拟器15] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 07:43:08 - InstagramDMTask - INFO - [模拟器15] Instagram任务配置热加载观察者已注册
2025-07-28 07:43:08 - InstagramDMTask - INFO - [模拟器15] Instagram私信任务执行器初始化完成
2025-07-28 07:43:08 - InstagramFollowTask - INFO - [模拟器15] Instagram关注任务配置加载完成
2025-07-28 07:43:08 - InstagramFollowTask - INFO - [模拟器15] Instagram关注任务执行器初始化完成
2025-07-28 07:43:08 - InstagramFollowTask - INFO - [模拟器15] 关注模式已设置为: direct
2025-07-28 07:43:08 - InstagramFollowTask - INFO - [模拟器15] 开始执行Instagram关注任务
2025-07-28 07:43:08 - InstagramFollowTask - WARNING - [模拟器15] 任务开始时间未由线程传递，在此设置
2025-07-28 07:43:08 - InstagramFollowTask - INFO - [模拟器15] 任务超时设置: 14秒，已运行: 0.00秒
2025-07-28 07:43:08 - InstagramDMTask - INFO - [模拟器15] 模拟器Android系统运行正常，桌面稳定
2025-07-28 07:43:08 - InstagramDMTask - INFO - [模拟器15] 开始检查应用安装状态
2025-07-28 07:43:08 - InstagramDMTask - INFO - [模拟器15] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 07:43:08 - InstagramDMTask - INFO - [模拟器15] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 07:43:08 - InstagramDMTask - INFO - [模拟器15] 📊 应用安装状态检测结果:
2025-07-28 07:43:08 - InstagramDMTask - INFO - [模拟器15] ✅ 所有必要应用已安装
2025-07-28 07:43:08 - InstagramDMTask - INFO - [模拟器15] 开始启动V2Ray应用
2025-07-28 07:43:09 - InstagramDMTask - INFO - [模拟器15] V2Ray启动命令执行成功，等待应用加载
2025-07-28 07:43:09 - InstagramDMTask - INFO - [模拟器15] V2Ray应用启动结果: 成功
2025-07-28 07:43:12 - InstagramDMTask - INFO - [模拟器15] ✅ V2Ray应用启动成功
2025-07-28 07:43:12 - InstagramDMTask - INFO - [模拟器15] 开始检查V2Ray节点列表状态
2025-07-28 07:43:13 - InstagramDMTask - INFO - [模拟器15] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 07:43:13 - InstagramDMTask - INFO - [模拟器15] 开始连接V2Ray节点
2025-07-28 07:43:14 - InstagramDMTask - INFO - [模拟器15] 当前连接状态: 未连接
2025-07-28 07:43:14 - InstagramDMTask - INFO - [模拟器15] V2Ray节点未连接，开始连接
2025-07-28 07:43:15 - InstagramDMTask - INFO - [模拟器15] 已点击连接按钮，等待连接完成
2025-07-28 07:43:18 - InstagramDMTask - INFO - [模拟器15] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 07:43:18 - InstagramDMTask - INFO - [模拟器15] V2Ray节点连接成功
2025-07-28 07:43:18 - InstagramDMTask - INFO - [模拟器15] 开始测试V2Ray节点延迟
2025-07-28 07:43:18 - InstagramDMTask - INFO - [模拟器15] 开始V2Ray节点延迟测试
2025-07-28 07:43:19 - InstagramDMTask - INFO - [模拟器15] 当前测试状态: 已连接，点击测试连接
2025-07-28 07:43:19 - InstagramDMTask - INFO - [模拟器15] 点击开始延迟测试
2025-07-28 07:43:19 - InstagramDMTask - INFO - [模拟器15] 已点击测试按钮，等待测试结果
2025-07-28 07:43:20 - InstagramDMTask - INFO - [模拟器15] 测试状态监控 (1/30): 测试中…
2025-07-28 07:43:20 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:22 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:23 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:24 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:25 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 07:43:25 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 07:43:25 - TaskActivityHeartbeatManager - ERROR - 模拟器 4 确认心跳异常 | 总无活动时间: 45.4秒
2025-07-28 07:43:25 - TaskActivityHeartbeatManager - INFO - 心跳异常处理开始，失败次数: 1/1 | emulator_id: 4
2025-07-28 07:43:25 - NativeScreenshotEngine - INFO - 开始截取模拟器 4 的截图
2025-07-28 07:43:25 - UnifiedEmulatorManager - INFO - 心跳检测失败: 心跳异常, 失败次数: 1 | emulator_id: 4
2025-07-28 07:43:25 - MainWindowV2 - INFO - 模拟器4心跳异常: 心跳异常 (失败次数: 1)
2025-07-28 07:43:25 - MainWindowV2 - WARNING - 模拟器4心跳检测失败: 心跳异常, 失败次数: 1
2025-07-28 07:43:25 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 07:43:26 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:26 - NativeScreenshotEngine - ERROR - 模拟器 4 绑定句柄无效: 0
2025-07-28 07:43:26 - ScreenshotManager - ERROR - 原生截图引擎截图失败
2025-07-28 07:43:26 - TaskActivityHeartbeatManager - INFO - 开始熔断处理 | emulator_id: 4
2025-07-28 07:43:26 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 4 | remaining_running: 1
2025-07-28 07:43:26 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 运行中 -> 异常
2025-07-28 07:43:26 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 运行中 -> 异常
2025-07-28 07:43:26 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 运行中 | new_state: 异常
2025-07-28 07:43:26 - InstagramTaskManager - INFO - 清理模拟器4的运行中Instagram线程，原因: 模拟器状态变为异常
2025-07-28 07:43:26 - InstagramTaskManager - INFO - 模拟器4的Instagram线程已清理完成，当前并发: 2/2
2025-07-28 07:43:26 - MainWindowV2 - INFO - 模拟器4: 运行中 -> 异常
2025-07-28 07:43:26 - TaskActivityHeartbeatManager - INFO - 模拟器已标记为异常并清理状态 | emulator_id: 4 | removed_from_running: True | removed_from_active: False | total_running: 1 | total_active: 0
2025-07-28 07:43:26 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已从任务活动监控移除
2025-07-28 07:43:26 - TaskActivityHeartbeatManager - INFO - 没有排队等待接力的模拟器
2025-07-28 07:43:26 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 1 | max_concurrent: 2
2025-07-28 07:43:26 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/2)
2025-07-28 07:43:26 - UnifiedEmulatorManager - INFO - 模拟器已切换: 4 -> -1
2025-07-28 07:43:26 - MainWindowV2 - INFO - 模拟器自动切换: 4 -> -1
2025-07-28 07:43:26 - MainWindowV2 - INFO - 模拟器已自动切换: 4 -> -1
2025-07-28 07:43:26 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 15
2025-07-28 07:43:26 - MainWindowV2 - WARNING - 模拟器15心跳状态更新未产生变化
2025-07-28 07:43:26 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 07:43:27 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:28 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:29 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:31 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:32 - InstagramDMTask - ERROR - [模拟器15] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: context deadline exceeded
2025-07-28 07:43:32 - InstagramDMTask - INFO - [模拟器15] 点击失败状态重置UI
2025-07-28 07:43:32 - InstagramDMTask - INFO - [模拟器15] 已点击失败状态，等待UI重置
2025-07-28 07:43:33 - InstagramDMTask - INFO - [模拟器15] 继续等待测试结果 (1/3)
2025-07-28 07:43:34 - InstagramDMTask - INFO - [模拟器15] 测试状态监控 (2/30): 测试中…
2025-07-28 07:43:34 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:35 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:36 - MainWindowV2 - INFO - 手动触发窗口排列
2025-07-28 07:43:36 - FixedAsyncBridge - INFO - 执行异步操作: arrange_windows
2025-07-28 07:43:36 - WindowArrangementManager - INFO - 手动触发窗口排列
2025-07-28 07:43:36 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 07:43:36 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 07:43:36 - WindowArrangementManager - INFO - 窗口扫描完成，找到 3 个模拟器窗口
2025-07-28 07:43:36 - WindowArrangementManager - INFO - 找到 3 个所有模拟器窗口
2025-07-28 07:43:36 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 07:43:36 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 07:43:36 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-15' 已在网格位置 (1, 2)，保持不动
2025-07-28 07:43:36 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-2' 不在网格位置 (当前位置: -32000, -32000)，需要排列
2025-07-28 07:43:36 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-1' 不在网格位置 (当前位置: -32000, -32000)，需要排列
2025-07-28 07:43:36 - WindowArrangementManager - INFO - 需要排列 2 个窗口
2025-07-28 07:43:36 - WindowArrangementManager - INFO - 找到 2 个空闲网格位置
2025-07-28 07:43:36 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-2' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 160x28
2025-07-28 07:43:36 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-1' 已排列到网格位置 (0, 1): (302, 0) 保持原大小: 160x28
2025-07-28 07:43:36 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 2/3 个窗口
2025-07-28 07:43:36 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 2/3 个窗口 (成功: 2/3)
2025-07-28 07:43:36 - FixedAsyncBridge - INFO - 异步操作完成: arrange_windows
2025-07-28 07:43:37 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:37 - MainWindowV2 - INFO - 手动触发窗口排列
2025-07-28 07:43:37 - FixedAsyncBridge - INFO - 执行异步操作: arrange_windows
2025-07-28 07:43:37 - WindowArrangementManager - INFO - 手动触发窗口排列
2025-07-28 07:43:37 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 07:43:37 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 07:43:37 - WindowArrangementManager - INFO - 窗口扫描完成，找到 3 个模拟器窗口
2025-07-28 07:43:37 - WindowArrangementManager - INFO - 找到 3 个所有模拟器窗口
2025-07-28 07:43:37 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 07:43:37 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 07:43:37 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-15' 已在网格位置 (1, 2)，保持不动
2025-07-28 07:43:37 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-2' 不在网格位置 (当前位置: -32000, -32000)，需要排列
2025-07-28 07:43:37 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-1' 不在网格位置 (当前位置: -32000, -32000)，需要排列
2025-07-28 07:43:37 - WindowArrangementManager - INFO - 需要排列 2 个窗口
2025-07-28 07:43:37 - WindowArrangementManager - INFO - 找到 2 个空闲网格位置
2025-07-28 07:43:37 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-2' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 160x28
2025-07-28 07:43:37 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-1' 已排列到网格位置 (0, 1): (302, 0) 保持原大小: 160x28
2025-07-28 07:43:38 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 2/3 个窗口
2025-07-28 07:43:38 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 2/3 个窗口 (成功: 2/3)
2025-07-28 07:43:38 - FixedAsyncBridge - INFO - 异步操作完成: arrange_windows
2025-07-28 07:43:38 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:39 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:41 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:42 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:43 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:45 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 07:43:45 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 07:43:45 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 15
2025-07-28 07:43:45 - MainWindowV2 - WARNING - 模拟器15心跳状态更新未产生变化
2025-07-28 07:43:45 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 07:43:46 - InstagramDMTask - ERROR - [模拟器15] ❌ V2Ray节点延迟测试失败 (2/3): 失败：: context deadline exceeded
2025-07-28 07:43:46 - InstagramDMTask - INFO - [模拟器15] 点击失败状态重置UI
2025-07-28 07:43:46 - InstagramDMTask - INFO - [模拟器15] 已点击失败状态，等待UI重置
2025-07-28 07:43:46 - InstagramDMTask - INFO - [模拟器15] 继续等待测试结果 (2/3)
2025-07-28 07:43:47 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:48 - MainWindowV2 - INFO - 手动触发窗口排列
2025-07-28 07:43:48 - FixedAsyncBridge - INFO - 执行异步操作: arrange_windows
2025-07-28 07:43:48 - WindowArrangementManager - INFO - 手动触发窗口排列
2025-07-28 07:43:48 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 07:43:48 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 07:43:48 - WindowArrangementManager - INFO - 窗口扫描完成，找到 3 个模拟器窗口
2025-07-28 07:43:48 - WindowArrangementManager - INFO - 找到 3 个所有模拟器窗口
2025-07-28 07:43:48 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 07:43:48 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 07:43:48 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-15' 已在网格位置 (1, 2)，保持不动
2025-07-28 07:43:48 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-2' 不在网格位置 (当前位置: 958, 618)，需要排列
2025-07-28 07:43:48 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-1' 不在网格位置 (当前位置: 1360, 242)，需要排列
2025-07-28 07:43:48 - WindowArrangementManager - INFO - 需要排列 2 个窗口
2025-07-28 07:43:48 - WindowArrangementManager - INFO - 找到 2 个空闲网格位置
2025-07-28 07:43:48 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-2' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 07:43:48 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-1' 已排列到网格位置 (0, 1): (302, 0) 保持原大小: 302x435
2025-07-28 07:43:48 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 2/3 个窗口
2025-07-28 07:43:48 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 2/3 个窗口 (成功: 2/3)
2025-07-28 07:43:48 - FixedAsyncBridge - INFO - 异步操作完成: arrange_windows
2025-07-28 07:43:49 - InstagramDMTask - INFO - [模拟器15] 测试状态监控 (3/30): 测试中…
2025-07-28 07:43:49 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:49 - MainWindowV2 - INFO - 手动触发窗口排列
2025-07-28 07:43:49 - FixedAsyncBridge - INFO - 执行异步操作: arrange_windows
2025-07-28 07:43:49 - WindowArrangementManager - INFO - 手动触发窗口排列
2025-07-28 07:43:49 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 07:43:49 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 07:43:49 - WindowArrangementManager - INFO - 窗口扫描完成，找到 3 个模拟器窗口
2025-07-28 07:43:49 - WindowArrangementManager - INFO - 找到 3 个所有模拟器窗口
2025-07-28 07:43:49 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 07:43:49 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 07:43:49 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-15' 已在网格位置 (1, 2)，保持不动
2025-07-28 07:43:49 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-2' 已在网格位置 (0, 0)，保持不动
2025-07-28 07:43:49 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-1' 已在网格位置 (0, 1)，保持不动
2025-07-28 07:43:49 - WindowArrangementManager - INFO - 所有窗口都已在网格位置，无需排列
2025-07-28 07:43:49 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 3/3 个窗口
2025-07-28 07:43:49 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 3/3 个窗口 (成功: 3/3)
2025-07-28 07:43:49 - FixedAsyncBridge - INFO - 异步操作完成: arrange_windows
2025-07-28 07:43:50 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:51 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:53 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:54 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:55 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:57 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:43:59 - InstagramDMTask - ERROR - [模拟器15] ❌ V2Ray节点延迟测试失败 (3/3): 失败：: net/http: request canceled (Client.Timeout exceeded while awaiting headers)
2025-07-28 07:43:59 - InstagramDMTask - ERROR - [模拟器15] 达到最大失败次数 (3)，触发节点切换
2025-07-28 07:43:59 - InstagramDMTask - ERROR - [模拟器15] V2Ray节点延迟测试失败或超时
2025-07-28 07:43:59 - InstagramDMTask - ERROR - [模拟器15] V2Ray节点延迟测试失败，开始节点切换 (第1/8次)
2025-07-28 07:43:59 - InstagramDMTask - INFO - [模拟器15] 开始V2Ray节点切换流程
2025-07-28 07:43:59 - InstagramDMTask - INFO - [模拟器15] 开始智能滑动浏览节点列表
2025-07-28 07:44:00 - InstagramDMTask - INFO - [模拟器15] 计划执行 1 次随机滑动
2025-07-28 07:44:00 - InstagramDMTask - INFO - [模拟器15] 第 1/1 次滑动: up, 距离比例: 0.57
2025-07-28 07:44:01 - InstagramDMTask - INFO - [模拟器15] 第 1 次滑动失败
2025-07-28 07:44:01 - InstagramDMTask - INFO - [模拟器15] 滑动后等待 0.2 秒
2025-07-28 07:44:01 - InstagramDMTask - INFO - [模拟器15] ✅ 智能滑动完成，共执行 1 次
2025-07-28 07:44:01 - InstagramDMTask - INFO - [模拟器15] 开始随机选择V2Ray节点
2025-07-28 07:44:02 - InstagramDMTask - INFO - [模拟器15] 找到 7 个可用节点
2025-07-28 07:44:02 - InstagramDMTask - INFO - [模拟器15] 随机选择节点: V1-106|台湾|x1.0
2025-07-28 07:44:02 - InstagramDMTask - INFO - [模拟器15] 节点位置: [11,107][88,121]
2025-07-28 07:44:02 - InstagramDMTask - INFO - [模拟器15] ✅ 成功点击节点: V1-106|台湾|x1.0
2025-07-28 07:44:02 - InstagramDMTask - INFO - [模拟器15] 等待1秒让节点切换完成
2025-07-28 07:44:03 - InstagramDMTask - INFO - [模拟器15] 开始连接V2Ray节点
2025-07-28 07:44:05 - InstagramDMTask - INFO - [模拟器15] 当前连接状态: 已连接，点击测试连接
2025-07-28 07:44:05 - InstagramDMTask - INFO - [模拟器15] V2Ray节点已连接，无需重复连接
2025-07-28 07:44:05 - InstagramDMTask - INFO - [模拟器15] ✅ V2Ray节点切换成功
2025-07-28 07:44:05 - InstagramDMTask - INFO - [模拟器15] 节点切换成功，重新测试延迟
2025-07-28 07:44:05 - InstagramDMTask - INFO - [模拟器15] 开始测试V2Ray节点延迟
2025-07-28 07:44:05 - InstagramDMTask - INFO - [模拟器15] 开始V2Ray节点延迟测试
2025-07-28 07:44:05 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 07:44:05 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 07:44:05 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 15
2025-07-28 07:44:05 - MainWindowV2 - WARNING - 模拟器15心跳状态更新未产生变化
2025-07-28 07:44:05 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 07:44:06 - InstagramDMTask - INFO - [模拟器15] 当前测试状态: 已连接，点击测试连接
2025-07-28 07:44:06 - InstagramDMTask - INFO - [模拟器15] 点击开始延迟测试
2025-07-28 07:44:06 - InstagramDMTask - INFO - [模拟器15] 已点击测试按钮，等待测试结果
2025-07-28 07:44:07 - InstagramDMTask - INFO - [模拟器15] 测试状态监控 (1/30): 测试中…
2025-07-28 07:44:07 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:44:09 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:44:10 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:44:11 - InstagramDMTask - INFO - [模拟器15] 🔄 延迟测试进行中，继续等待...
2025-07-28 07:44:25 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 07:44:25 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 07:44:25 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 15
2025-07-28 07:44:25 - MainWindowV2 - WARNING - 模拟器15心跳状态更新未产生变化
2025-07-28 07:44:25 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 07:44:45 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 07:44:45 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 07:44:45 - TaskActivityHeartbeatManager - INFO - 模拟器 15 疑似心跳异常，进入观察期 | 无活动时间: 34.4秒
2025-07-28 07:44:45 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 07:45:05 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 07:45:05 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 07:45:05 - TaskActivityHeartbeatManager - ERROR - 模拟器 15 确认心跳异常 | 总无活动时间: 54.4秒
2025-07-28 07:45:05 - TaskActivityHeartbeatManager - INFO - 心跳异常处理开始，失败次数: 1/1 | emulator_id: 15
2025-07-28 07:45:05 - NativeScreenshotEngine - INFO - 开始截取模拟器 15 的截图
2025-07-28 07:45:05 - UnifiedEmulatorManager - INFO - 心跳检测失败: 心跳异常, 失败次数: 1 | emulator_id: 15
2025-07-28 07:45:05 - MainWindowV2 - INFO - 模拟器15心跳异常: 心跳异常 (失败次数: 1)
2025-07-28 07:45:05 - MainWindowV2 - WARNING - 模拟器15心跳检测失败: 心跳异常, 失败次数: 1
2025-07-28 07:45:05 - MainWindowV2 - WARNING - 模拟器15心跳状态更新未产生变化
2025-07-28 07:45:06 - NativeScreenshotEngine - ERROR - 模拟器 15 绑定句柄无效: 0
2025-07-28 07:45:06 - ScreenshotManager - ERROR - 原生截图引擎截图失败
2025-07-28 07:45:06 - TaskActivityHeartbeatManager - INFO - 开始熔断处理 | emulator_id: 15
2025-07-28 07:45:06 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 15 | remaining_running: 0
2025-07-28 07:45:06 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器15状态变化: 运行中 -> 异常
2025-07-28 07:45:06 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器15状态变化: 运行中 -> 异常
2025-07-28 07:45:06 - Emulator - INFO - 模拟器状态变化 | emulator_id: 15 | old_state: 运行中 | new_state: 异常
2025-07-28 07:45:06 - InstagramTaskManager - INFO - 清理模拟器15的运行中Instagram线程，原因: 模拟器状态变为异常
2025-07-28 07:45:06 - InstagramTaskManager - INFO - 模拟器15的Instagram线程已清理完成，当前并发: 1/2
2025-07-28 07:45:06 - MainWindowV2 - INFO - 模拟器15: 运行中 -> 异常
2025-07-28 07:45:06 - TaskActivityHeartbeatManager - INFO - 模拟器已标记为异常并清理状态 | emulator_id: 15 | removed_from_running: True | removed_from_active: False | total_running: 0 | total_active: 0
2025-07-28 07:45:06 - TaskActivityHeartbeatManager - INFO - 模拟器 15 已从任务活动监控移除
2025-07-28 07:45:06 - TaskActivityHeartbeatManager - INFO - 没有排队等待接力的模拟器
2025-07-28 07:45:06 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 2
2025-07-28 07:45:06 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/2)
2025-07-28 07:45:06 - UnifiedEmulatorManager - INFO - 模拟器已切换: 15 -> -1
2025-07-28 07:45:06 - MainWindowV2 - INFO - 模拟器自动切换: 15 -> -1
2025-07-28 07:45:06 - MainWindowV2 - INFO - 模拟器已自动切换: 15 -> -1
2025-07-28 07:45:06 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
