2025-07-28 09:55:53 - root - INFO - 简化日志系统初始化完成
2025-07-28 09:55:53 - App - INFO - 开始Instagram直接关注真实流程测试
2025-07-28 09:55:53 - App - INFO - [直接关注测试器] ⚡ 开始Instagram直接关注快速测试
2025-07-28 09:55:53 - App - INFO - [直接关注测试器] 测试模拟器: 2
2025-07-28 09:55:53 - App - INFO - [直接关注测试器] 开始时间: 2025-07-28 09:55:53
2025-07-28 09:55:53 - App - INFO - [直接关注测试器] ⚡ 快速模式：跳过阶段一、二、三，直接测试阶段四
2025-07-28 09:55:53 - App - INFO - [直接关注测试器] 开始设置真实测试环境
2025-07-28 09:55:53 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-28 09:55:53 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-28 09:55:53 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-28 09:55:53 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-28 09:55:53 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-28 09:55:53 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-28 09:55:53 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-28 09:55:53 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-28 09:55:53 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-28 09:55:53 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-28 09:55:53 - InstagramDMTask - INFO - [模拟器2] 雷电模拟器API初始化成功
2025-07-28 09:55:53 - InstagramDMTask - INFO - [模拟器2] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 09:55:53 - InstagramDMTask - INFO - [模拟器2] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 09:55:53 - InstagramDMTask - INFO - [模拟器2] 已设置ld.emulator_id = 2
2025-07-28 09:55:53 - InstagramDMTask - INFO - [模拟器2] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 09:55:53 - InstagramDMTask - INFO - [模拟器2] Instagram任务配置热加载观察者已注册
2025-07-28 09:55:53 - InstagramDMTask - INFO - [模拟器2] Instagram私信任务执行器初始化完成
2025-07-28 09:55:53 - InstagramFollowTask - INFO - [模拟器2] Instagram关注任务配置加载完成
2025-07-28 09:55:53 - InstagramFollowTask - INFO - [模拟器2] Instagram关注任务执行器初始化完成
2025-07-28 09:55:53 - App - INFO - [直接关注测试器] 验证模拟器2就绪状态
2025-07-28 09:55:53 - App - INFO - [直接关注测试器] ✅ 模拟器2就绪
2025-07-28 09:55:53 - App - INFO - [直接关注测试器] ✅ 真实测试环境设置完成
2025-07-28 09:55:53 - App - INFO - [直接关注测试器] 开始测试阶段四: 直接关注业务流程
2025-07-28 09:55:53 - App - INFO - [直接关注测试器] 加载app_config.json真实配置
2025-07-28 09:55:53 - App - INFO - [直接关注测试器] 真实配置加载完成:
2025-07-28 09:55:53 - App - INFO - [直接关注测试器]   直接关注数量: 5
2025-07-28 09:55:53 - App - INFO - [直接关注测试器]   切换延迟: 2-22ms
2025-07-28 09:55:53 - App - INFO - [直接关注测试器]   关注延迟: 500-1000ms
2025-07-28 09:55:53 - App - INFO - [直接关注测试器]   地区筛选: 所有地区=False, 日本=True, 韩国=False, 泰国=False
2025-07-28 09:55:53 - App - INFO - [直接关注测试器] 🎯 开始执行模式一：直接关注模式（专用批量检测优化）
2025-07-28 09:55:53 - InstagramFollowTask - INFO - [模拟器2] 开始【模式一：直接关注循环】，目标关注数: 5
2025-07-28 09:55:53 - InstagramFollowTask - INFO - [模拟器2] 开始处理目标用户: m__yu11 (已关注: 0/5)
2025-07-28 09:55:53 - InstagramFollowTask - INFO - [模拟器2] 开始处理用户：m__yu11
2025-07-28 09:55:54 - InstagramFollowTask - INFO - [模拟器2] 跳转到用户主页：m__yu11
2025-07-28 09:55:54 - InstagramFollowTask - INFO - [模拟器2] 开始检测用户信息
2025-07-28 09:56:00 - InstagramFollowTask - INFO - [模拟器2] 🔍 关注状态检测: 关注
2025-07-28 09:56:00 - InstagramFollowTask - INFO - [模拟器2] ✅ 标题栏验证成功，当前在用户 m__yu11 的资料页
2025-07-28 09:56:00 - InstagramFollowTask - INFO - [模拟器2] 用户信息检测完成，耗时6.05秒
2025-07-28 09:56:00 - InstagramFollowTask - INFO - [模拟器2] 该用户未关注,开始关注
2025-07-28 09:56:01 - InstagramFollowTask - INFO - [模拟器2] 关注完成,已关注 1 / 5
2025-07-28 09:56:01 - InstagramFollowTask - INFO - [模拟器2] 直接关注任务状态更新信号已发送: 关注中 (1/5)
2025-07-28 09:56:01 - InstagramFollowTask - INFO - [模拟器2] ⏱️ 切换用户延迟: 22毫秒
2025-07-28 09:56:01 - InstagramFollowTask - INFO - [模拟器2] 开始处理目标用户: angelkibun (已关注: 1/5)
2025-07-28 09:56:01 - InstagramFollowTask - INFO - [模拟器2] 开始处理用户：angelkibun
2025-07-28 09:56:01 - InstagramFollowTask - INFO - [模拟器2] 跳转到用户主页：angelkibun
2025-07-28 09:56:01 - InstagramFollowTask - INFO - [模拟器2] 开始检测用户信息
2025-07-28 09:56:05 - InstagramFollowTask - INFO - [模拟器2] 🔍 关注状态检测: 关注
2025-07-28 09:56:05 - InstagramFollowTask - INFO - [模拟器2] ✅ 标题栏验证成功，当前在用户 angelkibun 的资料页
2025-07-28 09:56:05 - InstagramFollowTask - INFO - [模拟器2] 用户信息检测完成，耗时3.59秒
2025-07-28 09:56:05 - InstagramFollowTask - INFO - [模拟器2] 该用户未关注,开始关注
2025-07-28 09:56:07 - InstagramFollowTask - INFO - [模拟器2] 关注完成,已关注 2 / 5
2025-07-28 09:56:07 - InstagramFollowTask - INFO - [模拟器2] 直接关注任务状态更新信号已发送: 关注中 (2/5)
2025-07-28 09:56:07 - InstagramFollowTask - INFO - [模拟器2] ⏱️ 切换用户延迟: 8毫秒
2025-07-28 09:56:07 - InstagramFollowTask - INFO - [模拟器2] 开始处理目标用户: asami0523 (已关注: 2/5)
2025-07-28 09:56:07 - InstagramFollowTask - INFO - [模拟器2] 开始处理用户：asami0523
2025-07-28 09:56:07 - InstagramFollowTask - INFO - [模拟器2] 跳转到用户主页：asami0523
2025-07-28 09:56:07 - InstagramFollowTask - INFO - [模拟器2] 开始检测用户信息
2025-07-28 09:56:11 - InstagramFollowTask - INFO - [模拟器2] 🔍 关注状态检测: 关注
2025-07-28 09:56:11 - InstagramFollowTask - INFO - [模拟器2] ✅ 标题栏验证成功，当前在用户 asami0523 的资料页
2025-07-28 09:56:11 - InstagramFollowTask - INFO - [模拟器2] 用户信息检测完成，耗时4.19秒
2025-07-28 09:56:11 - InstagramFollowTask - INFO - [模拟器2] 该用户未关注,开始关注
2025-07-28 09:56:13 - InstagramFollowTask - INFO - [模拟器2] 关注完成,已关注 3 / 5
2025-07-28 09:56:13 - InstagramFollowTask - INFO - [模拟器2] 直接关注任务状态更新信号已发送: 关注中 (3/5)
2025-07-28 09:56:13 - InstagramFollowTask - INFO - [模拟器2] ⏱️ 切换用户延迟: 11毫秒
2025-07-28 09:56:13 - InstagramFollowTask - INFO - [模拟器2] 开始处理目标用户: lemon_home7 (已关注: 3/5)
2025-07-28 09:56:13 - InstagramFollowTask - INFO - [模拟器2] 开始处理用户：lemon_home7
2025-07-28 09:56:13 - InstagramFollowTask - INFO - [模拟器2] 跳转到用户主页：lemon_home7
2025-07-28 09:56:13 - InstagramFollowTask - INFO - [模拟器2] 开始检测用户信息
2025-07-28 09:56:18 - InstagramFollowTask - WARNING - [模拟器2] 页面解析失败，重新检测
2025-07-28 09:56:19 - InstagramFollowTask - INFO - [模拟器2] 🔍 关注状态检测: 关注
2025-07-28 09:56:19 - InstagramFollowTask - INFO - [模拟器2] ✅ 标题栏验证成功，当前在用户 lemon_home7 的资料页
2025-07-28 09:56:19 - InstagramFollowTask - INFO - [模拟器2] 用户信息检测完成，耗时6.88秒
2025-07-28 09:56:19 - InstagramFollowTask - INFO - [模拟器2] 该用户未关注,开始关注
2025-07-28 09:56:23 - InstagramFollowTask - INFO - [模拟器2] 关注完成,已关注 4 / 5
2025-07-28 09:56:23 - InstagramFollowTask - INFO - [模拟器2] 直接关注任务状态更新信号已发送: 关注中 (4/5)
2025-07-28 09:56:23 - InstagramFollowTask - INFO - [模拟器2] ⏱️ 切换用户延迟: 4毫秒
2025-07-28 09:56:23 - InstagramFollowTask - INFO - [模拟器2] 开始处理目标用户: peeps.mag (已关注: 4/5)
2025-07-28 09:56:23 - InstagramFollowTask - INFO - [模拟器2] 开始处理用户：peeps.mag
2025-07-28 09:56:23 - InstagramFollowTask - INFO - [模拟器2] 跳转到用户主页：peeps.mag
2025-07-28 09:56:23 - InstagramFollowTask - INFO - [模拟器2] 开始检测用户信息
2025-07-28 09:56:29 - InstagramFollowTask - INFO - [模拟器2] 🔍 关注状态检测: 关注
2025-07-28 09:56:29 - InstagramFollowTask - INFO - [模拟器2] ✅ 标题栏验证成功，当前在用户 peeps.mag 的资料页
2025-07-28 09:56:29 - InstagramFollowTask - INFO - [模拟器2] 用户信息检测完成，耗时5.68秒
2025-07-28 09:56:29 - InstagramFollowTask - INFO - [模拟器2] 该用户未关注,开始关注
2025-07-28 09:56:33 - InstagramFollowTask - INFO - [模拟器2] 关注完成,已关注 5 / 5
2025-07-28 09:56:33 - InstagramFollowTask - INFO - [模拟器2] 直接关注任务状态更新信号已发送: 已完成 (5/5)
2025-07-28 09:56:33 - InstagramFollowTask - INFO - [模拟器2] 任务完成,退出循环.--任务进度 :5 / 5 耗时: 39.44秒
2025-07-28 09:56:33 - InstagramFollowTask - INFO - [模拟器2] 【模式一：直接关注循环】完成，成功关注: 5, 跳过蓝V: 0, 跳过私密: 0
2025-07-28 09:56:33 - App - INFO - [直接关注测试器] ✅ 阶段四测试通过
2025-07-28 09:56:33 - App - INFO - [直接关注测试器] 📊 执行统计:
2025-07-28 09:56:33 - App - INFO - [直接关注测试器]   成功关注: 5
2025-07-28 09:56:33 - App - INFO - [直接关注测试器]   跳过私密: 0
2025-07-28 09:56:33 - App - INFO - [直接关注测试器]   跳过蓝V: 0
2025-07-28 09:56:33 - App - INFO - [直接关注测试器] ================================================================================
2025-07-28 09:56:33 - App - INFO - [直接关注测试器] 📋 Instagram直接关注测试报告
2025-07-28 09:56:33 - App - INFO - [直接关注测试器] ================================================================================
2025-07-28 09:56:33 - App - INFO - [直接关注测试器] 测试模式: ⚡ 快速模式
2025-07-28 09:56:33 - App - INFO - [直接关注测试器] 测试模拟器: 2
2025-07-28 09:56:33 - App - INFO - [直接关注测试器] 开始时间: 2025-07-28 09:55:53
2025-07-28 09:56:33 - App - INFO - [直接关注测试器] 结束时间: 2025-07-28 09:56:33
2025-07-28 09:56:33 - App - INFO - [直接关注测试器] 总耗时: 0:00:39.684520
2025-07-28 09:56:33 - App - INFO - [直接关注测试器] 总体结果: ✅ 通过
2025-07-28 09:56:33 - App - INFO - [直接关注测试器] --------------------------------------------------------------------------------
2025-07-28 09:56:33 - App - INFO - [直接关注测试器] 直接关注业务流程: ✅ 通过 (09:56:33)
2025-07-28 09:56:33 - App - INFO - [直接关注测试器]   📊 执行统计:
2025-07-28 09:56:33 - App - INFO - [直接关注测试器]     total_user_followed: 5
2025-07-28 09:56:33 - App - INFO - [直接关注测试器]     skipped_private: 0
2025-07-28 09:56:33 - App - INFO - [直接关注测试器]     skipped_verified: 0
2025-07-28 09:56:33 - App - INFO - [直接关注测试器] ================================================================================
2025-07-28 09:56:33 - App - INFO - [直接关注测试器] 📄 测试报告已保存: test_report_direct_follow_2_20250728_095553.json
2025-07-28 09:56:33 - App - INFO - 🎉 Instagram直接关注测试完成 - 结果: ✅ 成功
