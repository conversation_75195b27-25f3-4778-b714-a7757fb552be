2025-07-28 09:25:47 - root - INFO - 简化日志系统初始化完成
2025-07-28 09:25:47 - main - INFO - 应用程序启动
2025-07-28 09:25:47 - __main__ - INFO - Qt应用程序已创建
2025-07-28 09:25:47 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 09:25:47 - __main__ - INFO - 统一配置管理器已创建
2025-07-28 09:25:47 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-28 09:25:47 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-28 09:25:47 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-28 09:25:47 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-28 09:25:47 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-28 09:25:47 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-28 09:25:47 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-28 09:25:47 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-28 09:25:47 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-28 09:25:47 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-28 09:25:47 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-28 09:25:47 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 09:25:47 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-28 09:25:47 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-28 09:25:47 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-28 09:25:47 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-28 09:25:47 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-28 09:25:47 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-28 09:25:47 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-28 09:25:47 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-28 09:25:47 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-28 09:25:47 - __main__ - INFO - UI主窗口已创建
2025-07-28 09:25:47 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-28 09:25:48 - __main__ - INFO - 主窗口已显示
2025-07-28 09:25:48 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-28 09:25:48 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-28 09:25:48 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-28 09:25:48 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-28 09:25:48 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 09:25:48 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-28 09:25:48 - __main__ - INFO - UI层和业务层已连接
2025-07-28 09:25:48 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 09:25:48 - __main__ - INFO - 启动Qt事件循环
2025-07-28 09:25:48 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 09:25:48 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 09:25:48 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:25:48 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-28 09:25:48 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 09:25:48 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 09:25:48 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 09:25:48 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 09:25:48 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:25:48 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-28 09:25:48 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 09:25:48 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 09:25:48 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 09:25:48 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 09:25:48 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.26s | count: 1229
2025-07-28 09:25:48 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 09:25:48 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 09:25:48 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 09:25:48 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 09:25:48 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-28 09:25:48 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 09:25:48 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 09:25:48 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 09:25:48 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 09:25:48 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.27s | count: 1229
2025-07-28 09:25:48 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 09:25:48 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 09:25:48 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 09:25:48 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 09:25:49 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-28 09:25:49 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-28 09:25:49 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-28 09:25:49 - __main__ - INFO - 后台服务已启动
2025-07-28 09:25:49 - __main__ - INFO - 延迟启动服务完成
2025-07-28 09:25:50 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_direct_task
2025-07-28 09:25:50 - MainWindowV2 - INFO - Instagram直接关注任务已启动，涉及3个模拟器
2025-07-28 09:25:50 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram直接关注任务请求
2025-07-28 09:25:50 - MainWindowV2 - INFO - 用户启动Instagram直接关注任务，模拟器数量: 3
2025-07-28 09:25:50 - FixedAsyncBridge - INFO - 开始处理Instagram直接关注任务（线程池模式），模拟器数量: 3
2025-07-28 09:25:50 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [3, 4, 5]
2025-07-28 09:25:50 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 3
2025-07-28 09:25:50 - StartupManager - INFO - 批量启动请求 | count: 3
2025-07-28 09:25:50 - StartupManager - INFO - 启动调度器已启动
2025-07-28 09:25:50 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-28 09:25:50 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-28 09:25:50 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-28 09:25:50 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 3
2025-07-28 09:25:50 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: direct
2025-07-28 09:25:50 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 3
2025-07-28 09:25:50 - InstagramFollowTaskThread - INFO - [模拟器3] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 09:25:50 - InstagramFollowTaskManager - INFO - 为模拟器 3 创建Instagram关注任务线程，模式: direct
2025-07-28 09:25:50 - InstagramFollowTaskThread - INFO - [模拟器4] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 09:25:50 - InstagramFollowTaskManager - INFO - 为模拟器 4 创建Instagram关注任务线程，模式: direct
2025-07-28 09:25:50 - InstagramFollowTaskThread - INFO - [模拟器5] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 09:25:50 - InstagramFollowTaskManager - INFO - 为模拟器 5 创建Instagram关注任务线程，模式: direct
2025-07-28 09:25:50 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建3个Instagram线程
2025-07-28 09:25:50 - FixedAsyncBridge - INFO - 异步桥接器: Instagram直接关注任务请求已处理，状态: started
2025-07-28 09:25:50 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-28 09:25:50 - MainWindowV2 - INFO - 3个模拟器: 未知 -> 排队中
2025-07-28 09:25:50 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-28 09:25:50 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 3 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 3
2025-07-28 09:25:50 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 3
2025-07-28 09:25:50 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/3)
2025-07-28 09:25:50 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-28 09:25:50 - MainWindowV2 - INFO - 批量启动完成: 0/3 成功
2025-07-28 09:25:50 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 3
2025-07-28 09:25:50 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_direct_task
2025-07-28 09:25:50 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 排队中 | new_state: 启动中
2025-07-28 09:25:50 - MainWindowV2 - INFO - 模拟器3: 排队中 -> 启动中
2025-07-28 09:25:50 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 2 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 3
2025-07-28 09:25:50 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/3)
2025-07-28 09:25:51 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 3
2025-07-28 09:25:56 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-28 09:25:56 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 排队中 | new_state: 启动中
2025-07-28 09:25:56 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-28 09:25:56 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 4
2025-07-28 09:25:56 - MainWindowV2 - INFO - 模拟器4: 排队中 -> 启动中
2025-07-28 09:25:56 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 2 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 2 | max_concurrent: 3
2025-07-28 09:25:56 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中2个, 运行0个, 失败0个 (并发槽位:2/3)
2025-07-28 09:25:56 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 4
2025-07-28 09:26:01 - Emulator - INFO - Android系统启动完成 | emulator_id: 3 | elapsed_time: 10.0秒
2025-07-28 09:26:01 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 启动中 -> 运行中
2025-07-28 09:26:01 - InstagramFollowTaskManager - INFO - 启动模拟器3的Instagram关注任务线程 - 当前并发: 1/3
2025-07-28 09:26:01 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 启动中 | new_state: 运行中
2025-07-28 09:26:01 - Emulator - INFO - 模拟器启动成功 | emulator_id: 3 | running_count: 1
2025-07-28 09:26:01 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已添加到任务活动监控，失败计数: 0
2025-07-28 09:26:01 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务start
2025-07-28 09:26:01 - InstagramTaskThread - INFO - [模拟器3] 开始等待启动完成
2025-07-28 09:26:01 - MainWindowV2 - INFO - 模拟器3启动成功
2025-07-28 09:26:01 - InstagramTaskThread - INFO - [模拟器3] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 09:26:01 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 09:26:01 - InstagramTaskThread - INFO - [模拟器3] 开始窗口排列
2025-07-28 09:26:01 - WindowArrangementManager - INFO - 模拟器3启动完成，立即触发窗口排列
2025-07-28 09:26:01 - MainWindowV2 - WARNING - 未找到模拟器3，无法更新状态
2025-07-28 09:26:01 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-28 09:26:01 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 2 | max_concurrent: 3
2025-07-28 09:26:01 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/3)
2025-07-28 09:26:01 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中, PID: 22920
2025-07-28 09:26:01 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-28 09:26:01 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 09:26:01 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 排队中 | new_state: 启动中
2025-07-28 09:26:01 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 09:26:01 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 5
2025-07-28 09:26:01 - MainWindowV2 - INFO - 模拟器5: 排队中 -> 启动中
2025-07-28 09:26:01 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 2 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-28 09:26:01 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中2个, 运行1个, 失败0个 (并发槽位:3/3)
2025-07-28 09:26:02 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 5
2025-07-28 09:26:03 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 09:26:03 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 09:26:03 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 09:26:03 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 09:26:03 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 09:26:03 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 09:26:03 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 09:26:03 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 09:26:03 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 09:26:03 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 09:26:03 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 09:26:03 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 09:26:06 - Emulator - INFO - Android系统启动完成 | emulator_id: 4 | elapsed_time: 10.2秒
2025-07-28 09:26:06 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 启动中 -> 运行中
2025-07-28 09:26:06 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 启动中 | new_state: 运行中
2025-07-28 09:26:06 - InstagramFollowTaskManager - INFO - 启动模拟器4的Instagram关注任务线程 - 当前并发: 2/3
2025-07-28 09:26:06 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已添加到任务活动监控，失败计数: 0
2025-07-28 09:26:06 - Emulator - INFO - 模拟器启动成功 | emulator_id: 4 | running_count: 2
2025-07-28 09:26:06 - InstagramTaskThread - INFO - [模拟器4] 开始等待启动完成
2025-07-28 09:26:06 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务start
2025-07-28 09:26:06 - InstagramTaskThread - INFO - [模拟器4] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 09:26:06 - MainWindowV2 - INFO - 模拟器4启动成功
2025-07-28 09:26:06 - InstagramTaskThread - INFO - [模拟器4] 开始窗口排列
2025-07-28 09:26:06 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 09:26:06 - WindowArrangementManager - INFO - 模拟器4启动完成，立即触发窗口排列
2025-07-28 09:26:06 - MainWindowV2 - WARNING - 未找到模拟器4，无法更新状态
2025-07-28 09:26:06 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-28 09:26:06 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 1 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 66.66666666666666 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-28 09:26:06 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行2个, 失败0个 (并发槽位:3/3)
2025-07-28 09:26:07 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中, PID: 3648
2025-07-28 09:26:07 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-28 09:26:07 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 09:26:07 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 09:26:07 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 09:26:07 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 09:26:07 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 09:26:07 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 09:26:07 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 09:26:08 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 09:26:08 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 09:26:08 - WindowArrangementManager - INFO - 窗口扫描完成，找到 2 个运行中的模拟器窗口
2025-07-28 09:26:08 - WindowArrangementManager - INFO - 找到 2 个正在运行的模拟器窗口
2025-07-28 09:26:08 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 09:26:08 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 09:26:08 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 09:26:08 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已在网格位置 (0, 0)，保持不动
2025-07-28 09:26:08 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 09:26:08 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 09:26:08 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已排列到网格位置 (0, 1): (302, 0) 保持原大小: 302x435
2025-07-28 09:26:08 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/2 个窗口
2025-07-28 09:26:08 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/2 个窗口 (成功: 1/2)
2025-07-28 09:26:09 - InstagramTaskThread - INFO - [模拟器3] 窗口排列完成
2025-07-28 09:26:09 - InstagramFollowTaskThread - INFO - [模拟器3] 开始执行Instagram直接关注任务
2025-07-28 09:26:09 - InstagramDMTask - INFO - [模拟器3] 雷电模拟器API初始化成功
2025-07-28 09:26:09 - InstagramDMTask - INFO - [模拟器3] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 09:26:09 - InstagramDMTask - INFO - [模拟器3] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 09:26:09 - InstagramDMTask - INFO - [模拟器3] 已设置ld.emulator_id = 3
2025-07-28 09:26:09 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 09:26:09 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置热加载观察者已注册
2025-07-28 09:26:09 - InstagramDMTask - INFO - [模拟器3] Instagram私信任务执行器初始化完成
2025-07-28 09:26:09 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务配置加载完成
2025-07-28 09:26:09 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务执行器初始化完成
2025-07-28 09:26:09 - InstagramFollowTask - INFO - [模拟器3] 关注模式已设置为: direct
2025-07-28 09:26:09 - InstagramFollowTask - INFO - [模拟器3] 开始执行Instagram关注任务
2025-07-28 09:26:09 - InstagramFollowTask - WARNING - [模拟器3] 任务开始时间未由线程传递，在此设置
2025-07-28 09:26:09 - InstagramFollowTask - INFO - [模拟器3] 任务超时设置: 12秒，已运行: 0.00秒
2025-07-28 09:26:09 - InstagramDMTask - INFO - [模拟器3] 模拟器Android系统运行正常，桌面稳定
2025-07-28 09:26:09 - InstagramDMTask - INFO - [模拟器3] 开始检查应用安装状态
2025-07-28 09:26:09 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 09:26:09 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 09:26:09 - InstagramDMTask - INFO - [模拟器3] 📊 应用安装状态检测结果:
2025-07-28 09:26:09 - InstagramDMTask - INFO - [模拟器3] ✅ 所有必要应用已安装
2025-07-28 09:26:09 - InstagramDMTask - INFO - [模拟器3] 开始启动V2Ray应用
2025-07-28 09:26:10 - InstagramDMTask - INFO - [模拟器3] V2Ray启动命令执行成功，等待应用加载
2025-07-28 09:26:10 - InstagramDMTask - INFO - [模拟器3] V2Ray应用启动结果: 成功
2025-07-28 09:26:12 - Emulator - INFO - Android系统启动完成 | emulator_id: 5 | elapsed_time: 10.2秒
2025-07-28 09:26:12 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-28 09:26:12 - InstagramFollowTaskManager - INFO - 启动模拟器5的Instagram关注任务线程 - 当前并发: 3/3
2025-07-28 09:26:12 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 启动中 | new_state: 运行中
2025-07-28 09:26:12 - Emulator - INFO - 模拟器启动成功 | emulator_id: 5 | running_count: 3
2025-07-28 09:26:12 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已添加到任务活动监控，失败计数: 0
2025-07-28 09:26:12 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务start
2025-07-28 09:26:12 - InstagramTaskThread - INFO - [模拟器5] 开始等待启动完成
2025-07-28 09:26:12 - MainWindowV2 - INFO - 模拟器5启动成功
2025-07-28 09:26:12 - InstagramTaskThread - INFO - [模拟器5] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 09:26:12 - InstagramTaskThread - INFO - [模拟器5] 开始窗口排列
2025-07-28 09:26:12 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 09:26:12 - WindowArrangementManager - INFO - 模拟器5启动完成，立即触发窗口排列
2025-07-28 09:26:12 - MainWindowV2 - WARNING - 未找到模拟器5，无法更新状态
2025-07-28 09:26:12 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 09:26:12 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 0 | running: 3 | failed: 0 | cancelled: 0 | completed: 3 | percentage: 100.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-28 09:26:12 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行3个, 失败0个 (并发槽位:3/3)
2025-07-28 09:26:12 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中, PID: 23932
2025-07-28 09:26:12 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 09:26:12 - StartupManager - INFO - 调度器已停止
2025-07-28 09:26:13 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray应用启动成功
2025-07-28 09:26:13 - InstagramDMTask - INFO - [模拟器3] 开始检查V2Ray节点列表状态
2025-07-28 09:26:14 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 09:26:14 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 09:26:14 - WindowArrangementManager - INFO - 窗口扫描完成，找到 3 个运行中的模拟器窗口
2025-07-28 09:26:14 - WindowArrangementManager - INFO - 找到 3 个正在运行的模拟器窗口
2025-07-28 09:26:14 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 09:26:14 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 09:26:14 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 09:26:14 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已在网格位置 (0, 1)，保持不动
2025-07-28 09:26:14 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已在网格位置 (0, 0)，保持不动
2025-07-28 09:26:14 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 09:26:14 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 09:26:14 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 已排列到网格位置 (0, 2): (604, 0) 保持原大小: 302x435
2025-07-28 09:26:14 - InstagramDMTask - INFO - [模拟器3] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 09:26:14 - InstagramDMTask - INFO - [模拟器3] 开始连接V2Ray节点
2025-07-28 09:26:14 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/3 个窗口
2025-07-28 09:26:14 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/3 个窗口 (成功: 1/3)
2025-07-28 09:26:14 - InstagramTaskThread - INFO - [模拟器4] 窗口排列完成
2025-07-28 09:26:14 - InstagramFollowTaskThread - INFO - [模拟器4] 开始执行Instagram直接关注任务
2025-07-28 09:26:14 - InstagramDMTask - INFO - [模拟器4] 雷电模拟器API初始化成功
2025-07-28 09:26:14 - InstagramDMTask - INFO - [模拟器4] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 09:26:14 - InstagramDMTask - INFO - [模拟器4] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 09:26:14 - InstagramDMTask - INFO - [模拟器4] 已设置ld.emulator_id = 4
2025-07-28 09:26:14 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 09:26:14 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置热加载观察者已注册
2025-07-28 09:26:14 - InstagramDMTask - INFO - [模拟器4] Instagram私信任务执行器初始化完成
2025-07-28 09:26:14 - InstagramFollowTask - INFO - [模拟器4] Instagram关注任务配置加载完成
2025-07-28 09:26:14 - InstagramFollowTask - INFO - [模拟器4] Instagram关注任务执行器初始化完成
2025-07-28 09:26:14 - InstagramFollowTask - INFO - [模拟器4] 关注模式已设置为: direct
2025-07-28 09:26:14 - InstagramFollowTask - INFO - [模拟器4] 开始执行Instagram关注任务
2025-07-28 09:26:14 - InstagramFollowTask - WARNING - [模拟器4] 任务开始时间未由线程传递，在此设置
2025-07-28 09:26:14 - InstagramFollowTask - INFO - [模拟器4] 任务超时设置: 12秒，已运行: 0.00秒
2025-07-28 09:26:15 - InstagramDMTask - INFO - [模拟器4] 模拟器Android系统运行正常，桌面稳定
2025-07-28 09:26:15 - InstagramDMTask - INFO - [模拟器4] 开始检查应用安装状态
2025-07-28 09:26:15 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 09:26:15 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-28 09:26:15 - InstagramDMTask - INFO - [模拟器4] 📊 应用安装状态检测结果:
2025-07-28 09:26:15 - InstagramDMTask - INFO - [模拟器4] ✅ 所有必要应用已安装
2025-07-28 09:26:15 - InstagramDMTask - INFO - [模拟器4] 开始启动V2Ray应用
2025-07-28 09:26:15 - InstagramDMTask - INFO - [模拟器4] V2Ray启动命令执行成功，等待应用加载
2025-07-28 09:26:15 - InstagramDMTask - INFO - [模拟器4] V2Ray应用启动结果: 成功
2025-07-28 09:26:15 - InstagramDMTask - INFO - [模拟器3] 当前连接状态: 未连接
2025-07-28 09:26:15 - InstagramDMTask - INFO - [模拟器3] V2Ray节点未连接，开始连接
2025-07-28 09:26:16 - InstagramDMTask - INFO - [模拟器3] 已点击连接按钮，等待连接完成
2025-07-28 09:26:18 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray应用启动成功
2025-07-28 09:26:18 - InstagramDMTask - INFO - [模拟器4] 开始检查V2Ray节点列表状态
2025-07-28 09:26:19 - InstagramDMTask - INFO - [模拟器3] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 09:26:19 - InstagramDMTask - INFO - [模拟器3] V2Ray节点连接成功
2025-07-28 09:26:19 - InstagramDMTask - INFO - [模拟器3] 开始测试V2Ray节点延迟
2025-07-28 09:26:19 - InstagramDMTask - INFO - [模拟器3] 开始V2Ray节点延迟测试
2025-07-28 09:26:19 - InstagramDMTask - INFO - [模拟器4] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 09:26:19 - InstagramDMTask - INFO - [模拟器4] 开始连接V2Ray节点
2025-07-28 09:26:20 - InstagramTaskThread - INFO - [模拟器5] 窗口排列完成
2025-07-28 09:26:20 - InstagramFollowTaskThread - INFO - [模拟器5] 开始执行Instagram直接关注任务
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] 雷电模拟器API初始化成功
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] 已设置ld.emulator_id = 5
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置热加载观察者已注册
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] Instagram私信任务执行器初始化完成
2025-07-28 09:26:20 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务配置加载完成
2025-07-28 09:26:20 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务执行器初始化完成
2025-07-28 09:26:20 - InstagramFollowTask - INFO - [模拟器5] 关注模式已设置为: direct
2025-07-28 09:26:20 - InstagramFollowTask - INFO - [模拟器5] 开始执行Instagram关注任务
2025-07-28 09:26:20 - InstagramFollowTask - WARNING - [模拟器5] 任务开始时间未由线程传递，在此设置
2025-07-28 09:26:20 - InstagramFollowTask - INFO - [模拟器5] 任务超时设置: 12秒，已运行: 0.00秒
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器3] 当前测试状态: 已连接，点击测试连接
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器3] 点击开始延迟测试
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] 模拟器Android系统运行正常，桌面稳定
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] 开始检查应用安装状态
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器3] 已点击测试按钮，等待测试结果
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] 📊 应用安装状态检测结果:
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] ✅ 所有必要应用已安装
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] 开始启动V2Ray应用
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器4] 当前连接状态: 未连接
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器4] V2Ray节点未连接，开始连接
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] V2Ray启动命令执行成功，等待应用加载
2025-07-28 09:26:20 - InstagramDMTask - INFO - [模拟器5] V2Ray应用启动结果: 成功
2025-07-28 09:26:22 - InstagramDMTask - INFO - [模拟器3] 测试状态监控 (1/30): 连接成功：延时 315 毫秒
2025-07-28 09:26:22 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray节点延迟测试成功: 连接成功：延时 315 毫秒
2025-07-28 09:26:22 - InstagramDMTask - INFO - [模拟器3] 等待5秒后进入下一阶段
2025-07-28 09:26:22 - InstagramDMTask - INFO - [模拟器4] 已点击连接按钮，等待连接完成
2025-07-28 09:26:23 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray应用启动成功
2025-07-28 09:26:23 - InstagramDMTask - INFO - [模拟器5] 开始检查V2Ray节点列表状态
2025-07-28 09:26:24 - InstagramDMTask - INFO - [模拟器4] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 09:26:24 - InstagramDMTask - INFO - [模拟器4] V2Ray节点连接成功
2025-07-28 09:26:24 - InstagramDMTask - INFO - [模拟器4] 开始测试V2Ray节点延迟
2025-07-28 09:26:24 - InstagramDMTask - INFO - [模拟器4] 开始V2Ray节点延迟测试
2025-07-28 09:26:25 - InstagramDMTask - INFO - [模拟器5] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 09:26:25 - InstagramDMTask - INFO - [模拟器5] 开始连接V2Ray节点
2025-07-28 09:26:25 - InstagramDMTask - INFO - [模拟器4] 当前测试状态: 已连接，点击测试连接
2025-07-28 09:26:25 - InstagramDMTask - INFO - [模拟器4] 点击开始延迟测试
2025-07-28 09:26:25 - InstagramDMTask - INFO - [模拟器4] 已点击测试按钮，等待测试结果
2025-07-28 09:26:26 - InstagramDMTask - INFO - [模拟器5] 当前连接状态: 未连接
2025-07-28 09:26:26 - InstagramDMTask - INFO - [模拟器5] V2Ray节点未连接，开始连接
2025-07-28 09:26:27 - InstagramDMTask - INFO - [模拟器3] 开始启动Instagram应用
2025-07-28 09:26:27 - InstagramDMTask - INFO - [模拟器3] Instagram启动命令执行成功，等待应用加载
2025-07-28 09:26:27 - InstagramDMTask - INFO - [模拟器4] 测试状态监控 (1/30): 连接成功：延时 317 毫秒
2025-07-28 09:26:27 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray节点延迟测试成功: 连接成功：延时 317 毫秒
2025-07-28 09:26:27 - InstagramDMTask - INFO - [模拟器4] 等待5秒后进入下一阶段
2025-07-28 09:26:27 - TaskActivityHeartbeatManager - INFO - 开始检测 3 个模拟器的心跳状态
2025-07-28 09:26:27 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 09:26:27 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 09:26:27 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 09:26:27 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 09:26:27 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 09:26:27 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-28 09:26:27 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-28 09:26:27 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 09:26:27 - InstagramDMTask - INFO - [模拟器5] 已点击连接按钮，等待连接完成
2025-07-28 09:26:29 - InstagramDMTask - INFO - [模拟器5] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 09:26:29 - InstagramDMTask - INFO - [模拟器5] V2Ray节点连接成功
2025-07-28 09:26:29 - InstagramDMTask - INFO - [模拟器5] 开始测试V2Ray节点延迟
2025-07-28 09:26:29 - InstagramDMTask - INFO - [模拟器5] 开始V2Ray节点延迟测试
2025-07-28 09:26:30 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram应用启动命令执行完成
2025-07-28 09:26:30 - InstagramDMTask - INFO - [模拟器3] Instagram启动检测 第1/5次
2025-07-28 09:26:30 - InstagramDMTask - INFO - [模拟器3] 总任务超时(12秒)，Instagram启动检测中断，耗时: 20.88秒
2025-07-28 09:26:30 - InstagramDMTask - ERROR - [模拟器3] 检测到异常状态: 异常-总任务超时
2025-07-28 09:26:30 - LeiDianNativeAPI - INFO - 雷电原生API初始化成功: G:/leidian/LDPlayer9
2025-07-28 09:26:30 - NativeScreenshotEngine - INFO - 专业截图引擎初始化成功
2025-07-28 09:26:30 - ScreenshotManager - INFO - 原生截图引擎初始化成功
2025-07-28 09:26:30 - NativeScreenshotEngine - INFO - 开始截取模拟器 3 的截图
2025-07-28 09:26:30 - NativeScreenshotEngine - INFO - 模拟器 3 截图成功: test_screenshots\emulator_3_Instagram_总任务超时_1_20250728_092630.png
2025-07-28 09:26:30 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_3_Instagram_总任务超时_1_20250728_092630.png
2025-07-28 09:26:30 - InstagramDMTask - INFO - [模拟器3] 异常状态截图已保存: 异常-总任务超时
2025-07-28 09:26:30 - InstagramDMTask - INFO - [模拟器3] 任务终止原因: 异常-总任务超时
2025-07-28 09:26:30 - InstagramDMTask - INFO - [模拟器3] 开始关闭模拟器...
2025-07-28 09:26:30 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已从任务活动监控移除
2025-07-28 09:26:30 - InstagramDMTask - INFO - [模拟器3] 心跳监控已移除
2025-07-28 09:26:30 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 3 | remaining_running: 2
2025-07-28 09:26:30 - Emulator - INFO - 模拟器停止成功 | emulator_id: 3
2025-07-28 09:26:30 - InstagramDMTask - INFO - [模拟器3] 模拟器已关闭
2025-07-28 09:26:30 - InstagramDMTask - INFO - [模拟器3] 任务状态更新: 失败 - 异常-总任务超时
2025-07-28 09:26:30 - InstagramFollowTask - INFO - [模拟器3] Instagram页面状态检测结果: 任务终止-异常-总任务超时
2025-07-28 09:26:30 - InstagramFollowTask - ERROR - [模拟器3] ❌ Instagram任务终止
2025-07-28 09:26:30 - InstagramFollowTaskThread - ERROR - [模拟器3] Instagram直接关注任务执行失败: 任务终止-异常-总任务超时
2025-07-28 09:26:30 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务stop
2025-07-28 09:26:30 - MainWindowV2 - INFO - 模拟器3停止成功
2025-07-28 09:26:30 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 09:26:30 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 09:26:31 - InstagramDMTask - INFO - [模拟器5] 当前测试状态: 已连接，点击测试连接
2025-07-28 09:26:31 - InstagramDMTask - INFO - [模拟器5] 点击开始延迟测试
2025-07-28 09:26:31 - InstagramDMTask - INFO - [模拟器5] 已点击测试按钮，等待测试结果
2025-07-28 09:26:32 - InstagramDMTask - INFO - [模拟器4] 开始启动Instagram应用
2025-07-28 09:26:32 - InstagramDMTask - INFO - [模拟器4] Instagram启动命令执行成功，等待应用加载
2025-07-28 09:26:32 - InstagramDMTask - INFO - [模拟器5] 测试状态监控 (1/30): 连接成功：延时 282 毫秒
2025-07-28 09:26:32 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray节点延迟测试成功: 连接成功：延时 282 毫秒
2025-07-28 09:26:32 - InstagramDMTask - INFO - [模拟器5] 等待5秒后进入下一阶段
2025-07-28 09:26:35 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram应用启动命令执行完成
2025-07-28 09:26:35 - InstagramDMTask - INFO - [模拟器4] Instagram启动检测 第1/5次
2025-07-28 09:26:35 - InstagramDMTask - INFO - [模拟器4] 总任务超时(12秒)，Instagram启动检测中断，耗时: 20.69秒
2025-07-28 09:26:35 - InstagramDMTask - ERROR - [模拟器4] 检测到异常状态: 异常-总任务超时
2025-07-28 09:26:35 - NativeScreenshotEngine - INFO - 开始截取模拟器 4 的截图
2025-07-28 09:26:35 - NativeScreenshotEngine - INFO - 模拟器 4 截图成功: test_screenshots\emulator_4_Instagram_总任务超时_1_20250728_092635.png
2025-07-28 09:26:35 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_4_Instagram_总任务超时_1_20250728_092635.png
2025-07-28 09:26:35 - InstagramDMTask - INFO - [模拟器4] 异常状态截图已保存: 异常-总任务超时
2025-07-28 09:26:35 - InstagramDMTask - INFO - [模拟器4] 任务终止原因: 异常-总任务超时
2025-07-28 09:26:35 - InstagramDMTask - INFO - [模拟器4] 开始关闭模拟器...
2025-07-28 09:26:35 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已从任务活动监控移除
2025-07-28 09:26:35 - InstagramDMTask - INFO - [模拟器4] 心跳监控已移除
2025-07-28 09:26:35 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 4 | remaining_running: 1
2025-07-28 09:26:35 - Emulator - INFO - 模拟器停止成功 | emulator_id: 4
2025-07-28 09:26:35 - InstagramDMTask - INFO - [模拟器4] 模拟器已关闭
2025-07-28 09:26:35 - InstagramDMTask - INFO - [模拟器4] 任务状态更新: 失败 - 异常-总任务超时
2025-07-28 09:26:35 - InstagramFollowTask - INFO - [模拟器4] Instagram页面状态检测结果: 任务终止-异常-总任务超时
2025-07-28 09:26:35 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务stop
2025-07-28 09:26:35 - InstagramFollowTask - ERROR - [模拟器4] ❌ Instagram任务终止
2025-07-28 09:26:35 - MainWindowV2 - INFO - 模拟器4停止成功
2025-07-28 09:26:35 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 09:26:35 - InstagramFollowTaskThread - ERROR - [模拟器4] Instagram直接关注任务执行失败: 任务终止-异常-总任务超时
2025-07-28 09:26:35 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 09:26:37 - InstagramDMTask - INFO - [模拟器5] 开始启动Instagram应用
2025-07-28 09:26:37 - InstagramDMTask - INFO - [模拟器5] Instagram启动命令执行成功，等待应用加载
2025-07-28 09:26:40 - InstagramDMTask - INFO - [模拟器5] ✅ Instagram应用启动命令执行完成
2025-07-28 09:26:40 - InstagramDMTask - INFO - [模拟器5] Instagram启动检测 第1/5次
2025-07-28 09:26:40 - InstagramDMTask - INFO - [模拟器5] 总任务超时(12秒)，Instagram启动检测中断，耗时: 20.70秒
2025-07-28 09:26:40 - InstagramDMTask - ERROR - [模拟器5] 检测到异常状态: 异常-总任务超时
2025-07-28 09:26:40 - NativeScreenshotEngine - INFO - 开始截取模拟器 5 的截图
2025-07-28 09:26:41 - NativeScreenshotEngine - INFO - 模拟器 5 截图成功: test_screenshots\emulator_5_Instagram_总任务超时_1_20250728_092640.png
2025-07-28 09:26:41 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_5_Instagram_总任务超时_1_20250728_092640.png
2025-07-28 09:26:41 - InstagramDMTask - INFO - [模拟器5] 异常状态截图已保存: 异常-总任务超时
2025-07-28 09:26:41 - InstagramDMTask - INFO - [模拟器5] 任务终止原因: 异常-总任务超时
2025-07-28 09:26:41 - InstagramDMTask - INFO - [模拟器5] 开始关闭模拟器...
2025-07-28 09:26:41 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已从任务活动监控移除
2025-07-28 09:26:41 - InstagramDMTask - INFO - [模拟器5] 心跳监控已移除
2025-07-28 09:26:41 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 5 | remaining_running: 0
2025-07-28 09:26:41 - Emulator - INFO - 模拟器停止成功 | emulator_id: 5
2025-07-28 09:26:41 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务stop
2025-07-28 09:26:41 - InstagramDMTask - INFO - [模拟器5] 模拟器已关闭
2025-07-28 09:26:41 - MainWindowV2 - INFO - 模拟器5停止成功
2025-07-28 09:26:41 - InstagramDMTask - INFO - [模拟器5] 任务状态更新: 失败 - 异常-总任务超时
2025-07-28 09:26:41 - InstagramFollowTask - INFO - [模拟器5] Instagram页面状态检测结果: 任务终止-异常-总任务超时
2025-07-28 09:26:41 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 09:26:41 - InstagramFollowTask - ERROR - [模拟器5] ❌ Instagram任务终止
2025-07-28 09:26:41 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 09:26:41 - InstagramFollowTaskThread - ERROR - [模拟器5] Instagram直接关注任务执行失败: 任务终止-异常-总任务超时
2025-07-28 09:26:42 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 12 -> 1
2025-07-28 09:26:42 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 09:26:42 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:26:42 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 1
2025-07-28 09:26:43 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-28 09:26:43 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-28 09:26:43 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 09:26:43 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-28 09:26:43 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 1 -> 17
2025-07-28 09:26:43 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 09:26:43 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:26:43 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 17
2025-07-28 09:26:44 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 17 -> 2
2025-07-28 09:26:44 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 09:26:44 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:26:44 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 2
2025-07-28 09:26:44 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 2 -> 22
2025-07-28 09:26:44 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 09:26:44 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:26:44 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 22
2025-07-28 09:26:44 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 22 -> 222
2025-07-28 09:26:44 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 09:26:44 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:26:44 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 222
2025-07-28 09:26:45 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-28 09:26:45 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-28 09:26:45 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 09:26:45 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-28 09:26:47 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 222 -> 5
2025-07-28 09:26:47 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 09:26:47 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:26:47 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 5
2025-07-28 09:26:47 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 5 -> 50
2025-07-28 09:26:47 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 09:26:47 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:26:47 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 50
2025-07-28 09:26:47 - BasicConfigUI - INFO - 检测到配置热加载: basic_config.task_timeout_seconds = 50 -> 500
2025-07-28 09:26:47 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 09:26:47 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:26:47 - MainWindowV2 - INFO - 基础配置已变更: basic_config.task_timeout_seconds = 500
2025-07-28 09:26:49 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-28 09:26:49 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-28 09:26:49 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 09:26:49 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-28 09:26:49 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:26:49 - __main__ - INFO - 开始清理资源...
2025-07-28 09:26:49 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:26:49 - __main__ - INFO - 配置已保存
2025-07-28 09:26:49 - __main__ - ERROR - 异步桥梁关闭失败: 'FixedAsyncBridge' object has no attribute 'shutdown'
2025-07-28 09:26:49 - __main__ - INFO - 应用程序已退出
