2025-07-28 09:11:40 - root - INFO - 简化日志系统初始化完成
2025-07-28 09:11:40 - main - INFO - 应用程序启动
2025-07-28 09:11:40 - __main__ - INFO - Qt应用程序已创建
2025-07-28 09:11:40 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 09:11:40 - __main__ - INFO - 统一配置管理器已创建
2025-07-28 09:11:40 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-28 09:11:40 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-28 09:11:40 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-28 09:11:40 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-28 09:11:40 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-28 09:11:40 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-28 09:11:40 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-28 09:11:40 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-28 09:11:40 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-28 09:11:40 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-28 09:11:40 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-28 09:11:40 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 09:11:40 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-28 09:11:40 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-28 09:11:40 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-28 09:11:40 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-28 09:11:40 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-28 09:11:40 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-28 09:11:40 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-28 09:11:40 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-28 09:11:40 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-28 09:11:40 - __main__ - INFO - UI主窗口已创建
2025-07-28 09:11:40 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-28 09:11:41 - __main__ - INFO - 主窗口已显示
2025-07-28 09:11:41 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-28 09:11:41 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-28 09:11:41 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-28 09:11:41 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-28 09:11:41 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 09:11:41 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-28 09:11:41 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 09:11:41 - __main__ - INFO - UI层和业务层已连接
2025-07-28 09:11:41 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 09:11:41 - __main__ - INFO - 启动Qt事件循环
2025-07-28 09:11:41 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 09:11:41 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:11:41 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-28 09:11:41 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 09:11:41 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 09:11:41 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 09:11:41 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 09:11:41 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:11:41 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-28 09:11:41 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 09:11:41 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 09:11:41 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 09:11:41 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 09:11:41 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.30s | count: 1229
2025-07-28 09:11:41 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 09:11:41 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 09:11:41 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 09:11:41 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 09:11:41 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-28 09:11:41 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 09:11:41 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 09:11:41 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 09:11:41 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 09:11:41 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.27s | count: 1229
2025-07-28 09:11:41 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 09:11:41 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 09:11:41 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 09:11:41 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 09:11:42 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-28 09:11:42 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-28 09:11:42 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-28 09:11:42 - __main__ - INFO - 后台服务已启动
2025-07-28 09:11:42 - __main__ - INFO - 延迟启动服务完成
2025-07-28 09:11:44 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:11:46 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-28 09:11:46 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-28 09:11:46 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 09:11:46 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
2025-07-28 09:11:50 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_direct_task
2025-07-28 09:11:50 - MainWindowV2 - INFO - Instagram直接关注任务已启动，涉及10个模拟器
2025-07-28 09:11:50 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram直接关注任务请求
2025-07-28 09:11:50 - MainWindowV2 - INFO - 用户启动Instagram直接关注任务，模拟器数量: 10
2025-07-28 09:11:50 - FixedAsyncBridge - INFO - 开始处理Instagram直接关注任务（线程池模式），模拟器数量: 10
2025-07-28 09:11:50 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
2025-07-28 09:11:50 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 10
2025-07-28 09:11:50 - StartupManager - INFO - 批量启动请求 | count: 10
2025-07-28 09:11:50 - StartupManager - INFO - 启动调度器已启动
2025-07-28 09:11:50 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-28 09:11:50 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-28 09:11:50 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-28 09:11:50 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 3
2025-07-28 09:11:50 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: direct
2025-07-28 09:11:50 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 10
2025-07-28 09:11:50 - InstagramFollowTaskThread - INFO - [模拟器3] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 09:11:50 - InstagramFollowTaskManager - INFO - 为模拟器 3 创建Instagram关注任务线程，模式: direct
2025-07-28 09:11:50 - InstagramFollowTaskThread - INFO - [模拟器4] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 09:11:50 - InstagramFollowTaskManager - INFO - 为模拟器 4 创建Instagram关注任务线程，模式: direct
2025-07-28 09:11:50 - InstagramFollowTaskThread - INFO - [模拟器5] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 09:11:50 - InstagramFollowTaskManager - INFO - 为模拟器 5 创建Instagram关注任务线程，模式: direct
2025-07-28 09:11:50 - InstagramFollowTaskThread - INFO - [模拟器6] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 09:11:50 - InstagramFollowTaskManager - INFO - 为模拟器 6 创建Instagram关注任务线程，模式: direct
2025-07-28 09:11:50 - InstagramFollowTaskThread - INFO - [模拟器7] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 09:11:50 - InstagramFollowTaskManager - INFO - 为模拟器 7 创建Instagram关注任务线程，模式: direct
2025-07-28 09:11:50 - InstagramFollowTaskThread - INFO - [模拟器8] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 09:11:50 - MainWindowV2 - INFO - 10个模拟器: 未知 -> 排队中
2025-07-28 09:11:50 - InstagramFollowTaskManager - INFO - 为模拟器 8 创建Instagram关注任务线程，模式: direct
2025-07-28 09:11:50 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 10 | queued: 10 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 3
2025-07-28 09:11:50 - InstagramFollowTaskThread - INFO - [模拟器9] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 09:11:50 - MainWindowV2 - INFO - 启动进度: 排队10个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/3)
2025-07-28 09:11:50 - InstagramFollowTaskManager - INFO - 为模拟器 9 创建Instagram关注任务线程，模式: direct
2025-07-28 09:11:50 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-28 09:11:50 - InstagramFollowTaskThread - INFO - [模拟器10] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 09:11:50 - MainWindowV2 - INFO - 批量启动完成: 0/10 成功
2025-07-28 09:11:50 - InstagramFollowTaskManager - INFO - 为模拟器 10 创建Instagram关注任务线程，模式: direct
2025-07-28 09:11:50 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 10
2025-07-28 09:11:50 - InstagramFollowTaskThread - INFO - [模拟器11] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 09:11:50 - InstagramFollowTaskManager - INFO - 为模拟器 11 创建Instagram关注任务线程，模式: direct
2025-07-28 09:11:50 - InstagramFollowTaskThread - INFO - [模拟器12] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 09:11:50 - InstagramFollowTaskManager - INFO - 为模拟器 12 创建Instagram关注任务线程，模式: direct
2025-07-28 09:11:50 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建10个Instagram线程
2025-07-28 09:11:50 - FixedAsyncBridge - INFO - 异步桥接器: Instagram直接关注任务请求已处理，状态: started
2025-07-28 09:11:50 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-28 09:11:50 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-28 09:11:50 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 3
2025-07-28 09:11:50 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_direct_task
2025-07-28 09:11:50 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 排队中 | new_state: 启动中
2025-07-28 09:11:50 - MainWindowV2 - INFO - 模拟器3: 排队中 -> 启动中
2025-07-28 09:11:50 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 10 | queued: 9 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 3
2025-07-28 09:11:50 - MainWindowV2 - INFO - 启动进度: 排队9个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/3)
2025-07-28 09:11:50 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 3
2025-07-28 09:11:56 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-28 09:11:56 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 排队中 | new_state: 启动中
2025-07-28 09:11:56 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-28 09:11:56 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 4
2025-07-28 09:11:56 - MainWindowV2 - INFO - 模拟器4: 排队中 -> 启动中
2025-07-28 09:11:56 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 10 | queued: 8 | starting: 2 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 2 | max_concurrent: 3
2025-07-28 09:11:56 - MainWindowV2 - INFO - 启动进度: 排队8个, 启动中2个, 运行0个, 失败0个 (并发槽位:2/3)
2025-07-28 09:11:56 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 4
2025-07-28 09:12:00 - Emulator - INFO - Android系统启动完成 | emulator_id: 3 | elapsed_time: 9.9秒
2025-07-28 09:12:00 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 启动中 -> 运行中
2025-07-28 09:12:00 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 启动中 | new_state: 运行中
2025-07-28 09:12:00 - InstagramFollowTaskManager - INFO - 启动模拟器3的Instagram关注任务线程 - 当前并发: 1/3
2025-07-28 09:12:00 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已添加到任务活动监控，失败计数: 0
2025-07-28 09:12:00 - Emulator - INFO - 模拟器启动成功 | emulator_id: 3 | running_count: 1
2025-07-28 09:12:00 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务start
2025-07-28 09:12:00 - MainWindowV2 - INFO - 模拟器3启动成功
2025-07-28 09:12:00 - InstagramTaskThread - INFO - [模拟器3] 开始等待启动完成
2025-07-28 09:12:00 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 09:12:00 - InstagramTaskThread - INFO - [模拟器3] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 09:12:00 - InstagramTaskThread - INFO - [模拟器3] 开始窗口排列
2025-07-28 09:12:00 - WindowArrangementManager - INFO - 模拟器3启动完成，立即触发窗口排列
2025-07-28 09:12:00 - MainWindowV2 - WARNING - 未找到模拟器3，无法更新状态
2025-07-28 09:12:00 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-28 09:12:00 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 10 | queued: 8 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 10.0 | concurrent_slots_used: 2 | max_concurrent: 3
2025-07-28 09:12:00 - MainWindowV2 - INFO - 启动进度: 排队8个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/3)
2025-07-28 09:12:00 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中, PID: 17504
2025-07-28 09:12:00 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-28 09:12:01 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 09:12:01 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 排队中 | new_state: 启动中
2025-07-28 09:12:01 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 09:12:01 - MainWindowV2 - INFO - 模拟器5: 排队中 -> 启动中
2025-07-28 09:12:01 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 5
2025-07-28 09:12:01 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 10 | queued: 7 | starting: 2 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 10.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-28 09:12:01 - MainWindowV2 - INFO - 启动进度: 排队7个, 启动中2个, 运行1个, 失败0个 (并发槽位:3/3)
2025-07-28 09:12:01 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 5
2025-07-28 09:12:02 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 09:12:02 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 09:12:02 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 09:12:02 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 09:12:02 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 09:12:02 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 09:12:02 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 09:12:02 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 09:12:02 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 09:12:02 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 09:12:02 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 09:12:02 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 09:12:06 - Emulator - INFO - Android系统启动完成 | emulator_id: 4 | elapsed_time: 10.1秒
2025-07-28 09:12:06 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 启动中 -> 运行中
2025-07-28 09:12:06 - InstagramFollowTaskManager - INFO - 启动模拟器4的Instagram关注任务线程 - 当前并发: 2/3
2025-07-28 09:12:06 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 启动中 | new_state: 运行中
2025-07-28 09:12:06 - Emulator - INFO - 模拟器启动成功 | emulator_id: 4 | running_count: 2
2025-07-28 09:12:06 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已添加到任务活动监控，失败计数: 0
2025-07-28 09:12:06 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务start
2025-07-28 09:12:06 - MainWindowV2 - INFO - 模拟器4启动成功
2025-07-28 09:12:06 - InstagramTaskThread - INFO - [模拟器4] 开始等待启动完成
2025-07-28 09:12:06 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 09:12:06 - InstagramTaskThread - INFO - [模拟器4] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 09:12:06 - InstagramTaskThread - INFO - [模拟器4] 开始窗口排列
2025-07-28 09:12:06 - WindowArrangementManager - INFO - 模拟器4启动完成，立即触发窗口排列
2025-07-28 09:12:06 - MainWindowV2 - WARNING - 未找到模拟器4，无法更新状态
2025-07-28 09:12:06 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-28 09:12:06 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 10 | queued: 7 | starting: 1 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 20.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-28 09:12:06 - MainWindowV2 - INFO - 启动进度: 排队7个, 启动中1个, 运行2个, 失败0个 (并发槽位:3/3)
2025-07-28 09:12:06 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中, PID: 9956
2025-07-28 09:12:06 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-28 09:12:08 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 09:12:08 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 09:12:08 - WindowArrangementManager - INFO - 窗口扫描完成，找到 2 个运行中的模拟器窗口
2025-07-28 09:12:08 - WindowArrangementManager - INFO - 找到 2 个正在运行的模拟器窗口
2025-07-28 09:12:08 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 09:12:08 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 09:12:08 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 09:12:08 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已在网格位置 (0, 0)，保持不动
2025-07-28 09:12:08 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 09:12:08 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 09:12:08 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已排列到网格位置 (0, 1): (302, 0) 保持原大小: 302x435
2025-07-28 09:12:08 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/2 个窗口
2025-07-28 09:12:08 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/2 个窗口 (成功: 1/2)
2025-07-28 09:12:08 - InstagramTaskThread - INFO - [模拟器3] 窗口排列完成
2025-07-28 09:12:08 - InstagramFollowTaskThread - INFO - [模拟器3] 开始执行Instagram直接关注任务
2025-07-28 09:12:08 - InstagramDMTask - INFO - [模拟器3] 雷电模拟器API初始化成功
2025-07-28 09:12:08 - InstagramDMTask - INFO - [模拟器3] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 09:12:08 - InstagramDMTask - INFO - [模拟器3] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 09:12:08 - InstagramDMTask - INFO - [模拟器3] 已设置ld.emulator_id = 3
2025-07-28 09:12:08 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 09:12:08 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置热加载观察者已注册
2025-07-28 09:12:08 - InstagramDMTask - INFO - [模拟器3] Instagram私信任务执行器初始化完成
2025-07-28 09:12:08 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务配置加载完成
2025-07-28 09:12:08 - InstagramFollowTask - INFO - [模拟器3] Instagram关注任务执行器初始化完成
2025-07-28 09:12:08 - InstagramFollowTask - INFO - [模拟器3] 关注模式已设置为: direct
2025-07-28 09:12:08 - InstagramFollowTask - INFO - [模拟器3] 开始执行Instagram关注任务
2025-07-28 09:12:08 - InstagramFollowTask - WARNING - [模拟器3] 任务开始时间未由线程传递，在此设置
2025-07-28 09:12:08 - InstagramFollowTask - INFO - [模拟器3] 任务超时设置: 12秒，已运行: 0.00秒
2025-07-28 09:12:09 - InstagramDMTask - INFO - [模拟器3] 模拟器Android系统运行正常，桌面稳定
2025-07-28 09:12:09 - InstagramDMTask - INFO - [模拟器3] 开始检查应用安装状态
2025-07-28 09:12:09 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 09:12:09 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 09:12:09 - InstagramDMTask - INFO - [模拟器3] 📊 应用安装状态检测结果:
2025-07-28 09:12:09 - InstagramDMTask - INFO - [模拟器3] ✅ 所有必要应用已安装
2025-07-28 09:12:09 - InstagramDMTask - INFO - [模拟器3] 开始启动V2Ray应用
2025-07-28 09:12:09 - InstagramDMTask - INFO - [模拟器3] V2Ray启动命令执行成功，等待应用加载
2025-07-28 09:12:09 - InstagramDMTask - INFO - [模拟器3] V2Ray应用启动结果: 成功
2025-07-28 09:12:11 - Emulator - INFO - Android系统启动完成 | emulator_id: 5 | elapsed_time: 10.1秒
2025-07-28 09:12:11 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-28 09:12:11 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 启动中 | new_state: 运行中
2025-07-28 09:12:11 - InstagramFollowTaskManager - INFO - 启动模拟器5的Instagram关注任务线程 - 当前并发: 3/3
2025-07-28 09:12:11 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已添加到任务活动监控，失败计数: 0
2025-07-28 09:12:11 - Emulator - INFO - 模拟器启动成功 | emulator_id: 5 | running_count: 3
2025-07-28 09:12:11 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务start
2025-07-28 09:12:11 - MainWindowV2 - INFO - 模拟器5启动成功
2025-07-28 09:12:11 - InstagramTaskThread - INFO - [模拟器5] 开始等待启动完成
2025-07-28 09:12:11 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 09:12:11 - InstagramTaskThread - INFO - [模拟器5] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 09:12:11 - InstagramTaskThread - INFO - [模拟器5] 开始窗口排列
2025-07-28 09:12:11 - WindowArrangementManager - INFO - 模拟器5启动完成，立即触发窗口排列
2025-07-28 09:12:11 - MainWindowV2 - WARNING - 未找到模拟器5，无法更新状态
2025-07-28 09:12:11 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 09:12:11 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 10 | queued: 7 | starting: 0 | running: 3 | failed: 0 | cancelled: 0 | completed: 3 | percentage: 30.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-28 09:12:11 - MainWindowV2 - INFO - 启动进度: 排队7个, 启动中0个, 运行3个, 失败0个 (并发槽位:3/3)
2025-07-28 09:12:11 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中, PID: 16796
2025-07-28 09:12:11 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 09:12:12 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray应用启动成功
2025-07-28 09:12:12 - InstagramDMTask - INFO - [模拟器3] 开始检查V2Ray节点列表状态
2025-07-28 09:12:13 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 09:12:13 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 09:12:13 - WindowArrangementManager - INFO - 窗口扫描完成，找到 3 个运行中的模拟器窗口
2025-07-28 09:12:13 - WindowArrangementManager - INFO - 找到 3 个正在运行的模拟器窗口
2025-07-28 09:12:13 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 09:12:13 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 09:12:13 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 09:12:13 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已在网格位置 (0, 1)，保持不动
2025-07-28 09:12:13 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已在网格位置 (0, 0)，保持不动
2025-07-28 09:12:13 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 09:12:13 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 09:12:13 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 已排列到网格位置 (0, 2): (604, 0) 保持原大小: 302x435
2025-07-28 09:12:13 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/3 个窗口
2025-07-28 09:12:13 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/3 个窗口 (成功: 1/3)
2025-07-28 09:12:13 - InstagramDMTask - INFO - [模拟器3] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 09:12:13 - InstagramDMTask - INFO - [模拟器3] 开始连接V2Ray节点
2025-07-28 09:12:14 - InstagramTaskThread - INFO - [模拟器4] 窗口排列完成
2025-07-28 09:12:14 - InstagramFollowTaskThread - INFO - [模拟器4] 开始执行Instagram直接关注任务
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] 雷电模拟器API初始化成功
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] 已设置ld.emulator_id = 4
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置热加载观察者已注册
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] Instagram私信任务执行器初始化完成
2025-07-28 09:12:14 - InstagramFollowTask - INFO - [模拟器4] Instagram关注任务配置加载完成
2025-07-28 09:12:14 - InstagramFollowTask - INFO - [模拟器4] Instagram关注任务执行器初始化完成
2025-07-28 09:12:14 - InstagramFollowTask - INFO - [模拟器4] 关注模式已设置为: direct
2025-07-28 09:12:14 - InstagramFollowTask - INFO - [模拟器4] 开始执行Instagram关注任务
2025-07-28 09:12:14 - InstagramFollowTask - WARNING - [模拟器4] 任务开始时间未由线程传递，在此设置
2025-07-28 09:12:14 - InstagramFollowTask - INFO - [模拟器4] 任务超时设置: 12秒，已运行: 0.00秒
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] 模拟器Android系统运行正常，桌面稳定
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] 开始检查应用安装状态
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] 📊 应用安装状态检测结果:
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] ✅ 所有必要应用已安装
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] 开始启动V2Ray应用
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] V2Ray启动命令执行成功，等待应用加载
2025-07-28 09:12:14 - InstagramDMTask - INFO - [模拟器4] V2Ray应用启动结果: 成功
2025-07-28 09:12:15 - InstagramDMTask - INFO - [模拟器3] 当前连接状态: 未连接
2025-07-28 09:12:15 - InstagramDMTask - INFO - [模拟器3] V2Ray节点未连接，开始连接
2025-07-28 09:12:16 - InstagramDMTask - INFO - [模拟器3] 已点击连接按钮，等待连接完成
2025-07-28 09:12:18 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray应用启动成功
2025-07-28 09:12:18 - InstagramDMTask - INFO - [模拟器4] 开始检查V2Ray节点列表状态
2025-07-28 09:12:18 - InstagramDMTask - INFO - [模拟器3] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 09:12:18 - InstagramDMTask - INFO - [模拟器3] V2Ray节点连接成功
2025-07-28 09:12:18 - InstagramDMTask - INFO - [模拟器3] 开始测试V2Ray节点延迟
2025-07-28 09:12:18 - InstagramDMTask - INFO - [模拟器3] 开始V2Ray节点延迟测试
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器4] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器4] 开始连接V2Ray节点
2025-07-28 09:12:19 - InstagramTaskThread - INFO - [模拟器5] 窗口排列完成
2025-07-28 09:12:19 - InstagramFollowTaskThread - INFO - [模拟器5] 开始执行Instagram直接关注任务
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] 雷电模拟器API初始化成功
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] 已设置ld.emulator_id = 5
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置热加载观察者已注册
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] Instagram私信任务执行器初始化完成
2025-07-28 09:12:19 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务配置加载完成
2025-07-28 09:12:19 - InstagramFollowTask - INFO - [模拟器5] Instagram关注任务执行器初始化完成
2025-07-28 09:12:19 - InstagramFollowTask - INFO - [模拟器5] 关注模式已设置为: direct
2025-07-28 09:12:19 - InstagramFollowTask - INFO - [模拟器5] 开始执行Instagram关注任务
2025-07-28 09:12:19 - InstagramFollowTask - WARNING - [模拟器5] 任务开始时间未由线程传递，在此设置
2025-07-28 09:12:19 - InstagramFollowTask - INFO - [模拟器5] 任务超时设置: 12秒，已运行: 0.00秒
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] 模拟器Android系统运行正常，桌面稳定
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] 开始检查应用安装状态
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] 📊 应用安装状态检测结果:
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] ✅ 所有必要应用已安装
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] 开始启动V2Ray应用
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器3] 当前测试状态: 已连接，点击测试连接
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器3] 点击开始延迟测试
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] V2Ray启动命令执行成功，等待应用加载
2025-07-28 09:12:19 - InstagramDMTask - INFO - [模拟器5] V2Ray应用启动结果: 成功
2025-07-28 09:12:20 - InstagramDMTask - INFO - [模拟器3] 已点击测试按钮，等待测试结果
2025-07-28 09:12:20 - TaskActivityHeartbeatManager - INFO - 开始检测 3 个模拟器的心跳状态
2025-07-28 09:12:20 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 09:12:20 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 09:12:20 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 09:12:20 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 09:12:20 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 09:12:20 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-28 09:12:20 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-28 09:12:20 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 09:12:20 - InstagramDMTask - INFO - [模拟器4] 当前连接状态: 未连接
2025-07-28 09:12:20 - InstagramDMTask - INFO - [模拟器4] V2Ray节点未连接，开始连接
2025-07-28 09:12:21 - InstagramDMTask - INFO - [模拟器3] 测试状态监控 (1/30): 连接成功：延时 501 毫秒
2025-07-28 09:12:21 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray节点延迟测试成功: 连接成功：延时 501 毫秒
2025-07-28 09:12:21 - InstagramDMTask - INFO - [模拟器3] 等待5秒后进入下一阶段
2025-07-28 09:12:21 - InstagramDMTask - INFO - [模拟器4] 已点击连接按钮，等待连接完成
2025-07-28 09:12:23 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray应用启动成功
2025-07-28 09:12:23 - InstagramDMTask - INFO - [模拟器5] 开始检查V2Ray节点列表状态
2025-07-28 09:12:24 - InstagramDMTask - INFO - [模拟器4] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 09:12:24 - InstagramDMTask - INFO - [模拟器4] V2Ray节点连接成功
2025-07-28 09:12:24 - InstagramDMTask - INFO - [模拟器4] 开始测试V2Ray节点延迟
2025-07-28 09:12:24 - InstagramDMTask - INFO - [模拟器4] 开始V2Ray节点延迟测试
2025-07-28 09:12:24 - InstagramDMTask - INFO - [模拟器5] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 09:12:24 - InstagramDMTask - INFO - [模拟器5] 开始连接V2Ray节点
2025-07-28 09:12:25 - InstagramDMTask - INFO - [模拟器4] 当前测试状态: 已连接，点击测试连接
2025-07-28 09:12:25 - InstagramDMTask - INFO - [模拟器4] 点击开始延迟测试
2025-07-28 09:12:25 - InstagramDMTask - INFO - [模拟器4] 已点击测试按钮，等待测试结果
2025-07-28 09:12:25 - InstagramDMTask - INFO - [模拟器5] 当前连接状态: 未连接
2025-07-28 09:12:25 - InstagramDMTask - INFO - [模拟器5] V2Ray节点未连接，开始连接
2025-07-28 09:12:26 - InstagramDMTask - INFO - [模拟器3] 开始启动Instagram应用
2025-07-28 09:12:26 - InstagramDMTask - INFO - [模拟器5] 已点击连接按钮，等待连接完成
2025-07-28 09:12:27 - InstagramDMTask - INFO - [模拟器4] 测试状态监控 (1/30): 连接成功：延时 483 毫秒
2025-07-28 09:12:27 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray节点延迟测试成功: 连接成功：延时 483 毫秒
2025-07-28 09:12:27 - InstagramDMTask - INFO - [模拟器4] 等待5秒后进入下一阶段
2025-07-28 09:12:27 - InstagramDMTask - INFO - [模拟器3] Instagram启动命令执行成功，等待应用加载
2025-07-28 09:12:29 - InstagramDMTask - INFO - [模拟器5] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 09:12:29 - InstagramDMTask - INFO - [模拟器5] V2Ray节点连接成功
2025-07-28 09:12:29 - InstagramDMTask - INFO - [模拟器5] 开始测试V2Ray节点延迟
2025-07-28 09:12:29 - InstagramDMTask - INFO - [模拟器5] 开始V2Ray节点延迟测试
2025-07-28 09:12:30 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram应用启动命令执行完成
2025-07-28 09:12:30 - InstagramDMTask - INFO - [模拟器3] Instagram启动检测 第1/5次
2025-07-28 09:12:30 - InstagramDMTask - INFO - [模拟器3] 总任务超时(12秒)，Instagram启动检测中断，耗时: 21.14秒
2025-07-28 09:12:30 - InstagramDMTask - ERROR - [模拟器3] 检测到异常状态: 异常-总任务超时
2025-07-28 09:12:30 - LeiDianNativeAPI - INFO - 雷电原生API初始化成功: G:/leidian/LDPlayer9
2025-07-28 09:12:30 - NativeScreenshotEngine - INFO - 专业截图引擎初始化成功
2025-07-28 09:12:30 - ScreenshotManager - INFO - 原生截图引擎初始化成功
2025-07-28 09:12:30 - NativeScreenshotEngine - INFO - 开始截取模拟器 3 的截图
2025-07-28 09:12:30 - InstagramDMTask - INFO - [模拟器5] 当前测试状态: 已连接，点击测试连接
2025-07-28 09:12:30 - InstagramDMTask - INFO - [模拟器5] 点击开始延迟测试
2025-07-28 09:12:30 - NativeScreenshotEngine - INFO - 模拟器 3 截图成功: test_screenshots\emulator_3_Instagram_总任务超时_1_20250728_091230.png
2025-07-28 09:12:30 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_3_Instagram_总任务超时_1_20250728_091230.png
2025-07-28 09:12:30 - InstagramDMTask - INFO - [模拟器3] 异常状态截图已保存: 异常-总任务超时
2025-07-28 09:12:30 - InstagramDMTask - INFO - [模拟器3] 任务终止原因: 异常-总任务超时
2025-07-28 09:12:30 - InstagramDMTask - INFO - [模拟器3] 开始关闭模拟器...
2025-07-28 09:12:30 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已从任务活动监控移除
2025-07-28 09:12:30 - InstagramDMTask - INFO - [模拟器3] 心跳监控已移除
2025-07-28 09:12:30 - InstagramDMTask - INFO - [模拟器5] 已点击测试按钮，等待测试结果
2025-07-28 09:12:30 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 3 | remaining_running: 2
2025-07-28 09:12:30 - Emulator - INFO - 模拟器停止成功 | emulator_id: 3
2025-07-28 09:12:30 - InstagramDMTask - INFO - [模拟器3] 模拟器已关闭
2025-07-28 09:12:30 - InstagramDMTask - INFO - [模拟器3] 任务状态更新: 失败 - 异常-总任务超时
2025-07-28 09:12:30 - InstagramFollowTask - INFO - [模拟器3] Instagram页面状态检测结果: 任务终止-异常-总任务超时
2025-07-28 09:12:30 - InstagramFollowTask - ERROR - [模拟器3] ❌ Instagram任务终止
2025-07-28 09:12:30 - InstagramFollowTaskThread - ERROR - [模拟器3] Instagram直接关注任务执行失败: 任务终止-异常-总任务超时
2025-07-28 09:12:30 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务stop
2025-07-28 09:12:30 - MainWindowV2 - INFO - 模拟器3停止成功
2025-07-28 09:12:30 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 09:12:30 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 09:12:31 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器6状态变化: 排队中 -> 启动中
2025-07-28 09:12:31 - Emulator - INFO - 模拟器状态变化 | emulator_id: 6 | old_state: 排队中 | new_state: 启动中
2025-07-28 09:12:31 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器6状态变化: 排队中 -> 启动中
2025-07-28 09:12:31 - MainWindowV2 - INFO - 模拟器6: 排队中 -> 启动中
2025-07-28 09:12:31 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 6
2025-07-28 09:12:31 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 10 | queued: 6 | starting: 1 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 20.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-28 09:12:31 - MainWindowV2 - INFO - 启动进度: 排队6个, 启动中1个, 运行2个, 失败0个 (并发槽位:3/3)
2025-07-28 09:12:31 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 6
2025-07-28 09:12:31 - InstagramDMTask - INFO - [模拟器5] 测试状态监控 (1/30): 连接成功：延时 294 毫秒
2025-07-28 09:12:31 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray节点延迟测试成功: 连接成功：延时 294 毫秒
2025-07-28 09:12:31 - InstagramDMTask - INFO - [模拟器5] 等待5秒后进入下一阶段
2025-07-28 09:12:32 - InstagramDMTask - INFO - [模拟器4] 开始启动Instagram应用
2025-07-28 09:12:32 - InstagramDMTask - INFO - [模拟器4] Instagram启动命令执行成功，等待应用加载
2025-07-28 09:12:35 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram应用启动命令执行完成
2025-07-28 09:12:35 - InstagramDMTask - INFO - [模拟器4] Instagram启动检测 第1/5次
2025-07-28 09:12:35 - InstagramDMTask - INFO - [模拟器4] 总任务超时(12秒)，Instagram启动检测中断，耗时: 20.95秒
2025-07-28 09:12:35 - InstagramDMTask - ERROR - [模拟器4] 检测到异常状态: 异常-总任务超时
2025-07-28 09:12:35 - NativeScreenshotEngine - INFO - 开始截取模拟器 4 的截图
2025-07-28 09:12:35 - NativeScreenshotEngine - INFO - 模拟器 4 截图成功: test_screenshots\emulator_4_Instagram_总任务超时_1_20250728_091235.png
2025-07-28 09:12:35 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_4_Instagram_总任务超时_1_20250728_091235.png
2025-07-28 09:12:35 - InstagramDMTask - INFO - [模拟器4] 异常状态截图已保存: 异常-总任务超时
2025-07-28 09:12:35 - InstagramDMTask - INFO - [模拟器4] 任务终止原因: 异常-总任务超时
2025-07-28 09:12:35 - InstagramDMTask - INFO - [模拟器4] 开始关闭模拟器...
2025-07-28 09:12:35 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已从任务活动监控移除
2025-07-28 09:12:35 - InstagramDMTask - INFO - [模拟器4] 心跳监控已移除
2025-07-28 09:12:35 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 4 | remaining_running: 1
2025-07-28 09:12:35 - Emulator - INFO - 模拟器停止成功 | emulator_id: 4
2025-07-28 09:12:35 - InstagramDMTask - INFO - [模拟器4] 模拟器已关闭
2025-07-28 09:12:35 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务stop
2025-07-28 09:12:35 - InstagramDMTask - INFO - [模拟器4] 任务状态更新: 失败 - 异常-总任务超时
2025-07-28 09:12:35 - MainWindowV2 - INFO - 模拟器4停止成功
2025-07-28 09:12:35 - InstagramFollowTask - INFO - [模拟器4] Instagram页面状态检测结果: 任务终止-异常-总任务超时
2025-07-28 09:12:35 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 09:12:35 - InstagramFollowTask - ERROR - [模拟器4] ❌ Instagram任务终止
2025-07-28 09:12:35 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 09:12:35 - InstagramFollowTaskThread - ERROR - [模拟器4] Instagram直接关注任务执行失败: 任务终止-异常-总任务超时
2025-07-28 09:12:36 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器7状态变化: 排队中 -> 启动中
2025-07-28 09:12:36 - Emulator - INFO - 模拟器状态变化 | emulator_id: 7 | old_state: 排队中 | new_state: 启动中
2025-07-28 09:12:36 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器7状态变化: 排队中 -> 启动中
2025-07-28 09:12:36 - MainWindowV2 - INFO - 模拟器7: 排队中 -> 启动中
2025-07-28 09:12:36 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 7
2025-07-28 09:12:36 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 10 | queued: 5 | starting: 2 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 10.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-28 09:12:36 - MainWindowV2 - INFO - 启动进度: 排队5个, 启动中2个, 运行1个, 失败0个 (并发槽位:3/3)
2025-07-28 09:12:36 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 7
2025-07-28 09:12:36 - InstagramDMTask - INFO - [模拟器5] 开始启动Instagram应用
2025-07-28 09:12:37 - InstagramDMTask - INFO - [模拟器5] Instagram启动命令执行成功，等待应用加载
2025-07-28 09:12:40 - InstagramDMTask - INFO - [模拟器5] ✅ Instagram应用启动命令执行完成
2025-07-28 09:12:40 - InstagramDMTask - INFO - [模拟器5] Instagram启动检测 第1/5次
2025-07-28 09:12:40 - InstagramDMTask - INFO - [模拟器5] 总任务超时(12秒)，Instagram启动检测中断，耗时: 20.74秒
2025-07-28 09:12:40 - InstagramDMTask - ERROR - [模拟器5] 检测到异常状态: 异常-总任务超时
2025-07-28 09:12:40 - NativeScreenshotEngine - INFO - 开始截取模拟器 5 的截图
2025-07-28 09:12:40 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 09:12:40 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 09:12:40 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-28 09:12:40 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-28 09:12:40 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 09:12:40 - NativeScreenshotEngine - INFO - 模拟器 5 截图成功: test_screenshots\emulator_5_Instagram_总任务超时_1_20250728_091240.png
2025-07-28 09:12:40 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_5_Instagram_总任务超时_1_20250728_091240.png
2025-07-28 09:12:40 - InstagramDMTask - INFO - [模拟器5] 异常状态截图已保存: 异常-总任务超时
2025-07-28 09:12:40 - InstagramDMTask - INFO - [模拟器5] 任务终止原因: 异常-总任务超时
2025-07-28 09:12:40 - InstagramDMTask - INFO - [模拟器5] 开始关闭模拟器...
2025-07-28 09:12:40 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已从任务活动监控移除
2025-07-28 09:12:40 - InstagramDMTask - INFO - [模拟器5] 心跳监控已移除
2025-07-28 09:12:40 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 5 | remaining_running: 0
2025-07-28 09:12:40 - Emulator - INFO - 模拟器停止成功 | emulator_id: 5
2025-07-28 09:12:40 - InstagramDMTask - INFO - [模拟器5] 模拟器已关闭
2025-07-28 09:12:40 - InstagramDMTask - INFO - [模拟器5] 任务状态更新: 失败 - 异常-总任务超时
2025-07-28 09:12:40 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务stop
2025-07-28 09:12:40 - InstagramFollowTask - INFO - [模拟器5] Instagram页面状态检测结果: 任务终止-异常-总任务超时
2025-07-28 09:12:40 - MainWindowV2 - INFO - 模拟器5停止成功
2025-07-28 09:12:40 - InstagramFollowTask - ERROR - [模拟器5] ❌ Instagram任务终止
2025-07-28 09:12:40 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 09:12:40 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 09:12:40 - InstagramFollowTaskThread - ERROR - [模拟器5] Instagram直接关注任务执行失败: 任务终止-异常-总任务超时
2025-07-28 09:12:41 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器8状态变化: 排队中 -> 启动中
2025-07-28 09:12:41 - Emulator - INFO - 模拟器状态变化 | emulator_id: 8 | old_state: 排队中 | new_state: 启动中
2025-07-28 09:12:41 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器8状态变化: 排队中 -> 启动中
2025-07-28 09:12:41 - MainWindowV2 - INFO - 模拟器8: 排队中 -> 启动中
2025-07-28 09:12:41 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 8
2025-07-28 09:12:41 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 10 | queued: 4 | starting: 3 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-28 09:12:41 - MainWindowV2 - INFO - 启动进度: 排队4个, 启动中3个, 运行0个, 失败0个 (并发槽位:3/3)
2025-07-28 09:12:41 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 8
2025-07-28 09:12:44 - Emulator - INFO - Android系统启动完成 | emulator_id: 6 | elapsed_time: 13.4秒
2025-07-28 09:12:44 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器6状态变化: 启动中 -> 运行中
2025-07-28 09:12:44 - InstagramFollowTaskManager - INFO - 启动模拟器6的Instagram关注任务线程 - 当前并发: 4/3
2025-07-28 09:12:44 - Emulator - INFO - 模拟器状态变化 | emulator_id: 6 | old_state: 启动中 | new_state: 运行中
2025-07-28 09:12:44 - Emulator - INFO - 模拟器启动成功 | emulator_id: 6 | running_count: 1
2025-07-28 09:12:44 - TaskActivityHeartbeatManager - INFO - 模拟器 6 已添加到任务活动监控，失败计数: 0
2025-07-28 09:12:44 - MainWindowV2 - INFO - 任务完成: 模拟器6, 任务start
2025-07-28 09:12:44 - MainWindowV2 - INFO - 模拟器6启动成功
2025-07-28 09:12:44 - InstagramTaskThread - INFO - [模拟器6] 开始等待启动完成
2025-07-28 09:12:44 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=6, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 09:12:44 - InstagramTaskThread - INFO - [模拟器6] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 09:12:44 - InstagramTaskThread - INFO - [模拟器6] 开始窗口排列
2025-07-28 09:12:44 - WindowArrangementManager - INFO - 模拟器6启动完成，立即触发窗口排列
2025-07-28 09:12:44 - MainWindowV2 - WARNING - 未找到模拟器6，无法更新状态
2025-07-28 09:12:44 - MainWindowV2 - INFO - 模拟器6: 启动中 -> 运行中
2025-07-28 09:12:44 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 10 | queued: 4 | starting: 2 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 10.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-28 09:12:44 - MainWindowV2 - INFO - 启动进度: 排队4个, 启动中2个, 运行1个, 失败0个 (并发槽位:3/3)
2025-07-28 09:12:45 - MainWindowV2 - INFO - 模拟器6: 启动中 -> 运行中, PID: 19364
2025-07-28 09:12:45 - MainWindowV2 - INFO - 模拟器6: 启动中 -> 运行中
2025-07-28 09:12:46 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 09:12:46 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 09:12:46 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 09:12:46 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 09:12:46 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 09:12:46 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 09:12:46 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-6' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 09:12:46 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 09:12:46 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 09:12:46 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-6' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 09:12:46 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 09:12:46 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 09:12:50 - Emulator - INFO - Android系统启动完成 | emulator_id: 7 | elapsed_time: 13.7秒
2025-07-28 09:12:50 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器7状态变化: 启动中 -> 运行中
2025-07-28 09:12:50 - InstagramFollowTaskManager - INFO - 启动模拟器7的Instagram关注任务线程 - 当前并发: 5/3
2025-07-28 09:12:50 - Emulator - INFO - 模拟器状态变化 | emulator_id: 7 | old_state: 启动中 | new_state: 运行中
2025-07-28 09:12:50 - Emulator - INFO - 模拟器启动成功 | emulator_id: 7 | running_count: 2
2025-07-28 09:12:50 - TaskActivityHeartbeatManager - INFO - 模拟器 7 已添加到任务活动监控，失败计数: 0
2025-07-28 09:12:50 - MainWindowV2 - INFO - 任务完成: 模拟器7, 任务start
2025-07-28 09:12:50 - MainWindowV2 - INFO - 模拟器7启动成功
2025-07-28 09:12:50 - InstagramTaskThread - INFO - [模拟器7] 开始等待启动完成
2025-07-28 09:12:50 - InstagramTaskThread - INFO - [模拟器7] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 09:12:50 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=7, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 09:12:50 - InstagramTaskThread - INFO - [模拟器7] 开始窗口排列
2025-07-28 09:12:50 - WindowArrangementManager - INFO - 模拟器7启动完成，立即触发窗口排列
2025-07-28 09:12:50 - MainWindowV2 - WARNING - 未找到模拟器7，无法更新状态
2025-07-28 09:12:50 - MainWindowV2 - INFO - 模拟器7: 启动中 -> 运行中
2025-07-28 09:12:50 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 10 | queued: 4 | starting: 1 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 20.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-28 09:12:50 - MainWindowV2 - INFO - 启动进度: 排队4个, 启动中1个, 运行2个, 失败0个 (并发槽位:3/3)
2025-07-28 09:12:50 - MainWindowV2 - INFO - 模拟器7: 启动中 -> 运行中, PID: 8044
2025-07-28 09:12:50 - MainWindowV2 - INFO - 模拟器7: 启动中 -> 运行中
2025-07-28 09:12:51 - Emulator - INFO - Android系统启动完成 | emulator_id: 8 | elapsed_time: 10.2秒
2025-07-28 09:12:51 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器8状态变化: 启动中 -> 运行中
2025-07-28 09:12:51 - InstagramFollowTaskManager - INFO - 启动模拟器8的Instagram关注任务线程 - 当前并发: 6/3
2025-07-28 09:12:51 - Emulator - INFO - 模拟器状态变化 | emulator_id: 8 | old_state: 启动中 | new_state: 运行中
2025-07-28 09:12:51 - Emulator - INFO - 模拟器启动成功 | emulator_id: 8 | running_count: 3
2025-07-28 09:12:51 - TaskActivityHeartbeatManager - INFO - 模拟器 8 已添加到任务活动监控，失败计数: 0
2025-07-28 09:12:51 - MainWindowV2 - INFO - 任务完成: 模拟器8, 任务start
2025-07-28 09:12:51 - MainWindowV2 - INFO - 模拟器8启动成功
2025-07-28 09:12:51 - InstagramTaskThread - INFO - [模拟器8] 开始等待启动完成
2025-07-28 09:12:51 - InstagramTaskThread - INFO - [模拟器8] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 09:12:51 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=8, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 09:12:51 - InstagramTaskThread - INFO - [模拟器8] 开始窗口排列
2025-07-28 09:12:51 - WindowArrangementManager - INFO - 模拟器8启动完成，立即触发窗口排列
2025-07-28 09:12:51 - MainWindowV2 - WARNING - 未找到模拟器8，无法更新状态
2025-07-28 09:12:51 - MainWindowV2 - INFO - 模拟器8: 启动中 -> 运行中
2025-07-28 09:12:51 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 10 | queued: 4 | starting: 0 | running: 3 | failed: 0 | cancelled: 0 | completed: 3 | percentage: 30.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-28 09:12:51 - MainWindowV2 - INFO - 启动进度: 排队4个, 启动中0个, 运行3个, 失败0个 (并发槽位:3/3)
2025-07-28 09:12:52 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 09:12:52 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 09:12:52 - WindowArrangementManager - INFO - 窗口扫描完成，找到 3 个运行中的模拟器窗口
2025-07-28 09:12:52 - WindowArrangementManager - INFO - 找到 3 个正在运行的模拟器窗口
2025-07-28 09:12:52 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 09:12:52 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 09:12:52 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-8' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 09:12:52 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-7' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 09:12:52 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-6' 已在网格位置 (0, 0)，保持不动
2025-07-28 09:12:52 - WindowArrangementManager - INFO - 需要排列 2 个窗口
2025-07-28 09:12:52 - WindowArrangementManager - INFO - 找到 2 个空闲网格位置
2025-07-28 09:12:52 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-8' 已排列到网格位置 (0, 1): (302, 0) 保持原大小: 302x435
2025-07-28 09:12:52 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-7' 已排列到网格位置 (0, 2): (604, 0) 保持原大小: 302x435
2025-07-28 09:12:52 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 2/3 个窗口
2025-07-28 09:12:52 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 2/3 个窗口 (成功: 2/3)
2025-07-28 09:12:52 - MainWindowV2 - INFO - 模拟器8: 启动中 -> 运行中, PID: 24352
2025-07-28 09:12:52 - MainWindowV2 - INFO - 模拟器8: 启动中 -> 运行中
2025-07-28 09:12:52 - InstagramTaskThread - INFO - [模拟器6] 窗口排列完成
2025-07-28 09:12:52 - InstagramFollowTaskThread - INFO - [模拟器6] 开始执行Instagram直接关注任务
2025-07-28 09:12:52 - InstagramDMTask - INFO - [模拟器6] 雷电模拟器API初始化成功
2025-07-28 09:12:52 - InstagramDMTask - INFO - [模拟器6] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 09:12:52 - InstagramDMTask - INFO - [模拟器6] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 09:12:52 - InstagramDMTask - INFO - [模拟器6] 已设置ld.emulator_id = 6
2025-07-28 09:12:52 - InstagramDMTask - INFO - [模拟器6] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 09:12:52 - InstagramDMTask - INFO - [模拟器6] Instagram任务配置热加载观察者已注册
2025-07-28 09:12:52 - InstagramDMTask - INFO - [模拟器6] Instagram私信任务执行器初始化完成
2025-07-28 09:12:52 - InstagramFollowTask - INFO - [模拟器6] Instagram关注任务配置加载完成
2025-07-28 09:12:52 - InstagramFollowTask - INFO - [模拟器6] Instagram关注任务执行器初始化完成
2025-07-28 09:12:52 - InstagramFollowTask - INFO - [模拟器6] 关注模式已设置为: direct
2025-07-28 09:12:52 - InstagramFollowTask - INFO - [模拟器6] 开始执行Instagram关注任务
2025-07-28 09:12:52 - InstagramFollowTask - WARNING - [模拟器6] 任务开始时间未由线程传递，在此设置
2025-07-28 09:12:52 - InstagramFollowTask - INFO - [模拟器6] 任务超时设置: 12秒，已运行: 0.00秒
2025-07-28 09:12:52 - InstagramDMTask - INFO - [模拟器6] 模拟器Android系统运行正常，桌面稳定
2025-07-28 09:12:52 - InstagramDMTask - INFO - [模拟器6] 开始检查应用安装状态
2025-07-28 09:12:52 - InstagramDMTask - INFO - [模拟器6] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 09:12:53 - InstagramDMTask - INFO - [模拟器6] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-28 09:12:53 - InstagramDMTask - INFO - [模拟器6] 📊 应用安装状态检测结果:
2025-07-28 09:12:53 - InstagramDMTask - INFO - [模拟器6] ✅ 所有必要应用已安装
2025-07-28 09:12:53 - InstagramDMTask - INFO - [模拟器6] 开始启动V2Ray应用
2025-07-28 09:12:53 - InstagramDMTask - INFO - [模拟器6] V2Ray启动命令执行成功，等待应用加载
2025-07-28 09:12:53 - InstagramDMTask - INFO - [模拟器6] V2Ray应用启动结果: 成功
2025-07-28 09:12:53 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 09:12:53 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 09:12:53 - WindowArrangementManager - INFO - 窗口扫描完成，找到 3 个运行中的模拟器窗口
2025-07-28 09:12:53 - WindowArrangementManager - INFO - 找到 3 个正在运行的模拟器窗口
2025-07-28 09:12:53 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 09:12:53 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 09:12:53 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-8' 已在网格位置 (0, 1)，保持不动
2025-07-28 09:12:53 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-7' 已在网格位置 (0, 2)，保持不动
2025-07-28 09:12:53 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-6' 已在网格位置 (0, 0)，保持不动
2025-07-28 09:12:53 - WindowArrangementManager - INFO - 所有窗口都已在网格位置，无需排列
2025-07-28 09:12:53 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 3/3 个窗口
2025-07-28 09:12:53 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 3/3 个窗口 (成功: 3/3)
2025-07-28 09:12:56 - InstagramDMTask - INFO - [模拟器6] ✅ V2Ray应用启动成功
2025-07-28 09:12:56 - InstagramDMTask - INFO - [模拟器6] 开始检查V2Ray节点列表状态
2025-07-28 09:12:57 - InstagramDMTask - INFO - [模拟器6] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 09:12:57 - InstagramDMTask - INFO - [模拟器6] 开始连接V2Ray节点
2025-07-28 09:12:58 - InstagramTaskThread - INFO - [模拟器7] 窗口排列完成
2025-07-28 09:12:58 - InstagramFollowTaskThread - INFO - [模拟器7] 开始执行Instagram直接关注任务
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] 雷电模拟器API初始化成功
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] 已设置ld.emulator_id = 7
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] Instagram任务配置热加载观察者已注册
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] Instagram私信任务执行器初始化完成
2025-07-28 09:12:58 - InstagramFollowTask - INFO - [模拟器7] Instagram关注任务配置加载完成
2025-07-28 09:12:58 - InstagramFollowTask - INFO - [模拟器7] Instagram关注任务执行器初始化完成
2025-07-28 09:12:58 - InstagramFollowTask - INFO - [模拟器7] 关注模式已设置为: direct
2025-07-28 09:12:58 - InstagramFollowTask - INFO - [模拟器7] 开始执行Instagram关注任务
2025-07-28 09:12:58 - InstagramFollowTask - WARNING - [模拟器7] 任务开始时间未由线程传递，在此设置
2025-07-28 09:12:58 - InstagramFollowTask - INFO - [模拟器7] 任务超时设置: 12秒，已运行: 0.00秒
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] 模拟器Android系统运行正常，桌面稳定
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] 开始检查应用安装状态
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] 📊 应用安装状态检测结果:
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] ✅ 所有必要应用已安装
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] 开始启动V2Ray应用
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] V2Ray启动命令执行成功，等待应用加载
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器7] V2Ray应用启动结果: 成功
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器6] 当前连接状态: 未连接
2025-07-28 09:12:58 - InstagramDMTask - INFO - [模拟器6] V2Ray节点未连接，开始连接
2025-07-28 09:12:59 - InstagramTaskThread - INFO - [模拟器8] 窗口排列完成
2025-07-28 09:12:59 - InstagramFollowTaskThread - INFO - [模拟器8] 开始执行Instagram直接关注任务
2025-07-28 09:12:59 - InstagramDMTask - INFO - [模拟器8] 雷电模拟器API初始化成功
2025-07-28 09:12:59 - InstagramDMTask - INFO - [模拟器8] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 09:12:59 - InstagramDMTask - INFO - [模拟器8] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 09:12:59 - InstagramDMTask - INFO - [模拟器8] 已设置ld.emulator_id = 8
2025-07-28 09:12:59 - InstagramDMTask - INFO - [模拟器8] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 09:12:59 - InstagramDMTask - INFO - [模拟器8] Instagram任务配置热加载观察者已注册
2025-07-28 09:12:59 - InstagramDMTask - INFO - [模拟器8] Instagram私信任务执行器初始化完成
2025-07-28 09:12:59 - InstagramFollowTask - INFO - [模拟器8] Instagram关注任务配置加载完成
2025-07-28 09:12:59 - InstagramFollowTask - INFO - [模拟器8] Instagram关注任务执行器初始化完成
2025-07-28 09:12:59 - InstagramFollowTask - INFO - [模拟器8] 关注模式已设置为: direct
2025-07-28 09:12:59 - InstagramFollowTask - INFO - [模拟器8] 开始执行Instagram关注任务
2025-07-28 09:12:59 - InstagramFollowTask - WARNING - [模拟器8] 任务开始时间未由线程传递，在此设置
2025-07-28 09:12:59 - InstagramFollowTask - INFO - [模拟器8] 任务超时设置: 12秒，已运行: 0.00秒
2025-07-28 09:13:00 - InstagramDMTask - INFO - [模拟器6] 已点击连接按钮，等待连接完成
2025-07-28 09:13:00 - InstagramDMTask - INFO - [模拟器8] 模拟器Android系统运行正常，桌面稳定
2025-07-28 09:13:00 - InstagramDMTask - INFO - [模拟器8] 开始检查应用安装状态
2025-07-28 09:13:00 - TaskActivityHeartbeatManager - INFO - 开始检测 3 个模拟器的心跳状态
2025-07-28 09:13:00 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 09:13:00 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 6
2025-07-28 09:13:00 - MainWindowV2 - WARNING - 模拟器6心跳状态更新未产生变化
2025-07-28 09:13:00 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 7
2025-07-28 09:13:00 - MainWindowV2 - WARNING - 模拟器7心跳状态更新未产生变化
2025-07-28 09:13:00 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 8
2025-07-28 09:13:00 - MainWindowV2 - WARNING - 模拟器8心跳状态更新未产生变化
2025-07-28 09:13:00 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 09:13:00 - InstagramDMTask - INFO - [模拟器8] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 09:13:00 - InstagramDMTask - INFO - [模拟器8] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 09:13:00 - InstagramDMTask - INFO - [模拟器8] 📊 应用安装状态检测结果:
2025-07-28 09:13:00 - InstagramDMTask - INFO - [模拟器8] ✅ 所有必要应用已安装
2025-07-28 09:13:00 - InstagramDMTask - INFO - [模拟器8] 开始启动V2Ray应用
2025-07-28 09:13:00 - InstagramDMTask - INFO - [模拟器8] V2Ray启动命令执行成功，等待应用加载
2025-07-28 09:13:00 - InstagramDMTask - INFO - [模拟器8] V2Ray应用启动结果: 成功
2025-07-28 09:13:01 - InstagramDMTask - INFO - [模拟器7] ✅ V2Ray应用启动成功
2025-07-28 09:13:01 - InstagramDMTask - INFO - [模拟器7] 开始检查V2Ray节点列表状态
2025-07-28 09:13:02 - InstagramDMTask - INFO - [模拟器6] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 09:13:02 - InstagramDMTask - INFO - [模拟器6] V2Ray节点连接成功
2025-07-28 09:13:02 - InstagramDMTask - INFO - [模拟器6] 开始测试V2Ray节点延迟
2025-07-28 09:13:02 - InstagramDMTask - INFO - [模拟器6] 开始V2Ray节点延迟测试
2025-07-28 09:13:03 - InstagramDMTask - INFO - [模拟器7] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 09:13:03 - InstagramDMTask - INFO - [模拟器7] 开始连接V2Ray节点
2025-07-28 09:13:03 - InstagramDMTask - INFO - [模拟器6] 当前测试状态: 已连接，点击测试连接
2025-07-28 09:13:03 - InstagramDMTask - INFO - [模拟器6] 点击开始延迟测试
2025-07-28 09:13:03 - InstagramDMTask - INFO - [模拟器8] ✅ V2Ray应用启动成功
2025-07-28 09:13:03 - InstagramDMTask - INFO - [模拟器8] 开始检查V2Ray节点列表状态
2025-07-28 09:13:03 - InstagramDMTask - INFO - [模拟器6] 已点击测试按钮，等待测试结果
2025-07-28 09:13:04 - InstagramDMTask - INFO - [模拟器7] 当前连接状态: 未连接
2025-07-28 09:13:04 - InstagramDMTask - INFO - [模拟器7] V2Ray节点未连接，开始连接
2025-07-28 09:13:04 - InstagramDMTask - INFO - [模拟器8] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 09:13:04 - InstagramDMTask - INFO - [模拟器8] 开始连接V2Ray节点
2025-07-28 09:13:05 - InstagramDMTask - INFO - [模拟器6] 测试状态监控 (1/30): 测试中…
2025-07-28 09:13:05 - InstagramDMTask - INFO - [模拟器6] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:05 - InstagramDMTask - INFO - [模拟器7] 已点击连接按钮，等待连接完成
2025-07-28 09:13:06 - InstagramDMTask - INFO - [模拟器8] 当前连接状态: 未连接
2025-07-28 09:13:06 - InstagramDMTask - INFO - [模拟器8] V2Ray节点未连接，开始连接
2025-07-28 09:13:06 - InstagramDMTask - INFO - [模拟器6] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:07 - InstagramDMTask - INFO - [模拟器8] 已点击连接按钮，等待连接完成
2025-07-28 09:13:07 - InstagramDMTask - INFO - [模拟器7] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 09:13:07 - InstagramDMTask - INFO - [模拟器7] V2Ray节点连接成功
2025-07-28 09:13:07 - InstagramDMTask - INFO - [模拟器7] 开始测试V2Ray节点延迟
2025-07-28 09:13:07 - InstagramDMTask - INFO - [模拟器7] 开始V2Ray节点延迟测试
2025-07-28 09:13:08 - InstagramDMTask - INFO - [模拟器6] ✅ V2Ray节点延迟测试成功: 连接成功：延时 3553 毫秒
2025-07-28 09:13:08 - InstagramDMTask - INFO - [模拟器6] 等待5秒后进入下一阶段
2025-07-28 09:13:09 - InstagramDMTask - INFO - [模拟器7] 当前测试状态: 已连接，点击测试连接
2025-07-28 09:13:09 - InstagramDMTask - INFO - [模拟器7] 点击开始延迟测试
2025-07-28 09:13:09 - InstagramDMTask - INFO - [模拟器7] 已点击测试按钮，等待测试结果
2025-07-28 09:13:09 - InstagramDMTask - INFO - [模拟器8] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 09:13:09 - InstagramDMTask - INFO - [模拟器8] V2Ray节点连接成功
2025-07-28 09:13:09 - InstagramDMTask - INFO - [模拟器8] 开始测试V2Ray节点延迟
2025-07-28 09:13:09 - InstagramDMTask - INFO - [模拟器8] 开始V2Ray节点延迟测试
2025-07-28 09:13:10 - InstagramDMTask - INFO - [模拟器7] 测试状态监控 (1/30): 测试中…
2025-07-28 09:13:10 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:10 - InstagramDMTask - INFO - [模拟器8] 当前测试状态: 已连接，点击测试连接
2025-07-28 09:13:10 - InstagramDMTask - INFO - [模拟器8] 点击开始延迟测试
2025-07-28 09:13:11 - InstagramDMTask - INFO - [模拟器8] 已点击测试按钮，等待测试结果
2025-07-28 09:13:11 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:12 - InstagramDMTask - INFO - [模拟器8] 测试状态监控 (1/30): 测试中…
2025-07-28 09:13:12 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:13 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:13 - InstagramDMTask - INFO - [模拟器6] 开始启动Instagram应用
2025-07-28 09:13:13 - InstagramDMTask - INFO - [模拟器6] Instagram启动命令执行成功，等待应用加载
2025-07-28 09:13:13 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:14 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:14 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:15 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:16 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:16 - InstagramDMTask - INFO - [模拟器6] ✅ Instagram应用启动命令执行完成
2025-07-28 09:13:16 - InstagramDMTask - INFO - [模拟器6] Instagram启动检测 第1/5次
2025-07-28 09:13:16 - InstagramDMTask - INFO - [模拟器6] 总任务超时(12秒)，Instagram启动检测中断，耗时: 23.97秒
2025-07-28 09:13:16 - InstagramDMTask - ERROR - [模拟器6] 检测到异常状态: 异常-总任务超时
2025-07-28 09:13:16 - NativeScreenshotEngine - INFO - 开始截取模拟器 6 的截图
2025-07-28 09:13:16 - NativeScreenshotEngine - INFO - 模拟器 6 截图成功: test_screenshots\emulator_6_Instagram_总任务超时_1_20250728_091316.png
2025-07-28 09:13:16 - ScreenshotManager - INFO - 异常截图已保存: test_screenshots\emulator_6_Instagram_总任务超时_1_20250728_091316.png
2025-07-28 09:13:16 - InstagramDMTask - INFO - [模拟器6] 异常状态截图已保存: 异常-总任务超时
2025-07-28 09:13:16 - InstagramDMTask - INFO - [模拟器6] 任务终止原因: 异常-总任务超时
2025-07-28 09:13:16 - InstagramDMTask - INFO - [模拟器6] 开始关闭模拟器...
2025-07-28 09:13:16 - TaskActivityHeartbeatManager - INFO - 模拟器 6 已从任务活动监控移除
2025-07-28 09:13:16 - InstagramDMTask - INFO - [模拟器6] 心跳监控已移除
2025-07-28 09:13:17 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 6 | remaining_running: 2
2025-07-28 09:13:17 - Emulator - INFO - 模拟器停止成功 | emulator_id: 6
2025-07-28 09:13:17 - InstagramDMTask - INFO - [模拟器6] 模拟器已关闭
2025-07-28 09:13:17 - MainWindowV2 - INFO - 任务完成: 模拟器6, 任务stop
2025-07-28 09:13:17 - InstagramDMTask - INFO - [模拟器6] 任务状态更新: 失败 - 异常-总任务超时
2025-07-28 09:13:17 - MainWindowV2 - INFO - 模拟器6停止成功
2025-07-28 09:13:17 - InstagramFollowTask - INFO - [模拟器6] Instagram页面状态检测结果: 任务终止-异常-总任务超时
2025-07-28 09:13:17 - InstagramFollowTask - ERROR - [模拟器6] ❌ Instagram任务终止
2025-07-28 09:13:17 - InstagramFollowTaskThread - ERROR - [模拟器6] Instagram直接关注任务执行失败: 任务终止-异常-总任务超时
2025-07-28 09:13:17 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=6, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 09:13:17 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 09:13:17 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:17 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:17 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器9状态变化: 排队中 -> 启动中
2025-07-28 09:13:17 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器9状态变化: 排队中 -> 启动中
2025-07-28 09:13:17 - Emulator - INFO - 模拟器状态变化 | emulator_id: 9 | old_state: 排队中 | new_state: 启动中
2025-07-28 09:13:17 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 9
2025-07-28 09:13:17 - MainWindowV2 - INFO - 模拟器9: 排队中 -> 启动中
2025-07-28 09:13:17 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 10 | queued: 3 | starting: 1 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 20.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-28 09:13:17 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中1个, 运行2个, 失败0个 (并发槽位:3/3)
2025-07-28 09:13:17 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 9
2025-07-28 09:13:18 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:18 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:19 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:20 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 09:13:20 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 09:13:20 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 7
2025-07-28 09:13:20 - MainWindowV2 - WARNING - 模拟器7心跳状态更新未产生变化
2025-07-28 09:13:20 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 8
2025-07-28 09:13:20 - MainWindowV2 - WARNING - 模拟器8心跳状态更新未产生变化
2025-07-28 09:13:20 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 09:13:20 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:21 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:21 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:22 - InstagramDMTask - ERROR - [模拟器7] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: context deadline exceeded (Client.Timeout exceeded while awaiting headers)
2025-07-28 09:13:22 - InstagramDMTask - INFO - [模拟器7] 点击失败状态重置UI
2025-07-28 09:13:22 - InstagramDMTask - INFO - [模拟器7] 已点击失败状态，等待UI重置
2025-07-28 09:13:23 - InstagramDMTask - INFO - [模拟器7] 继续等待测试结果 (1/3)
2025-07-28 09:13:24 - InstagramDMTask - ERROR - [模拟器8] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: context deadline exceeded (Client.Timeout exceeded while awaiting headers)
2025-07-28 09:13:24 - InstagramDMTask - INFO - [模拟器8] 点击失败状态重置UI
2025-07-28 09:13:24 - InstagramDMTask - INFO - [模拟器7] 测试状态监控 (2/30): 测试中…
2025-07-28 09:13:24 - InstagramDMTask - INFO - [模拟器8] 已点击失败状态，等待UI重置
2025-07-28 09:13:24 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:24 - InstagramDMTask - INFO - [模拟器8] 继续等待测试结果 (1/3)
2025-07-28 09:13:25 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:26 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:26 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:27 - InstagramDMTask - INFO - [模拟器8] 测试状态监控 (2/30): 测试中…
2025-07-28 09:13:27 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:28 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:28 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:29 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:30 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:31 - InstagramDMTask - ERROR - [模拟器7] ❌ V2Ray节点延迟测试失败 (2/3): 失败：: io: read/write on closed pipe
2025-07-28 09:13:31 - InstagramDMTask - INFO - [模拟器7] 点击失败状态重置UI
2025-07-28 09:13:31 - Emulator - INFO - Android系统启动完成 | emulator_id: 9 | elapsed_time: 13.2秒
2025-07-28 09:13:31 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器9状态变化: 启动中 -> 运行中
2025-07-28 09:13:31 - Emulator - INFO - 模拟器状态变化 | emulator_id: 9 | old_state: 启动中 | new_state: 运行中
2025-07-28 09:13:31 - InstagramFollowTaskManager - INFO - 启动模拟器9的Instagram关注任务线程 - 当前并发: 7/3
2025-07-28 09:13:31 - TaskActivityHeartbeatManager - INFO - 模拟器 9 已添加到任务活动监控，失败计数: 0
2025-07-28 09:13:31 - Emulator - INFO - 模拟器启动成功 | emulator_id: 9 | running_count: 3
2025-07-28 09:13:31 - MainWindowV2 - INFO - 任务完成: 模拟器9, 任务start
2025-07-28 09:13:31 - MainWindowV2 - INFO - 模拟器9启动成功
2025-07-28 09:13:31 - InstagramTaskThread - INFO - [模拟器9] 开始等待启动完成
2025-07-28 09:13:31 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=9, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 09:13:31 - InstagramTaskThread - INFO - [模拟器9] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 09:13:31 - WindowArrangementManager - INFO - 模拟器9启动完成，立即触发窗口排列
2025-07-28 09:13:31 - InstagramTaskThread - INFO - [模拟器9] 开始窗口排列
2025-07-28 09:13:31 - MainWindowV2 - WARNING - 未找到模拟器9，无法更新状态
2025-07-28 09:13:31 - MainWindowV2 - INFO - 模拟器9: 启动中 -> 运行中
2025-07-28 09:13:31 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 10 | queued: 3 | starting: 0 | running: 3 | failed: 0 | cancelled: 0 | completed: 3 | percentage: 30.0 | concurrent_slots_used: 3 | max_concurrent: 3
2025-07-28 09:13:31 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中0个, 运行3个, 失败0个 (并发槽位:3/3)
2025-07-28 09:13:31 - InstagramDMTask - INFO - [模拟器7] 已点击失败状态，等待UI重置
2025-07-28 09:13:31 - MainWindowV2 - INFO - 模拟器9: 启动中 -> 运行中, PID: 5268
2025-07-28 09:13:31 - MainWindowV2 - INFO - 模拟器9: 启动中 -> 运行中
2025-07-28 09:13:31 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:31 - InstagramDMTask - INFO - [模拟器7] 继续等待测试结果 (2/3)
2025-07-28 09:13:32 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:32 - InstagramDMTask - ERROR - [模拟器7] ❌ V2Ray节点延迟测试失败 (3/3): 失败：: io: read/write on closed pipe
2025-07-28 09:13:32 - InstagramDMTask - ERROR - [模拟器7] 达到最大失败次数 (3)，触发节点切换
2025-07-28 09:13:32 - InstagramDMTask - ERROR - [模拟器7] V2Ray节点延迟测试失败或超时
2025-07-28 09:13:32 - InstagramDMTask - ERROR - [模拟器7] V2Ray节点延迟测试失败，开始节点切换 (第1/8次)
2025-07-28 09:13:32 - InstagramDMTask - INFO - [模拟器7] 开始V2Ray节点切换流程
2025-07-28 09:13:32 - InstagramDMTask - INFO - [模拟器7] 开始智能滑动浏览节点列表
2025-07-28 09:13:33 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 09:13:33 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 09:13:33 - WindowArrangementManager - INFO - 窗口扫描完成，找到 3 个运行中的模拟器窗口
2025-07-28 09:13:33 - WindowArrangementManager - INFO - 找到 3 个正在运行的模拟器窗口
2025-07-28 09:13:33 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 09:13:33 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 09:13:33 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-9' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 09:13:33 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-8' 已在网格位置 (0, 1)，保持不动
2025-07-28 09:13:33 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-7' 已在网格位置 (0, 2)，保持不动
2025-07-28 09:13:33 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 09:13:33 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 09:13:33 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-9' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 09:13:33 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/3 个窗口
2025-07-28 09:13:33 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/3 个窗口 (成功: 1/3)
2025-07-28 09:13:33 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:34 - InstagramDMTask - INFO - [模拟器7] 计划执行 5 次随机滑动
2025-07-28 09:13:34 - InstagramDMTask - INFO - [模拟器7] 第 1/5 次滑动: down, 距离比例: 0.63
2025-07-28 09:13:34 - InstagramDMTask - INFO - [模拟器7] 第 1 次滑动失败
2025-07-28 09:13:34 - InstagramDMTask - INFO - [模拟器7] 滑动后等待 0.2 秒
2025-07-28 09:13:34 - InstagramDMTask - INFO - [模拟器7] 第 2/5 次滑动: down, 距离比例: 0.39
2025-07-28 09:13:35 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:35 - InstagramDMTask - INFO - [模拟器7] 第 2 次滑动失败
2025-07-28 09:13:35 - InstagramDMTask - INFO - [模拟器7] 滑动后等待 0.2 秒
2025-07-28 09:13:35 - InstagramDMTask - INFO - [模拟器7] 第 3/5 次滑动: down, 距离比例: 0.35
2025-07-28 09:13:36 - InstagramDMTask - INFO - [模拟器7] 第 3 次滑动失败
2025-07-28 09:13:36 - InstagramDMTask - INFO - [模拟器7] 滑动后等待 0.2 秒
2025-07-28 09:13:36 - InstagramDMTask - INFO - [模拟器7] 第 4/5 次滑动: up, 距离比例: 0.66
2025-07-28 09:13:36 - InstagramDMTask - INFO - [模拟器7] 第 4 次滑动失败
2025-07-28 09:13:36 - InstagramDMTask - INFO - [模拟器7] 滑动后等待 0.2 秒
2025-07-28 09:13:37 - InstagramDMTask - INFO - [模拟器7] 第 5/5 次滑动: up, 距离比例: 0.61
2025-07-28 09:13:37 - InstagramDMTask - ERROR - [模拟器8] ❌ V2Ray节点延迟测试失败 (2/3): 失败：: context deadline exceeded (Client.Timeout exceeded while awaiting headers)
2025-07-28 09:13:37 - InstagramDMTask - INFO - [模拟器8] 点击失败状态重置UI
2025-07-28 09:13:37 - InstagramDMTask - INFO - [模拟器7] 第 5 次滑动失败
2025-07-28 09:13:37 - InstagramDMTask - INFO - [模拟器7] 滑动后等待 0.2 秒
2025-07-28 09:13:37 - InstagramDMTask - INFO - [模拟器8] 已点击失败状态，等待UI重置
2025-07-28 09:13:37 - InstagramDMTask - INFO - [模拟器7] ✅ 智能滑动完成，共执行 5 次
2025-07-28 09:13:37 - InstagramDMTask - INFO - [模拟器7] 开始随机选择V2Ray节点
2025-07-28 09:13:38 - InstagramDMTask - INFO - [模拟器8] 继续等待测试结果 (2/3)
2025-07-28 09:13:39 - InstagramTaskThread - INFO - [模拟器9] 窗口排列完成
2025-07-28 09:13:39 - InstagramFollowTaskThread - INFO - [模拟器9] 开始执行Instagram直接关注任务
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] 雷电模拟器API初始化成功
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] 已设置ld.emulator_id = 9
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] Instagram任务配置热加载观察者已注册
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] Instagram私信任务执行器初始化完成
2025-07-28 09:13:39 - InstagramFollowTask - INFO - [模拟器9] Instagram关注任务配置加载完成
2025-07-28 09:13:39 - InstagramFollowTask - INFO - [模拟器9] Instagram关注任务执行器初始化完成
2025-07-28 09:13:39 - InstagramFollowTask - INFO - [模拟器9] 关注模式已设置为: direct
2025-07-28 09:13:39 - InstagramFollowTask - INFO - [模拟器9] 开始执行Instagram关注任务
2025-07-28 09:13:39 - InstagramFollowTask - WARNING - [模拟器9] 任务开始时间未由线程传递，在此设置
2025-07-28 09:13:39 - InstagramFollowTask - INFO - [模拟器9] 任务超时设置: 12秒，已运行: 0.00秒
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] 模拟器Android系统运行正常，桌面稳定
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] 开始检查应用安装状态
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器7] 找到 7 个可用节点
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器7] 随机选择节点: V4-161|香港|x2.0
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器7] 节点位置: [11,65][88,79]
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] 📊 应用安装状态检测结果:
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] ✅ 所有必要应用已安装
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] 开始启动V2Ray应用
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器7] ✅ 成功点击节点: V4-161|香港|x2.0
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器7] 等待1秒让节点切换完成
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] V2Ray启动命令执行成功，等待应用加载
2025-07-28 09:13:39 - InstagramDMTask - INFO - [模拟器9] V2Ray应用启动结果: 成功
2025-07-28 09:13:40 - TaskActivityHeartbeatManager - INFO - 开始检测 3 个模拟器的心跳状态
2025-07-28 09:13:40 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 09:13:40 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 7
2025-07-28 09:13:40 - MainWindowV2 - WARNING - 模拟器7心跳状态更新未产生变化
2025-07-28 09:13:40 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 8
2025-07-28 09:13:40 - MainWindowV2 - WARNING - 模拟器8心跳状态更新未产生变化
2025-07-28 09:13:40 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 9
2025-07-28 09:13:40 - MainWindowV2 - WARNING - 模拟器9心跳状态更新未产生变化
2025-07-28 09:13:40 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 09:13:40 - InstagramDMTask - INFO - [模拟器7] 开始连接V2Ray节点
2025-07-28 09:13:40 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:41 - InstagramDMTask - INFO - [模拟器7] 当前连接状态: 已连接，点击测试连接
2025-07-28 09:13:41 - InstagramDMTask - INFO - [模拟器7] V2Ray节点已连接，无需重复连接
2025-07-28 09:13:41 - InstagramDMTask - INFO - [模拟器7] ✅ V2Ray节点切换成功
2025-07-28 09:13:41 - InstagramDMTask - INFO - [模拟器7] 节点切换成功，重新测试延迟
2025-07-28 09:13:41 - InstagramDMTask - INFO - [模拟器7] 开始测试V2Ray节点延迟
2025-07-28 09:13:41 - InstagramDMTask - INFO - [模拟器7] 开始V2Ray节点延迟测试
2025-07-28 09:13:42 - InstagramDMTask - INFO - [模拟器8] 测试状态监控 (3/30): 测试中…
2025-07-28 09:13:42 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:42 - InstagramDMTask - INFO - [模拟器9] ✅ V2Ray应用启动成功
2025-07-28 09:13:42 - InstagramDMTask - INFO - [模拟器9] 开始检查V2Ray节点列表状态
2025-07-28 09:13:42 - InstagramDMTask - INFO - [模拟器7] 当前测试状态: 已连接，点击测试连接
2025-07-28 09:13:42 - InstagramDMTask - INFO - [模拟器7] 点击开始延迟测试
2025-07-28 09:13:43 - InstagramDMTask - INFO - [模拟器7] 已点击测试按钮，等待测试结果
2025-07-28 09:13:43 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:44 - InstagramDMTask - INFO - [模拟器9] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 09:13:44 - InstagramDMTask - INFO - [模拟器9] 开始连接V2Ray节点
2025-07-28 09:13:44 - InstagramDMTask - INFO - [模拟器7] 测试状态监控 (1/30): 测试中…
2025-07-28 09:13:44 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:44 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:45 - InstagramDMTask - INFO - [模拟器9] 当前连接状态: 未连接
2025-07-28 09:13:45 - InstagramDMTask - INFO - [模拟器9] V2Ray节点未连接，开始连接
2025-07-28 09:13:45 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:45 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:46 - InstagramDMTask - INFO - [模拟器9] 已点击连接按钮，等待连接完成
2025-07-28 09:13:47 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:47 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:48 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:48 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:49 - InstagramDMTask - INFO - [模拟器9] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 09:13:49 - InstagramDMTask - INFO - [模拟器9] V2Ray节点连接成功
2025-07-28 09:13:49 - InstagramDMTask - INFO - [模拟器9] 开始测试V2Ray节点延迟
2025-07-28 09:13:49 - InstagramDMTask - INFO - [模拟器9] 开始V2Ray节点延迟测试
2025-07-28 09:13:49 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:50 - InstagramDMTask - INFO - [模拟器9] 当前测试状态: 已连接，点击测试连接
2025-07-28 09:13:50 - InstagramDMTask - INFO - [模拟器9] 点击开始延迟测试
2025-07-28 09:13:50 - InstagramDMTask - INFO - [模拟器9] 已点击测试按钮，等待测试结果
2025-07-28 09:13:50 - InstagramDMTask - ERROR - [模拟器8] ❌ V2Ray节点延迟测试失败 (3/3): 失败：: net/http: request canceled (Client.Timeout exceeded while awaiting headers)
2025-07-28 09:13:50 - InstagramDMTask - ERROR - [模拟器8] 达到最大失败次数 (3)，触发节点切换
2025-07-28 09:13:50 - InstagramDMTask - ERROR - [模拟器8] V2Ray节点延迟测试失败或超时
2025-07-28 09:13:50 - InstagramDMTask - ERROR - [模拟器8] V2Ray节点延迟测试失败，开始节点切换 (第1/8次)
2025-07-28 09:13:50 - InstagramDMTask - INFO - [模拟器8] 开始V2Ray节点切换流程
2025-07-28 09:13:50 - InstagramDMTask - INFO - [模拟器8] 开始智能滑动浏览节点列表
2025-07-28 09:13:51 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:51 - InstagramDMTask - INFO - [模拟器9] 测试状态监控 (1/30): 测试中…
2025-07-28 09:13:51 - InstagramDMTask - INFO - [模拟器9] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:52 - InstagramDMTask - INFO - [模拟器8] 计划执行 2 次随机滑动
2025-07-28 09:13:52 - InstagramDMTask - INFO - [模拟器8] 第 1/2 次滑动: down, 距离比例: 0.44
2025-07-28 09:13:52 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:52 - InstagramDMTask - INFO - [模拟器8] 第 1 次滑动失败
2025-07-28 09:13:52 - InstagramDMTask - INFO - [模拟器8] 滑动后等待 0.2 秒
2025-07-28 09:13:52 - InstagramDMTask - INFO - [模拟器8] 第 2/2 次滑动: down, 距离比例: 0.58
2025-07-28 09:13:53 - InstagramDMTask - INFO - [模拟器9] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:53 - InstagramDMTask - INFO - [模拟器8] 第 2 次滑动失败
2025-07-28 09:13:53 - InstagramDMTask - INFO - [模拟器8] 滑动后等待 0.2 秒
2025-07-28 09:13:53 - InstagramDMTask - INFO - [模拟器8] ✅ 智能滑动完成，共执行 2 次
2025-07-28 09:13:53 - InstagramDMTask - INFO - [模拟器8] 开始随机选择V2Ray节点
2025-07-28 09:13:53 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:54 - InstagramDMTask - INFO - [模拟器9] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:54 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:55 - InstagramDMTask - INFO - [模拟器8] 找到 7 个可用节点
2025-07-28 09:13:55 - InstagramDMTask - INFO - [模拟器8] 随机选择节点: V4-46|日本|x1.5
2025-07-28 09:13:55 - InstagramDMTask - INFO - [模拟器8] 节点位置: [11,291][82,305]
2025-07-28 09:13:55 - InstagramDMTask - INFO - [模拟器8] ✅ 成功点击节点: V4-46|日本|x1.5
2025-07-28 09:13:55 - InstagramDMTask - INFO - [模拟器8] 等待1秒让节点切换完成
2025-07-28 09:13:55 - InstagramDMTask - INFO - [模拟器9] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:56 - InstagramDMTask - ERROR - [模拟器7] ❌ V2Ray节点延迟测试失败 (1/3): 失败：: net/http: request canceled (Client.Timeout exceeded while awaiting headers)
2025-07-28 09:13:56 - InstagramDMTask - INFO - [模拟器7] 点击失败状态重置UI
2025-07-28 09:13:56 - InstagramDMTask - INFO - [模拟器8] 开始连接V2Ray节点
2025-07-28 09:13:56 - InstagramDMTask - INFO - [模拟器7] 已点击失败状态，等待UI重置
2025-07-28 09:13:56 - InstagramDMTask - INFO - [模拟器7] 继续等待测试结果 (1/3)
2025-07-28 09:13:56 - InstagramDMTask - INFO - [模拟器9] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:57 - InstagramDMTask - INFO - [模拟器8] 当前连接状态: 已连接，点击测试连接
2025-07-28 09:13:57 - InstagramDMTask - INFO - [模拟器8] V2Ray节点已连接，无需重复连接
2025-07-28 09:13:57 - InstagramDMTask - INFO - [模拟器8] ✅ V2Ray节点切换成功
2025-07-28 09:13:57 - InstagramDMTask - INFO - [模拟器8] 节点切换成功，重新测试延迟
2025-07-28 09:13:57 - InstagramDMTask - INFO - [模拟器8] 开始测试V2Ray节点延迟
2025-07-28 09:13:57 - InstagramDMTask - INFO - [模拟器8] 开始V2Ray节点延迟测试
2025-07-28 09:13:58 - InstagramDMTask - INFO - [模拟器7] 测试状态监控 (2/30): 测试中…
2025-07-28 09:13:58 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:58 - InstagramDMTask - INFO - [模拟器9] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:58 - InstagramDMTask - INFO - [模拟器8] 当前测试状态: 已连接，点击测试连接
2025-07-28 09:13:58 - InstagramDMTask - INFO - [模拟器8] 点击开始延迟测试
2025-07-28 09:13:58 - InstagramDMTask - INFO - [模拟器8] 已点击测试按钮，等待测试结果
2025-07-28 09:13:59 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:13:59 - InstagramDMTask - INFO - [模拟器9] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:14:00 - InstagramDMTask - INFO - [模拟器8] 测试状态监控 (1/30): 测试中…
2025-07-28 09:14:00 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:14:00 - TaskActivityHeartbeatManager - INFO - 开始检测 3 个模拟器的心跳状态
2025-07-28 09:14:00 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 09:14:00 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 7
2025-07-28 09:14:00 - MainWindowV2 - WARNING - 模拟器7心跳状态更新未产生变化
2025-07-28 09:14:00 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 8
2025-07-28 09:14:00 - MainWindowV2 - WARNING - 模拟器8心跳状态更新未产生变化
2025-07-28 09:14:00 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 9
2025-07-28 09:14:00 - MainWindowV2 - WARNING - 模拟器9心跳状态更新未产生变化
2025-07-28 09:14:00 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 09:14:00 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:14:00 - InstagramDMTask - INFO - [模拟器9] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:14:01 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:14:02 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:14:02 - InstagramDMTask - INFO - [模拟器9] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:14:02 - InstagramDMTask - INFO - [模拟器8] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:14:03 - InstagramDMTask - INFO - [模拟器7] 🔄 延迟测试进行中，继续等待...
2025-07-28 09:14:03 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:14:03 - __main__ - INFO - 开始清理资源...
2025-07-28 09:14:03 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 09:14:03 - __main__ - INFO - 配置已保存
2025-07-28 09:14:03 - __main__ - ERROR - 异步桥梁关闭失败: 'FixedAsyncBridge' object has no attribute 'shutdown'
2025-07-28 09:14:03 - __main__ - INFO - 应用程序已退出
