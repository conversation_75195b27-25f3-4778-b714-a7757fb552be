2025-07-28 12:49:04 - root - INFO - 简化日志系统初始化完成
2025-07-28 12:49:04 - main - INFO - 应用程序启动
2025-07-28 12:49:04 - __main__ - INFO - Qt应用程序已创建
2025-07-28 12:49:04 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 12:49:04 - __main__ - INFO - 统一配置管理器已创建
2025-07-28 12:49:04 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-28 12:49:04 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-28 12:49:04 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-28 12:49:04 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-28 12:49:04 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-28 12:49:04 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-28 12:49:04 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-28 12:49:04 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-28 12:49:04 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-28 12:49:04 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-28 12:49:04 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-28 12:49:04 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 12:49:04 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-28 12:49:04 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-28 12:49:04 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-28 12:49:04 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-28 12:49:04 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-28 12:49:04 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-28 12:49:04 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-28 12:49:04 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-28 12:49:04 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-28 12:49:04 - __main__ - INFO - UI主窗口已创建
2025-07-28 12:49:04 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-28 12:49:05 - __main__ - INFO - 主窗口已显示
2025-07-28 12:49:05 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-28 12:49:05 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-28 12:49:05 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-28 12:49:05 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-28 12:49:05 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 12:49:05 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-28 12:49:05 - __main__ - INFO - UI层和业务层已连接
2025-07-28 12:49:05 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 12:49:05 - __main__ - INFO - 启动Qt事件循环
2025-07-28 12:49:05 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 12:49:05 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 12:49:05 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 12:49:05 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-28 12:49:05 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 12:49:05 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 12:49:05 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 12:49:05 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 12:49:05 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 12:49:05 - App - INFO - ldconsole命令执行成功，输出长度: 48411
2025-07-28 12:49:05 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 12:49:05 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 12:49:05 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 12:49:05 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 12:49:05 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.26s | count: 1229
2025-07-28 12:49:05 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 12:49:05 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 12:49:05 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 12:49:05 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 12:49:05 - App - INFO - ldconsole命令执行成功，输出长度: 48411
2025-07-28 12:49:05 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 12:49:05 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 12:49:05 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 12:49:05 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 12:49:05 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.25s | count: 1229
2025-07-28 12:49:05 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 12:49:05 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 12:49:05 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 12:49:05 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 12:49:06 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-28 12:49:06 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-28 12:49:06 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-28 12:49:06 - __main__ - INFO - 后台服务已启动
2025-07-28 12:49:06 - __main__ - INFO - 延迟启动服务完成
2025-07-28 12:49:08 - FixedAsyncBridge - INFO - 执行异步操作: instagram_dm_task
2025-07-28 12:49:08 - MainWindowV2 - INFO - Instagram粉丝私信任务已启动，涉及3个模拟器
2025-07-28 12:49:08 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram粉丝私信任务请求
2025-07-28 12:49:08 - MainWindowV2 - INFO - 用户启动Instagram粉丝私信任务，模拟器数量: 3
2025-07-28 12:51:55 - ConfigHotReloadService - INFO - 检测到配置文件变化: app_config.json
2025-07-28 12:51:55 - ConfigHotReloadService - INFO - 检测到配置文件变更，重新加载: app_config.json
2025-07-28 12:51:55 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 12:51:55 - ConfigHotReloadService - INFO - 配置文件app_config.json热加载成功
请求 | count: 3
2025-07-28 12:49:08 - StartupManager - INFO - 启动调度器已启动
2025-07-28 12:49:08 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-28 12:49:08 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建任务线程池
2025-07-28 12:49:08 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-28 12:49:08 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 2
2025-07-28 12:49:08 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 3
2025-07-28 12:49:08 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建3个Instagram线程
2025-07-28 12:49:08 - FixedAsyncBridge - INFO - 异步桥接器: Instagram粉丝私信任务请求已处理，状态: started
2025-07-28 12:49:08 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-28 12:49:08 - MainWindowV2 - INFO - 3个模拟器: 未知 -> 排队中
2025-07-28 12:49:08 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 3
2025-07-28 12:49:08 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 3 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 2
2025-07-28 12:49:08 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/2)
2025-07-28 12:49:08 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-28 12:49:08 - MainWindowV2 - INFO - 批量启动完成: 0/3 成功
2025-07-28 12:49:08 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 3
2025-07-28 12:49:08 - FixedAsyncBridge - INFO - 异步操作完成: instagram_dm_task
2025-07-28 12:49:08 - MainWindowV2 - INFO - Instagram粉丝私信任务启动成功: Instagram粉丝私信任务已启动，涉及3个模拟器（线程池并发模式）
2025-07-28 12:49:08 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 排队中 | new_state: 启动中
2025-07-28 12:49:08 - MainWindowV2 - INFO - 模拟器3: 排队中 -> 启动中
2025-07-28 12:49:08 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 2 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 2
2025-07-28 12:49:08 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/2)
2025-07-28 12:49:08 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 3
2025-07-28 12:49:13 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-28 12:49:13 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 排队中 | new_state: 启动中
2025-07-28 12:49:13 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 4
2025-07-28 12:49:13 - MainWindowV2 - INFO - 模拟器4: 排队中 -> 启动中
2025-07-28 12:49:13 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 2 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 12:49:13 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中2个, 运行0个, 失败0个 (并发槽位:2/2)
2025-07-28 12:49:13 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 4
2025-07-28 12:49:18 - Emulator - INFO - Android系统启动完成 | emulator_id: 3 | elapsed_time: 10.2秒
2025-07-28 12:49:18 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 启动中 -> 运行中
2025-07-28 12:49:18 - InstagramTaskManager - INFO - 启动模拟器3的Instagram私信任务线程 - 当前并发: 1/2
2025-07-28 12:49:18 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 启动中 | new_state: 运行中
2025-07-28 12:49:18 - Emulator - INFO - 模拟器启动成功 | emulator_id: 3 | running_count: 1
2025-07-28 12:49:18 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已添加到任务活动监控，失败计数: 0
2025-07-28 12:49:18 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务start
2025-07-28 12:49:18 - MainWindowV2 - INFO - 模拟器3启动成功
2025-07-28 12:49:18 - InstagramTaskThread - INFO - [模拟器3] 开始等待启动完成
2025-07-28 12:49:18 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 12:49:18 - InstagramTaskThread - INFO - [模拟器3] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 12:49:18 - InstagramTaskThread - INFO - [模拟器3] 开始窗口排列
2025-07-28 12:49:18 - WindowArrangementManager - INFO - 模拟器3启动完成，立即触发窗口排列
2025-07-28 12:49:18 - MainWindowV2 - WARNING - 未找到模拟器3，无法更新状态
2025-07-28 12:49:18 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-28 12:49:18 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 12:49:18 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/2)
2025-07-28 12:49:19 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中, PID: 20524
2025-07-28 12:49:19 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-28 12:49:20 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 12:49:20 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 12:49:20 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 12:49:20 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 12:49:20 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 12:49:20 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 12:49:20 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 12:49:20 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 12:49:20 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 12:49:20 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 12:49:21 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 12:49:21 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 12:49:23 - Emulator - INFO - Android系统启动完成 | emulator_id: 4 | elapsed_time: 10.1秒
2025-07-28 12:49:23 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 启动中 -> 运行中
2025-07-28 12:49:23 - InstagramTaskManager - INFO - 启动模拟器4的Instagram私信任务线程 - 当前并发: 2/2
2025-07-28 12:49:23 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 启动中 | new_state: 运行中
2025-07-28 12:49:23 - Emulator - INFO - 模拟器启动成功 | emulator_id: 4 | running_count: 2
2025-07-28 12:49:23 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已添加到任务活动监控，失败计数: 0
2025-07-28 12:49:23 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务start
2025-07-28 12:49:23 - InstagramTaskThread - INFO - [模拟器4] 开始等待启动完成
2025-07-28 12:49:23 - MainWindowV2 - INFO - 模拟器4启动成功
2025-07-28 12:49:23 - InstagramTaskThread - INFO - [模拟器4] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 12:49:23 - InstagramTaskThread - INFO - [模拟器4] 开始窗口排列
2025-07-28 12:49:23 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 12:49:23 - WindowArrangementManager - INFO - 模拟器4启动完成，立即触发窗口排列
2025-07-28 12:49:23 - MainWindowV2 - WARNING - 未找到模拟器4，无法更新状态
2025-07-28 12:49:23 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-28 12:49:23 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 0 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 66.66666666666666 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 12:49:23 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行2个, 失败0个 (并发槽位:2/2)
2025-07-28 12:49:24 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中, PID: 24376
2025-07-28 12:49:24 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-28 12:49:24 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 12:49:24 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 12:49:24 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 12:49:24 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 12:49:24 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 12:49:24 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 12:49:24 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 12:49:25 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 12:49:25 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 12:49:25 - WindowArrangementManager - INFO - 窗口扫描完成，找到 2 个运行中的模拟器窗口
2025-07-28 12:49:25 - WindowArrangementManager - INFO - 找到 2 个正在运行的模拟器窗口
2025-07-28 12:49:25 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 12:49:25 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 12:49:25 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 12:49:25 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已在网格位置 (0, 0)，保持不动
2025-07-28 12:49:25 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 12:49:25 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 12:49:25 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-4' 已排列到网格位置 (0, 1): (302, 0) 保持原大小: 302x435
2025-07-28 12:49:25 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/2 个窗口
2025-07-28 12:49:25 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/2 个窗口 (成功: 1/2)
2025-07-28 12:49:26 - InstagramTaskThread - INFO - [模拟器3] 窗口排列完成
2025-07-28 12:49:26 - InstagramTaskThread - INFO - [模拟器3] 开始执行Instagram私信任务
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] 雷电模拟器API初始化成功
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] 已设置ld.emulator_id = 3
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置热加载观察者已注册
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] Instagram私信任务执行器初始化完成
2025-07-28 12:49:27 - InstagramTaskThread - INFO - [模拟器3] 任务超时计时已从线程启动开始: 666秒
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] 开始执行Instagram私信任务
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] 任务超时设置: 666秒，已运行: 8.12秒
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] 模拟器Android系统运行正常，桌面稳定
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] 开始检查应用安装状态
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] 📊 应用安装状态检测结果:
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] ✅ 所有必要应用已安装
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] 开始启动V2Ray应用
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] V2Ray启动命令执行成功，等待应用加载
2025-07-28 12:49:27 - InstagramDMTask - INFO - [模拟器3] V2Ray应用启动结果: 成功
2025-07-28 12:49:30 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray应用启动成功
2025-07-28 12:49:30 - InstagramDMTask - INFO - [模拟器3] 开始检查V2Ray节点列表状态
2025-07-28 12:49:31 - InstagramTaskThread - INFO - [模拟器4] 窗口排列完成
2025-07-28 12:49:31 - InstagramTaskThread - INFO - [模拟器4] 开始执行Instagram私信任务
2025-07-28 12:49:31 - InstagramDMTask - INFO - [模拟器4] 雷电模拟器API初始化成功
2025-07-28 12:49:31 - InstagramDMTask - INFO - [模拟器4] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 12:49:31 - InstagramDMTask - INFO - [模拟器4] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 12:49:31 - InstagramDMTask - INFO - [模拟器4] 已设置ld.emulator_id = 4
2025-07-28 12:49:31 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 12:49:31 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置热加载观察者已注册
2025-07-28 12:49:31 - InstagramDMTask - INFO - [模拟器4] Instagram私信任务执行器初始化完成
2025-07-28 12:49:31 - InstagramTaskThread - INFO - [模拟器4] 任务超时计时已从线程启动开始: 666秒
2025-07-28 12:49:31 - InstagramDMTask - INFO - [模拟器4] 开始执行Instagram私信任务
2025-07-28 12:49:31 - InstagramDMTask - INFO - [模拟器4] 任务超时设置: 666秒，已运行: 8.01秒
2025-07-28 12:49:31 - InstagramDMTask - INFO - [模拟器3] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 12:49:31 - InstagramDMTask - INFO - [模拟器3] 开始连接V2Ray节点
2025-07-28 12:49:32 - InstagramDMTask - INFO - [模拟器4] 模拟器Android系统运行正常，桌面稳定
2025-07-28 12:49:32 - InstagramDMTask - INFO - [模拟器4] 开始检查应用安装状态
2025-07-28 12:49:32 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 12:49:32 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram已安装，版本: 321.0.0.39.106
2025-07-28 12:49:32 - InstagramDMTask - INFO - [模拟器4] 📊 应用安装状态检测结果:
2025-07-28 12:49:32 - InstagramDMTask - INFO - [模拟器4] ✅ 所有必要应用已安装
2025-07-28 12:49:32 - InstagramDMTask - INFO - [模拟器4] 开始启动V2Ray应用
2025-07-28 12:49:32 - InstagramDMTask - INFO - [模拟器4] V2Ray启动命令执行成功，等待应用加载
2025-07-28 12:49:32 - InstagramDMTask - INFO - [模拟器4] V2Ray应用启动结果: 成功
2025-07-28 12:49:33 - InstagramDMTask - INFO - [模拟器3] 当前连接状态: 未连接
2025-07-28 12:49:33 - InstagramDMTask - INFO - [模拟器3] V2Ray节点未连接，开始连接
2025-07-28 12:49:34 - InstagramDMTask - INFO - [模拟器3] 已点击连接按钮，等待连接完成
2025-07-28 12:49:35 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray应用启动成功
2025-07-28 12:49:35 - InstagramDMTask - INFO - [模拟器4] 开始检查V2Ray节点列表状态
2025-07-28 12:49:36 - InstagramDMTask - INFO - [模拟器4] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 12:49:36 - InstagramDMTask - INFO - [模拟器4] 开始连接V2Ray节点
2025-07-28 12:49:36 - InstagramDMTask - INFO - [模拟器3] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 12:49:36 - InstagramDMTask - INFO - [模拟器3] V2Ray节点连接成功
2025-07-28 12:49:36 - InstagramDMTask - INFO - [模拟器3] 开始测试V2Ray节点延迟
2025-07-28 12:49:36 - InstagramDMTask - INFO - [模拟器3] 开始V2Ray节点延迟测试
2025-07-28 12:49:37 - InstagramDMTask - INFO - [模拟器4] 当前连接状态: 未连接
2025-07-28 12:49:37 - InstagramDMTask - INFO - [模拟器4] V2Ray节点未连接，开始连接
2025-07-28 12:49:38 - InstagramDMTask - INFO - [模拟器3] 当前测试状态: 已连接，点击测试连接
2025-07-28 12:49:38 - InstagramDMTask - INFO - [模拟器3] 点击开始延迟测试
2025-07-28 12:49:38 - InstagramDMTask - INFO - [模拟器3] 已点击测试按钮，等待测试结果
2025-07-28 12:49:39 - InstagramDMTask - INFO - [模拟器4] 已点击连接按钮，等待连接完成
2025-07-28 12:49:39 - InstagramDMTask - INFO - [模拟器3] 测试状态监控 (1/30): 连接成功：延时 276 毫秒
2025-07-28 12:49:39 - InstagramDMTask - INFO - [模拟器3] ✅ V2Ray节点延迟测试成功: 连接成功：延时 276 毫秒
2025-07-28 12:49:39 - InstagramDMTask - INFO - [模拟器3] 等待5秒后进入下一阶段
2025-07-28 12:49:41 - InstagramDMTask - INFO - [模拟器4] 连接状态检查 (1/10): 已连接，点击测试连接
2025-07-28 12:49:41 - InstagramDMTask - INFO - [模拟器4] V2Ray节点连接成功
2025-07-28 12:49:41 - InstagramDMTask - INFO - [模拟器4] 开始测试V2Ray节点延迟
2025-07-28 12:49:41 - InstagramDMTask - INFO - [模拟器4] 开始V2Ray节点延迟测试
2025-07-28 12:49:42 - InstagramDMTask - INFO - [模拟器4] 当前测试状态: 已连接，点击测试连接
2025-07-28 12:49:42 - InstagramDMTask - INFO - [模拟器4] 点击开始延迟测试
2025-07-28 12:49:42 - InstagramDMTask - INFO - [模拟器4] 已点击测试按钮，等待测试结果
2025-07-28 12:49:44 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 12:49:44 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 12:49:44 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 12:49:44 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 12:49:44 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 12:49:44 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 12:49:44 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 12:49:44 - InstagramDMTask - INFO - [模拟器4] 测试状态监控 (1/30): 连接成功：延时 598 毫秒
2025-07-28 12:49:44 - InstagramDMTask - INFO - [模拟器4] ✅ V2Ray节点延迟测试成功: 连接成功：延时 598 毫秒
2025-07-28 12:49:44 - InstagramDMTask - INFO - [模拟器4] 等待5秒后进入下一阶段
2025-07-28 12:49:44 - InstagramDMTask - INFO - [模拟器3] 开始启动Instagram应用
2025-07-28 12:49:44 - InstagramDMTask - INFO - [模拟器3] Instagram启动命令执行成功，等待应用加载
2025-07-28 12:49:47 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram应用启动命令执行完成
2025-07-28 12:49:47 - InstagramDMTask - INFO - [模拟器3] Instagram启动检测 第1/5次
2025-07-28 12:49:49 - InstagramDMTask - INFO - [模拟器4] 开始启动Instagram应用
2025-07-28 12:49:49 - InstagramDMTask - INFO - [模拟器4] Instagram启动命令执行成功，等待应用加载
2025-07-28 12:49:52 - InstagramDMTask - INFO - [模拟器3] ✅ 批量验证成功
2025-07-28 12:49:52 - InstagramDMTask - INFO - [模拟器3] ✅ 验证成功
2025-07-28 12:49:52 - InstagramDMTask - INFO - [模拟器3] Instagram页面状态检测结果: 正常-在主页面
2025-07-28 12:49:52 - InstagramDMTask - INFO - [模拟器3] ✅ Instagram已在主页面，可以继续执行任务
2025-07-28 12:49:52 - InstagramDMTask - INFO - [模拟器3] 开始导航到个人主页
2025-07-28 12:49:52 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram应用启动命令执行完成
2025-07-28 12:49:52 - InstagramDMTask - INFO - [模拟器4] Instagram启动检测 第1/5次
2025-07-28 12:49:56 - InstagramDMTask - INFO - [模拟器3] ✅ 个人主页加载完成
2025-07-28 12:49:56 - InstagramDMTask - INFO - [模拟器3] 开始获取粉丝数量信息
2025-07-28 12:49:57 - InstagramDMTask - INFO - [模拟器3] 原始粉丝数文本: 6
2025-07-28 12:49:57 - InstagramDMTask - INFO - [模拟器3] ✅ 粉丝数量验证通过: 6
2025-07-28 12:49:57 - InstagramDMTask - INFO - [模拟器3] 开始打开粉丝列表
2025-07-28 12:50:00 - InstagramDMTask - INFO - [模拟器4] ✅ 批量验证成功
2025-07-28 12:50:00 - InstagramDMTask - INFO - [模拟器4] ✅ 验证成功
2025-07-28 12:50:00 - InstagramDMTask - INFO - [模拟器4] Instagram页面状态检测结果: 正常-在主页面
2025-07-28 12:50:00 - InstagramDMTask - INFO - [模拟器4] ✅ Instagram已在主页面，可以继续执行任务
2025-07-28 12:50:00 - InstagramDMTask - INFO - [模拟器4] 开始导航到个人主页
2025-07-28 12:50:01 - InstagramDMTask - INFO - [模拟器3] ✅ 粉丝列表加载完成
2025-07-28 12:50:01 - InstagramDMTask - INFO - [模拟器3] 开始初始化私信任务
2025-07-28 12:50:01 - InstagramDMTask - INFO - [模拟器3] 已加载 5 条去重记录
2025-07-28 12:50:01 - InstagramDMTask - INFO - [模拟器3] 任务目标数量: 25
2025-07-28 12:50:01 - InstagramDMTask - INFO - [模拟器3] 延迟范围: 2-200ms
2025-07-28 12:50:01 - InstagramDMTask - INFO - [模拟器3] ✅ 私信任务初始化完成
2025-07-28 12:50:01 - InstagramDMTask - INFO - [模拟器3] Instagram任务状态更新信号已发送: 私信中（已发送0/目标25）
2025-07-28 12:50:01 - InstagramDMTask - INFO - [模拟器3] 开始批量私信发送循环
2025-07-28 12:50:01 - InstagramDMTask - INFO - [模拟器3] 初始化循环参数
2025-07-28 12:50:01 - InstagramDMTask - INFO - [模拟器3] 开始主循环，目标数量: 25
2025-07-28 12:50:01 - InstagramDMTask - INFO - [模拟器3] 当前进度: 0/25
2025-07-28 12:50:01 - InstagramDMTask - INFO - [模拟器3] 开始批量获取当前屏幕可见的粉丝列表
2025-07-28 12:50:01 - MainWindowV2 - INFO - 模拟器3的Instagram任务状态已更新: 私信中（已发送0/目标25）
2025-07-28 12:50:03 - InstagramDMTask - INFO - [模拟器3] 找到 6 个粉丝
2025-07-28 12:50:03 - InstagramDMTask - INFO - [模拟器3] ✅ 找到符合条件的粉丝: wavytvfawvwa
2025-07-28 12:50:03 - InstagramDMTask - INFO - [模拟器3] ✅ 找到符合条件的粉丝: wavtvawavtva59
2025-07-28 12:50:03 - InstagramDMTask - INFO - [模拟器3] ✅ 找到符合条件的粉丝: dawvtwavtvadawvt
2025-07-28 12:50:03 - InstagramDMTask - INFO - [模拟器3] ✅ 找到符合条件的粉丝: wavtvawavrav
2025-07-28 12:50:03 - InstagramDMTask - INFO - [模拟器3] ✅ 找到符合条件的粉丝: trawvaawvtva
2025-07-28 12:50:03 - InstagramDMTask - INFO - [模拟器3] ✅ 找到符合条件的粉丝: awdvtvdwahad
2025-07-28 12:50:03 - InstagramDMTask - INFO - [模拟器3] 找到 6 个可见粉丝
2025-07-28 12:50:03 - InstagramDMTask - INFO - [模拟器3] 用户 wavytvfawvwa 已发送过，跳过
2025-07-28 12:50:03 - InstagramDMTask - INFO - [模拟器3] 用户 wavtvawavtva59 已发送过，跳过
2025-07-28 12:50:03 - InstagramDMTask - INFO - [模拟器3] 📤 准备发送私信给: dawvtwavtvadawvt (第1个)
2025-07-28 12:50:03 - InstagramDMTask - INFO - [模拟器3] 开始向 dawvtwavtvadawvt 发送私信
2025-07-28 12:50:03 - InstagramDMTask - INFO - [模拟器3] 点击用户名进入主页
2025-07-28 12:50:03 - InstagramDMTask - INFO - [模拟器3] ✅ 点击用户名成功
2025-07-28 12:50:03 - InstagramDMTask - INFO - [模拟器3] 智能等待发消息按钮出现
2025-07-28 12:50:04 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 12:50:04 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 12:50:04 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 12:50:04 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 12:50:04 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 12:50:04 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 12:50:04 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 12:50:04 - InstagramDMTask - INFO - [模拟器4] ✅ 个人主页加载完成
2025-07-28 12:50:04 - InstagramDMTask - INFO - [模拟器4] 开始获取粉丝数量信息
2025-07-28 12:50:06 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 3.04s, 检测1次
2025-07-28 12:50:06 - InstagramDMTask - INFO - [模拟器3] 撤回功能未启用，跳过撤回步骤
2025-07-28 12:50:06 - InstagramDMTask - INFO - [模拟器3] 智能等待输入框出现
2025-07-28 12:50:06 - InstagramDMTask - INFO - [模拟器4] 原始粉丝数文本: 5
2025-07-28 12:50:06 - InstagramDMTask - INFO - [模拟器4] ✅ 粉丝数量验证通过: 5
2025-07-28 12:50:06 - InstagramDMTask - INFO - [模拟器4] 开始打开粉丝列表
2025-07-28 12:50:07 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.55s, 检测1次
2025-07-28 12:50:08 - InstagramDMTask - INFO - [模拟器3] 智能等待输入框激活
2025-07-28 12:50:09 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.27s, 检测1次
2025-07-28 12:50:09 - InstagramDMTask - INFO - [模拟器3] ✅ 私信内容输入成功
2025-07-28 12:50:09 - InstagramDMTask - INFO - [模拟器3] 等待消息延迟: 100ms
2025-07-28 12:50:11 - InstagramDMTask - INFO - [模拟器4] ✅ 粉丝列表加载完成
2025-07-28 12:50:11 - InstagramDMTask - INFO - [模拟器4] 开始初始化私信任务
2025-07-28 12:50:11 - InstagramDMTask - INFO - [模拟器4] 已加载 5 条去重记录
2025-07-28 12:50:11 - InstagramDMTask - INFO - [模拟器4] 任务目标数量: 25
2025-07-28 12:50:11 - InstagramDMTask - INFO - [模拟器4] 延迟范围: 2-200ms
2025-07-28 12:50:11 - InstagramDMTask - INFO - [模拟器4] ✅ 私信任务初始化完成
2025-07-28 12:50:11 - InstagramDMTask - INFO - [模拟器4] Instagram任务状态更新信号已发送: 私信中（已发送0/目标25）
2025-07-28 12:50:11 - InstagramDMTask - INFO - [模拟器4] 开始批量私信发送循环
2025-07-28 12:50:11 - InstagramDMTask - INFO - [模拟器4] 初始化循环参数
2025-07-28 12:50:11 - InstagramDMTask - INFO - [模拟器4] 开始主循环，目标数量: 25
2025-07-28 12:50:11 - InstagramDMTask - INFO - [模拟器4] 当前进度: 0/25
2025-07-28 12:50:11 - MainWindowV2 - INFO - 模拟器4的Instagram任务状态已更新: 私信中（已发送0/目标25）
2025-07-28 12:50:11 - InstagramDMTask - INFO - [模拟器4] 开始批量获取当前屏幕可见的粉丝列表
2025-07-28 12:50:12 - InstagramDMTask - INFO - [模拟器3] ✅ 找到发送按钮
2025-07-28 12:50:12 - InstagramDMTask - INFO - [模拟器4] 找到 5 个粉丝
2025-07-28 12:50:12 - InstagramDMTask - INFO - [模拟器4] ✅ 找到符合条件的粉丝: sakura.houkann.cocoa
2025-07-28 12:50:12 - InstagramDMTask - INFO - [模拟器4] ✅ 找到符合条件的粉丝: dawvtwavtvadawvt
2025-07-28 12:50:12 - InstagramDMTask - INFO - [模拟器4] ✅ 找到符合条件的粉丝: wavtvawavtva242
2025-07-28 12:50:12 - InstagramDMTask - INFO - [模拟器4] ✅ 找到符合条件的粉丝: awdvtvdwahad
2025-07-28 12:50:12 - InstagramDMTask - INFO - [模拟器4] ✅ 找到符合条件的粉丝: wavtvawavrav
2025-07-28 12:50:12 - InstagramDMTask - INFO - [模拟器4] 找到 5 个可见粉丝
2025-07-28 12:50:12 - InstagramDMTask - INFO - [模拟器4] 📤 准备发送私信给: sakura.houkann.cocoa (第1个)
2025-07-28 12:50:12 - InstagramDMTask - INFO - [模拟器4] 开始向 sakura.houkann.cocoa 发送私信
2025-07-28 12:50:12 - InstagramDMTask - INFO - [模拟器4] 点击用户名进入主页
2025-07-28 12:50:12 - InstagramDMTask - INFO - [模拟器4] ✅ 点击用户名成功
2025-07-28 12:50:12 - InstagramDMTask - INFO - [模拟器4] 智能等待发消息按钮出现
2025-07-28 12:50:12 - InstagramDMTask - INFO - [模拟器3] 开始返回粉丝列表（需要2次返回）
2025-07-28 12:50:12 - InstagramDMTask - INFO - [模拟器3] 第1次返回: 私信界面 → 用户主页
2025-07-28 12:50:14 - InstagramDMTask - INFO - [模拟器3] ✅ 返回操作成功 (title_container)
2025-07-28 12:50:14 - InstagramDMTask - INFO - [模拟器3] 智能等待用户主页加载
2025-07-28 12:50:15 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.21s, 检测1次
2025-07-28 12:50:15 - InstagramDMTask - INFO - [模拟器3] 第2次返回: 用户主页 → 粉丝列表
2025-07-28 12:50:15 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 3.36s, 检测1次
2025-07-28 12:50:16 - InstagramDMTask - INFO - [模拟器4] 撤回功能未启用，跳过撤回步骤
2025-07-28 12:50:16 - InstagramDMTask - INFO - [模拟器4] 智能等待输入框出现
2025-07-28 12:50:17 - InstagramDMTask - INFO - [模拟器3] ✅ 返回操作成功 (back_button)
2025-07-28 12:50:17 - InstagramDMTask - INFO - [模拟器3] 智能等待粉丝列表加载
2025-07-28 12:50:17 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.86s, 检测1次
2025-07-28 12:50:18 - InstagramDMTask - INFO - [模拟器4] 智能等待输入框激活
2025-07-28 12:50:18 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.25s, 检测1次
2025-07-28 12:50:18 - InstagramDMTask - INFO - [模拟器3] ✅ 成功返回粉丝列表
2025-07-28 12:50:18 - InstagramDMTask - INFO - [模拟器3] ✅ 成功向 dawvtwavtvadawvt 发送私信
2025-07-28 12:50:18 - InstagramDMTask - INFO - [模拟器3] ✅ 成功发送第 1 条私信给 dawvtwavtvadawvt
2025-07-28 12:50:18 - InstagramDMTask - INFO - [模拟器3] 已记录用户: dawvtwavtvadawvt
2025-07-28 12:50:18 - InstagramDMTask - INFO - [模拟器3] Instagram任务状态更新信号已发送: 私信中（已发送1/目标25）
2025-07-28 12:50:18 - InstagramDMTask - INFO - [模拟器3] ⏱️ 等待 85ms 后继续下一个用户
2025-07-28 12:50:18 - MainWindowV2 - INFO - 模拟器3的Instagram任务状态已更新: 私信中（已发送1/目标25）
2025-07-28 12:50:18 - InstagramDMTask - INFO - [模拟器3] 📤 准备发送私信给: wavtvawavrav (第2个)
2025-07-28 12:50:18 - InstagramDMTask - INFO - [模拟器3] 开始向 wavtvawavrav 发送私信
2025-07-28 12:50:18 - InstagramDMTask - INFO - [模拟器3] 点击用户名进入主页
2025-07-28 12:50:18 - InstagramDMTask - INFO - [模拟器3] ✅ 点击用户名成功
2025-07-28 12:50:18 - InstagramDMTask - INFO - [模拟器3] 智能等待发消息按钮出现
2025-07-28 12:50:19 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.25s, 检测1次
2025-07-28 12:50:19 - InstagramDMTask - INFO - [模拟器4] ✅ 私信内容输入成功
2025-07-28 12:50:19 - InstagramDMTask - INFO - [模拟器4] 等待消息延迟: 100ms
2025-07-28 12:50:21 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 3.02s, 检测1次
2025-07-28 12:50:21 - InstagramDMTask - INFO - [模拟器4] ✅ 找到发送按钮
2025-07-28 12:50:21 - InstagramDMTask - INFO - [模拟器3] 撤回功能未启用，跳过撤回步骤
2025-07-28 12:50:21 - InstagramDMTask - INFO - [模拟器3] 智能等待输入框出现
2025-07-28 12:50:22 - InstagramDMTask - INFO - [模拟器4] 开始返回粉丝列表（需要2次返回）
2025-07-28 12:50:22 - InstagramDMTask - INFO - [模拟器4] 第1次返回: 私信界面 → 用户主页
2025-07-28 12:50:23 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.69s, 检测1次
2025-07-28 12:50:23 - InstagramDMTask - INFO - [模拟器3] 智能等待输入框激活
2025-07-28 12:50:24 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 12:50:24 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 12:50:24 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 12:50:24 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 12:50:24 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 12:50:24 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 12:50:24 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 12:50:24 - InstagramDMTask - INFO - [模拟器4] ✅ 返回操作成功 (title_container)
2025-07-28 12:50:24 - InstagramDMTask - INFO - [模拟器4] 智能等待用户主页加载
2025-07-28 12:50:25 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.24s, 检测1次
2025-07-28 12:50:25 - InstagramDMTask - INFO - [模拟器3] ✅ 私信内容输入成功
2025-07-28 12:50:25 - InstagramDMTask - INFO - [模拟器3] 等待消息延迟: 100ms
2025-07-28 12:50:25 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.25s, 检测1次
2025-07-28 12:50:25 - InstagramDMTask - INFO - [模拟器4] 第2次返回: 用户主页 → 粉丝列表
2025-07-28 12:50:27 - InstagramDMTask - INFO - [模拟器4] ✅ 返回操作成功 (back_button)
2025-07-28 12:50:27 - InstagramDMTask - INFO - [模拟器4] 智能等待粉丝列表加载
2025-07-28 12:50:27 - InstagramDMTask - INFO - [模拟器3] ✅ 找到发送按钮
2025-07-28 12:50:28 - InstagramDMTask - INFO - [模拟器3] 开始返回粉丝列表（需要2次返回）
2025-07-28 12:50:28 - InstagramDMTask - INFO - [模拟器3] 第1次返回: 私信界面 → 用户主页
2025-07-28 12:50:28 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.37s, 检测1次
2025-07-28 12:50:28 - InstagramDMTask - INFO - [模拟器4] ✅ 成功返回粉丝列表
2025-07-28 12:50:28 - InstagramDMTask - INFO - [模拟器4] ✅ 成功向 sakura.houkann.cocoa 发送私信
2025-07-28 12:50:28 - InstagramDMTask - INFO - [模拟器4] ✅ 成功发送第 1 条私信给 sakura.houkann.cocoa
2025-07-28 12:50:28 - InstagramDMTask - INFO - [模拟器4] 已记录用户: sakura.houkann.cocoa
2025-07-28 12:50:28 - InstagramDMTask - INFO - [模拟器4] Instagram任务状态更新信号已发送: 私信中（已发送1/目标25）
2025-07-28 12:50:28 - InstagramDMTask - INFO - [模拟器4] ⏱️ 等待 64ms 后继续下一个用户
2025-07-28 12:50:28 - MainWindowV2 - INFO - 模拟器4的Instagram任务状态已更新: 私信中（已发送1/目标25）
2025-07-28 12:50:28 - InstagramDMTask - INFO - [模拟器4] 📤 准备发送私信给: dawvtwavtvadawvt (第2个)
2025-07-28 12:50:28 - InstagramDMTask - INFO - [模拟器4] 开始向 dawvtwavtvadawvt 发送私信
2025-07-28 12:50:28 - InstagramDMTask - INFO - [模拟器4] 点击用户名进入主页
2025-07-28 12:50:28 - InstagramDMTask - INFO - [模拟器4] ✅ 点击用户名成功
2025-07-28 12:50:28 - InstagramDMTask - INFO - [模拟器4] 智能等待发消息按钮出现
2025-07-28 12:50:31 - InstagramDMTask - INFO - [模拟器3] ✅ 返回操作成功 (title_container)
2025-07-28 12:50:31 - InstagramDMTask - INFO - [模拟器3] 智能等待用户主页加载
2025-07-28 12:50:32 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 3.27s, 检测1次
2025-07-28 12:50:32 - InstagramDMTask - INFO - [模拟器4] 撤回功能未启用，跳过撤回步骤
2025-07-28 12:50:32 - InstagramDMTask - INFO - [模拟器4] 智能等待输入框出现
2025-07-28 12:50:32 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.22s, 检测1次
2025-07-28 12:50:32 - InstagramDMTask - INFO - [模拟器3] 第2次返回: 用户主页 → 粉丝列表
2025-07-28 12:50:34 - InstagramDMTask - INFO - [模拟器3] ✅ 返回操作成功 (back_button)
2025-07-28 12:50:34 - InstagramDMTask - INFO - [模拟器3] 智能等待粉丝列表加载
2025-07-28 12:50:34 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 2.52s, 检测1次
2025-07-28 12:50:34 - InstagramDMTask - INFO - [模拟器4] 智能等待输入框激活
2025-07-28 12:50:35 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.28s, 检测1次
2025-07-28 12:50:35 - InstagramDMTask - INFO - [模拟器3] ✅ 成功返回粉丝列表
2025-07-28 12:50:35 - InstagramDMTask - INFO - [模拟器3] ✅ 成功向 wavtvawavrav 发送私信
2025-07-28 12:50:35 - InstagramDMTask - INFO - [模拟器3] ✅ 成功发送第 2 条私信给 wavtvawavrav
2025-07-28 12:50:35 - InstagramDMTask - INFO - [模拟器3] 已记录用户: wavtvawavrav
2025-07-28 12:50:35 - InstagramDMTask - INFO - [模拟器3] Instagram任务状态更新信号已发送: 私信中（已发送2/目标25）
2025-07-28 12:50:35 - InstagramDMTask - INFO - [模拟器3] ⏱️ 等待 71ms 后继续下一个用户
2025-07-28 12:50:35 - MainWindowV2 - INFO - 模拟器3的Instagram任务状态已更新: 私信中（已发送2/目标25）
2025-07-28 12:50:35 - InstagramDMTask - INFO - [模拟器3] 📤 准备发送私信给: trawvaawvtva (第3个)
2025-07-28 12:50:35 - InstagramDMTask - INFO - [模拟器3] 开始向 trawvaawvtva 发送私信
2025-07-28 12:50:35 - InstagramDMTask - INFO - [模拟器3] 点击用户名进入主页
2025-07-28 12:50:35 - InstagramDMTask - INFO - [模拟器3] ✅ 点击用户名成功
2025-07-28 12:50:35 - InstagramDMTask - INFO - [模拟器3] 智能等待发消息按钮出现
2025-07-28 12:50:36 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.21s, 检测1次
2025-07-28 12:50:36 - InstagramDMTask - INFO - [模拟器4] ✅ 私信内容输入成功
2025-07-28 12:50:36 - InstagramDMTask - INFO - [模拟器4] 等待消息延迟: 100ms
2025-07-28 12:50:38 - InstagramDMTask - INFO - [模拟器4] ✅ 找到发送按钮
2025-07-28 12:50:39 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 3.40s, 检测1次
2025-07-28 12:50:39 - InstagramDMTask - INFO - [模拟器4] 开始返回粉丝列表（需要2次返回）
2025-07-28 12:50:39 - InstagramDMTask - INFO - [模拟器4] 第1次返回: 私信界面 → 用户主页
2025-07-28 12:50:39 - InstagramDMTask - INFO - [模拟器3] 撤回功能未启用，跳过撤回步骤
2025-07-28 12:50:39 - InstagramDMTask - INFO - [模拟器3] 智能等待输入框出现
2025-07-28 12:50:40 - InstagramDMTask - INFO - [模拟器4] ✅ 返回操作成功 (title_container)
2025-07-28 12:50:40 - InstagramDMTask - INFO - [模拟器4] 智能等待用户主页加载
2025-07-28 12:50:41 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.61s, 检测1次
2025-07-28 12:50:41 - InstagramDMTask - INFO - [模拟器3] 智能等待输入框激活
2025-07-28 12:50:42 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.24s, 检测1次
2025-07-28 12:50:42 - InstagramDMTask - INFO - [模拟器4] 第2次返回: 用户主页 → 粉丝列表
2025-07-28 12:50:42 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.26s, 检测1次
2025-07-28 12:50:42 - InstagramDMTask - INFO - [模拟器3] ✅ 私信内容输入成功
2025-07-28 12:50:42 - InstagramDMTask - INFO - [模拟器3] 等待消息延迟: 100ms
2025-07-28 12:50:43 - InstagramDMTask - INFO - [模拟器4] ✅ 返回操作成功 (back_button)
2025-07-28 12:50:43 - InstagramDMTask - INFO - [模拟器4] 智能等待粉丝列表加载
2025-07-28 12:50:44 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 12:50:44 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 12:50:44 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 12:50:44 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 12:50:44 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 12:50:44 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 12:50:44 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 12:50:44 - InstagramDMTask - INFO - [模拟器3] ✅ 找到发送按钮
2025-07-28 12:50:45 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.27s, 检测1次
2025-07-28 12:50:45 - InstagramDMTask - INFO - [模拟器4] ✅ 成功返回粉丝列表
2025-07-28 12:50:45 - InstagramDMTask - INFO - [模拟器4] ✅ 成功向 dawvtwavtvadawvt 发送私信
2025-07-28 12:50:45 - InstagramDMTask - INFO - [模拟器4] ✅ 成功发送第 2 条私信给 dawvtwavtvadawvt
2025-07-28 12:50:45 - InstagramDMTask - INFO - [模拟器4] 已记录用户: dawvtwavtvadawvt
2025-07-28 12:50:45 - InstagramDMTask - INFO - [模拟器4] Instagram任务状态更新信号已发送: 私信中（已发送2/目标25）
2025-07-28 12:50:45 - InstagramDMTask - INFO - [模拟器4] ⏱️ 等待 183ms 后继续下一个用户
2025-07-28 12:50:45 - MainWindowV2 - INFO - 模拟器4的Instagram任务状态已更新: 私信中（已发送2/目标25）
2025-07-28 12:50:45 - InstagramDMTask - INFO - [模拟器4] 📤 准备发送私信给: wavtvawavtva242 (第3个)
2025-07-28 12:50:45 - InstagramDMTask - INFO - [模拟器4] 开始向 wavtvawavtva242 发送私信
2025-07-28 12:50:45 - InstagramDMTask - INFO - [模拟器4] 点击用户名进入主页
2025-07-28 12:50:45 - InstagramDMTask - INFO - [模拟器4] ✅ 点击用户名成功
2025-07-28 12:50:45 - InstagramDMTask - INFO - [模拟器4] 智能等待发消息按钮出现
2025-07-28 12:50:45 - InstagramDMTask - INFO - [模拟器3] 开始返回粉丝列表（需要2次返回）
2025-07-28 12:50:45 - InstagramDMTask - INFO - [模拟器3] 第1次返回: 私信界面 → 用户主页
2025-07-28 12:50:47 - InstagramDMTask - INFO - [模拟器3] ✅ 返回操作成功 (title_container)
2025-07-28 12:50:47 - InstagramDMTask - INFO - [模拟器3] 智能等待用户主页加载
2025-07-28 12:50:48 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.24s, 检测1次
2025-07-28 12:50:48 - InstagramDMTask - INFO - [模拟器3] 第2次返回: 用户主页 → 粉丝列表
2025-07-28 12:50:48 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 3.17s, 检测1次
2025-07-28 12:50:48 - InstagramDMTask - INFO - [模拟器4] 撤回功能未启用，跳过撤回步骤
2025-07-28 12:50:48 - InstagramDMTask - INFO - [模拟器4] 智能等待输入框出现
2025-07-28 12:50:50 - InstagramDMTask - INFO - [模拟器3] ✅ 返回操作成功 (back_button)
2025-07-28 12:50:50 - InstagramDMTask - INFO - [模拟器3] 智能等待粉丝列表加载
2025-07-28 12:50:51 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.29s, 检测1次
2025-07-28 12:50:51 - InstagramDMTask - INFO - [模拟器3] ✅ 成功返回粉丝列表
2025-07-28 12:50:51 - InstagramDMTask - INFO - [模拟器3] ✅ 成功向 trawvaawvtva 发送私信
2025-07-28 12:50:51 - InstagramDMTask - INFO - [模拟器3] ✅ 成功发送第 3 条私信给 trawvaawvtva
2025-07-28 12:50:51 - InstagramDMTask - INFO - [模拟器3] 已记录用户: trawvaawvtva
2025-07-28 12:50:51 - InstagramDMTask - INFO - [模拟器3] Instagram任务状态更新信号已发送: 私信中（已发送3/目标25）
2025-07-28 12:50:51 - InstagramDMTask - INFO - [模拟器3] ⏱️ 等待 29ms 后继续下一个用户
2025-07-28 12:50:51 - MainWindowV2 - INFO - 模拟器3的Instagram任务状态已更新: 私信中（已发送3/目标25）
2025-07-28 12:50:51 - InstagramDMTask - INFO - [模拟器3] 📤 准备发送私信给: awdvtvdwahad (第4个)
2025-07-28 12:50:51 - InstagramDMTask - INFO - [模拟器3] 开始向 awdvtvdwahad 发送私信
2025-07-28 12:50:51 - InstagramDMTask - INFO - [模拟器3] 点击用户名进入主页
2025-07-28 12:50:51 - InstagramDMTask - INFO - [模拟器3] ✅ 点击用户名成功
2025-07-28 12:50:51 - InstagramDMTask - INFO - [模拟器3] 智能等待发消息按钮出现
2025-07-28 12:50:52 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 3.33s, 检测1次
2025-07-28 12:50:52 - InstagramDMTask - INFO - [模拟器4] 智能等待输入框激活
2025-07-28 12:50:53 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.23s, 检测1次
2025-07-28 12:50:53 - InstagramDMTask - INFO - [模拟器4] ✅ 私信内容输入成功
2025-07-28 12:50:53 - InstagramDMTask - INFO - [模拟器4] 等待消息延迟: 100ms
2025-07-28 12:50:54 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 3.28s, 检测1次
2025-07-28 12:50:55 - InstagramDMTask - INFO - [模拟器3] 撤回功能未启用，跳过撤回步骤
2025-07-28 12:50:55 - InstagramDMTask - INFO - [模拟器3] 智能等待输入框出现
2025-07-28 12:50:56 - InstagramDMTask - INFO - [模拟器4] ✅ 找到发送按钮
2025-07-28 12:50:56 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.37s, 检测1次
2025-07-28 12:50:56 - InstagramDMTask - INFO - [模拟器3] 智能等待输入框激活
2025-07-28 12:50:56 - InstagramDMTask - INFO - [模拟器4] 开始返回粉丝列表（需要2次返回）
2025-07-28 12:50:56 - InstagramDMTask - INFO - [模拟器4] 第1次返回: 私信界面 → 用户主页
2025-07-28 12:50:57 - InstagramDMTask - INFO - [模拟器3] ⚡ 快速检测成功: 1.21s, 检测1次
2025-07-28 12:50:57 - InstagramDMTask - INFO - [模拟器3] ✅ 私信内容输入成功
2025-07-28 12:50:57 - InstagramDMTask - INFO - [模拟器3] 等待消息延迟: 100ms
2025-07-28 12:50:58 - InstagramDMTask - INFO - [模拟器4] ✅ 返回操作成功 (title_container)
2025-07-28 12:50:58 - InstagramDMTask - INFO - [模拟器4] 智能等待用户主页加载
2025-07-28 12:50:59 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.21s, 检测1次
2025-07-28 12:50:59 - InstagramDMTask - INFO - [模拟器4] 第2次返回: 用户主页 → 粉丝列表
2025-07-28 12:51:01 - InstagramDMTask - INFO - [模拟器3] ✅ 找到发送按钮
2025-07-28 12:51:01 - InstagramDMTask - INFO - [模拟器4] ✅ 返回操作成功 (back_button)
2025-07-28 12:51:01 - InstagramDMTask - INFO - [模拟器4] 智能等待粉丝列表加载
2025-07-28 12:51:01 - InstagramDMTask - INFO - [模拟器3] 开始返回粉丝列表（需要2次返回）
2025-07-28 12:51:01 - InstagramDMTask - INFO - [模拟器3] 第1次返回: 私信界面 → 用户主页
2025-07-28 12:51:02 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.26s, 检测1次
2025-07-28 12:51:02 - InstagramDMTask - INFO - [模拟器4] ✅ 成功返回粉丝列表
2025-07-28 12:51:02 - InstagramDMTask - INFO - [模拟器4] ✅ 成功向 wavtvawavtva242 发送私信
2025-07-28 12:51:02 - InstagramDMTask - INFO - [模拟器4] ✅ 成功发送第 3 条私信给 wavtvawavtva242
2025-07-28 12:51:02 - InstagramDMTask - INFO - [模拟器4] 已记录用户: wavtvawavtva242
2025-07-28 12:51:02 - InstagramDMTask - INFO - [模拟器4] Instagram任务状态更新信号已发送: 私信中（已发送3/目标25）
2025-07-28 12:51:02 - InstagramDMTask - INFO - [模拟器4] ⏱️ 等待 76ms 后继续下一个用户
2025-07-28 12:51:02 - MainWindowV2 - INFO - 模拟器4的Instagram任务状态已更新: 私信中（已发送3/目标25）
2025-07-28 12:51:02 - InstagramDMTask - INFO - [模拟器4] 📤 准备发送私信给: awdvtvdwahad (第4个)
2025-07-28 12:51:02 - InstagramDMTask - INFO - [模拟器4] 开始向 awdvtvdwahad 发送私信
2025-07-28 12:51:02 - InstagramDMTask - INFO - [模拟器4] 点击用户名进入主页
2025-07-28 12:51:02 - InstagramDMTask - INFO - [模拟器4] ✅ 点击用户名成功
2025-07-28 12:51:02 - InstagramDMTask - INFO - [模拟器4] 智能等待发消息按钮出现
2025-07-28 12:51:04 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 12:51:04 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 12:51:04 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 12:51:04 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 12:51:04 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 12:51:04 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 12:51:04 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 12:51:05 - InstagramDMTask - INFO - [模拟器3] ✅ 返回操作成功 (title_container)
2025-07-28 12:51:05 - InstagramDMTask - INFO - [模拟器3] 智能等待用户主页加载
2025-07-28 12:51:05 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 3.14s, 检测1次
2025-07-28 12:51:06 - InstagramDMTask - INFO - [模拟器4] 撤回功能未启用，跳过撤回步骤
2025-07-28 12:51:06 - InstagramDMTask - INFO - [模拟器4] 智能等待输入框出现
2025-07-28 12:51:06 - InstagramDMTask - WARNING - [模拟器3] 快速检测超时: 1s, 共检测1次
2025-07-28 12:51:06 - InstagramDMTask - WARNING - [模拟器3] 用户主页加载检测超时，继续执行
2025-07-28 12:51:06 - InstagramDMTask - INFO - [模拟器3] 第2次返回: 用户主页 → 粉丝列表
2025-07-28 12:51:08 - InstagramDMTask - INFO - [模拟器3] ✅ 返回操作成功 (back_button)
2025-07-28 12:51:08 - InstagramDMTask - INFO - [模拟器3] 智能等待粉丝列表加载
2025-07-28 12:51:08 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 2.03s, 检测1次
2025-07-28 12:51:08 - InstagramDMTask - INFO - [模拟器4] 智能等待输入框激活
2025-07-28 12:51:09 - InstagramDMTask - WARNING - [模拟器3] 快速检测超时: 1s, 共检测1次
2025-07-28 12:51:09 - InstagramDMTask - WARNING - [模拟器3] 粉丝列表加载检测超时，继续执行
2025-07-28 12:51:09 - InstagramDMTask - INFO - [模拟器3] ✅ 成功返回粉丝列表
2025-07-28 12:51:09 - InstagramDMTask - INFO - [模拟器3] ✅ 成功向 awdvtvdwahad 发送私信
2025-07-28 12:51:09 - InstagramDMTask - INFO - [模拟器3] ✅ 成功发送第 4 条私信给 awdvtvdwahad
2025-07-28 12:51:09 - InstagramDMTask - INFO - [模拟器3] 已记录用户: awdvtvdwahad
2025-07-28 12:51:09 - InstagramDMTask - INFO - [模拟器3] Instagram任务状态更新信号已发送: 私信中（已发送4/目标25）
2025-07-28 12:51:09 - InstagramDMTask - INFO - [模拟器3] ⏱️ 等待 47ms 后继续下一个用户
2025-07-28 12:51:09 - MainWindowV2 - INFO - 模拟器3的Instagram任务状态已更新: 私信中（已发送4/目标25）
2025-07-28 12:51:09 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.27s, 检测1次
2025-07-28 12:51:09 - InstagramDMTask - INFO - [模拟器4] ✅ 私信内容输入成功
2025-07-28 12:51:09 - InstagramDMTask - INFO - [模拟器4] 等待消息延迟: 100ms
2025-07-28 12:51:12 - InstagramDMTask - INFO - [模拟器4] ✅ 找到发送按钮
2025-07-28 12:51:12 - InstagramDMTask - INFO - [模拟器3] 滚动粉丝列表
2025-07-28 12:51:12 - InstagramDMTask - INFO - [模拟器4] 开始返回粉丝列表（需要2次返回）
2025-07-28 12:51:12 - InstagramDMTask - INFO - [模拟器4] 第1次返回: 私信界面 → 用户主页
2025-07-28 12:51:13 - InstagramDMTask - INFO - [模拟器3] 滑动4个容器中的3个位置，距离210px
2025-07-28 12:51:14 - InstagramDMTask - INFO - [模拟器4] ✅ 返回操作成功 (title_container)
2025-07-28 12:51:14 - InstagramDMTask - INFO - [模拟器4] 智能等待用户主页加载
2025-07-28 12:51:15 - InstagramDMTask - INFO - [模拟器3] 当前进度: 4/25
2025-07-28 12:51:15 - InstagramDMTask - INFO - [模拟器3] 开始批量获取当前屏幕可见的粉丝列表
2025-07-28 12:51:15 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.21s, 检测1次
2025-07-28 12:51:15 - InstagramDMTask - INFO - [模拟器4] 第2次返回: 用户主页 → 粉丝列表
2025-07-28 12:51:16 - InstagramDMTask - INFO - [模拟器3] 找到 0 个粉丝
2025-07-28 12:51:16 - InstagramDMTask - WARNING - [模拟器3] 未找到粉丝，重试第1次
2025-07-28 12:51:16 - InstagramDMTask - INFO - [模拟器3] 滚动粉丝列表
2025-07-28 12:51:17 - InstagramDMTask - INFO - [模拟器4] ✅ 返回操作成功 (back_button)
2025-07-28 12:51:17 - InstagramDMTask - INFO - [模拟器4] 智能等待粉丝列表加载
2025-07-28 12:51:17 - InstagramDMTask - INFO - [模拟器3] 滑动4个容器中的3个位置，距离210px
2025-07-28 12:51:18 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.26s, 检测1次
2025-07-28 12:51:18 - InstagramDMTask - INFO - [模拟器4] ✅ 成功返回粉丝列表
2025-07-28 12:51:18 - InstagramDMTask - INFO - [模拟器4] ✅ 成功向 awdvtvdwahad 发送私信
2025-07-28 12:51:18 - InstagramDMTask - INFO - [模拟器4] ✅ 成功发送第 4 条私信给 awdvtvdwahad
2025-07-28 12:51:18 - InstagramDMTask - INFO - [模拟器4] 已记录用户: awdvtvdwahad
2025-07-28 12:51:18 - InstagramDMTask - INFO - [模拟器4] Instagram任务状态更新信号已发送: 私信中（已发送4/目标25）
2025-07-28 12:51:18 - InstagramDMTask - INFO - [模拟器4] ⏱️ 等待 146ms 后继续下一个用户
2025-07-28 12:51:18 - MainWindowV2 - INFO - 模拟器4的Instagram任务状态已更新: 私信中（已发送4/目标25）
2025-07-28 12:51:18 - InstagramDMTask - INFO - [模拟器4] 📤 准备发送私信给: wavtvawavrav (第5个)
2025-07-28 12:51:18 - InstagramDMTask - INFO - [模拟器4] 开始向 wavtvawavrav 发送私信
2025-07-28 12:51:18 - InstagramDMTask - INFO - [模拟器4] 点击用户名进入主页
2025-07-28 12:51:18 - InstagramDMTask - INFO - [模拟器4] ✅ 点击用户名成功
2025-07-28 12:51:18 - InstagramDMTask - INFO - [模拟器4] 智能等待发消息按钮出现
2025-07-28 12:51:19 - InstagramDMTask - INFO - [模拟器3] 当前进度: 4/25
2025-07-28 12:51:19 - InstagramDMTask - INFO - [模拟器3] 开始批量获取当前屏幕可见的粉丝列表
2025-07-28 12:51:20 - InstagramDMTask - INFO - [模拟器3] 找到 0 个粉丝
2025-07-28 12:51:20 - InstagramDMTask - WARNING - [模拟器3] 未找到粉丝，重试第2次
2025-07-28 12:51:20 - InstagramDMTask - INFO - [模拟器3] 滚动粉丝列表
2025-07-28 12:51:21 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 3.11s, 检测1次
2025-07-28 12:51:22 - InstagramDMTask - INFO - [模拟器3] 滑动4个容器中的3个位置，距离210px
2025-07-28 12:51:22 - InstagramDMTask - INFO - [模拟器4] 撤回功能未启用，跳过撤回步骤
2025-07-28 12:51:22 - InstagramDMTask - INFO - [模拟器4] 智能等待输入框出现
2025-07-28 12:51:23 - InstagramDMTask - INFO - [模拟器3] 当前进度: 4/25
2025-07-28 12:51:23 - InstagramDMTask - INFO - [模拟器3] 开始批量获取当前屏幕可见的粉丝列表
2025-07-28 12:51:23 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.79s, 检测1次
2025-07-28 12:51:24 - InstagramDMTask - INFO - [模拟器4] 智能等待输入框激活
2025-07-28 12:51:24 - TaskActivityHeartbeatManager - INFO - 开始检测 2 个模拟器的心跳状态
2025-07-28 12:51:24 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 12:51:24 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 12:51:24 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 12:51:24 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 4
2025-07-28 12:51:24 - MainWindowV2 - WARNING - 模拟器4心跳状态更新未产生变化
2025-07-28 12:51:24 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 12:51:24 - InstagramDMTask - INFO - [模拟器3] 找到 0 个粉丝
2025-07-28 12:51:24 - InstagramDMTask - WARNING - [模拟器3] 未找到粉丝，重试第3次
2025-07-28 12:51:24 - InstagramDMTask - INFO - [模拟器3] 滚动粉丝列表
2025-07-28 12:51:25 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.22s, 检测1次
2025-07-28 12:51:25 - InstagramDMTask - INFO - [模拟器4] ✅ 私信内容输入成功
2025-07-28 12:51:25 - InstagramDMTask - INFO - [模拟器4] 等待消息延迟: 100ms
2025-07-28 12:51:26 - InstagramDMTask - INFO - [模拟器3] 滑动4个容器中的3个位置，距离210px
2025-07-28 12:51:27 - InstagramDMTask - INFO - [模拟器3] 当前进度: 4/25
2025-07-28 12:51:27 - InstagramDMTask - INFO - [模拟器3] 开始批量获取当前屏幕可见的粉丝列表
2025-07-28 12:51:27 - InstagramDMTask - INFO - [模拟器4] ✅ 找到发送按钮
2025-07-28 12:51:28 - InstagramDMTask - INFO - [模拟器4] 开始返回粉丝列表（需要2次返回）
2025-07-28 12:51:28 - InstagramDMTask - INFO - [模拟器4] 第1次返回: 私信界面 → 用户主页
2025-07-28 12:51:29 - InstagramDMTask - INFO - [模拟器3] 找到 0 个粉丝
2025-07-28 12:51:29 - InstagramDMTask - ERROR - [模拟器3] 连续3次未找到粉丝，停止任务
2025-07-28 12:51:29 - InstagramDMTask - INFO - [模拟器3] ✅ 批量私信发送完成，共发送 4 条私信
2025-07-28 12:51:29 - InstagramDMTask - INFO - [模拟器3] Instagram任务状态更新信号已发送: 私信中（已发送4/目标25）
2025-07-28 12:51:29 - InstagramDMTask - INFO - [模拟器3] Instagram任务配置观察者已注销
2025-07-28 12:51:29 - InstagramDMTask - INFO - [模拟器3] 开始任务完成后清理工作
2025-07-28 12:51:29 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已从任务活动监控移除
2025-07-28 12:51:29 - InstagramDMTask - INFO - [模拟器3] 心跳监控已移除
2025-07-28 12:51:29 - InstagramDMTask - INFO - [模拟器3] 准备关闭模拟器
2025-07-28 12:51:29 - MainWindowV2 - WARNING - 模拟器3的Instagram任务状态更新失败
2025-07-28 12:51:29 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 3 | remaining_running: 1
2025-07-28 12:51:29 - Emulator - INFO - 模拟器停止成功 | emulator_id: 3
2025-07-28 12:51:29 - InstagramDMTask - INFO - [模拟器3] 模拟器已成功关闭
2025-07-28 12:51:29 - InstagramDMTask - INFO - [模拟器3] 任务完成后清理工作完成
2025-07-28 12:51:29 - InstagramTaskThread - INFO - [模拟器3] Instagram任务执行成功: Instagram私信任务执行完成
2025-07-28 12:51:29 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务stop
2025-07-28 12:51:29 - MainWindowV2 - INFO - 模拟器3停止成功
2025-07-28 12:51:29 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 12:51:29 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 12:51:29 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 排队中 -> 启动中
2025-07-28 12:51:29 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 排队中 | new_state: 启动中
2025-07-28 12:51:29 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 5
2025-07-28 12:51:29 - MainWindowV2 - INFO - 模拟器5: 排队中 -> 启动中
2025-07-28 12:51:29 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 12:51:29 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/2)
2025-07-28 12:51:29 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 5
2025-07-28 12:51:30 - InstagramDMTask - INFO - [模拟器4] ✅ 返回操作成功 (title_container)
2025-07-28 12:51:30 - InstagramDMTask - INFO - [模拟器4] 智能等待用户主页加载
2025-07-28 12:51:31 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.23s, 检测1次
2025-07-28 12:51:31 - InstagramDMTask - INFO - [模拟器4] 第2次返回: 用户主页 → 粉丝列表
2025-07-28 12:51:33 - InstagramDMTask - INFO - [模拟器4] ✅ 返回操作成功 (back_button)
2025-07-28 12:51:33 - InstagramDMTask - INFO - [模拟器4] 智能等待粉丝列表加载
2025-07-28 12:51:34 - InstagramDMTask - INFO - [模拟器4] ⚡ 快速检测成功: 1.32s, 检测1次
2025-07-28 12:51:34 - InstagramDMTask - INFO - [模拟器4] ✅ 成功返回粉丝列表
2025-07-28 12:51:34 - InstagramDMTask - INFO - [模拟器4] ✅ 成功向 wavtvawavrav 发送私信
2025-07-28 12:51:34 - InstagramDMTask - INFO - [模拟器4] ✅ 成功发送第 5 条私信给 wavtvawavrav
2025-07-28 12:51:34 - InstagramDMTask - INFO - [模拟器4] 已记录用户: wavtvawavrav
2025-07-28 12:51:34 - InstagramDMTask - INFO - [模拟器4] Instagram任务状态更新信号已发送: 私信中（已发送5/目标25）
2025-07-28 12:51:34 - InstagramDMTask - INFO - [模拟器4] ⏱️ 等待 96ms 后继续下一个用户
2025-07-28 12:51:34 - MainWindowV2 - INFO - 模拟器4的Instagram任务状态已更新: 私信中（已发送5/目标25）
2025-07-28 12:51:37 - InstagramDMTask - INFO - [模拟器4] 已到达粉丝列表底部
2025-07-28 12:51:37 - InstagramDMTask - INFO - [模拟器4] 已到达粉丝列表底部
2025-07-28 12:51:37 - InstagramDMTask - INFO - [模拟器4] ✅ 批量私信发送完成，共发送 5 条私信
2025-07-28 12:51:37 - InstagramDMTask - INFO - [模拟器4] Instagram任务状态更新信号已发送: 私信中（已发送5/目标25）
2025-07-28 12:51:37 - InstagramDMTask - INFO - [模拟器4] Instagram任务配置观察者已注销
2025-07-28 12:51:37 - InstagramDMTask - INFO - [模拟器4] 开始任务完成后清理工作
2025-07-28 12:51:37 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已从任务活动监控移除
2025-07-28 12:51:37 - InstagramDMTask - INFO - [模拟器4] 心跳监控已移除
2025-07-28 12:51:37 - InstagramDMTask - INFO - [模拟器4] 准备关闭模拟器
2025-07-28 12:51:37 - MainWindowV2 - WARNING - 模拟器4的Instagram任务状态更新失败
2025-07-28 12:51:37 - StartupManager - INFO - 释放模拟器槽位 | emulator_id: 4 | remaining_running: 0
2025-07-28 12:51:37 - Emulator - INFO - 模拟器停止成功 | emulator_id: 4
2025-07-28 12:51:37 - InstagramDMTask - INFO - [模拟器4] 模拟器已成功关闭
2025-07-28 12:51:37 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务stop
2025-07-28 12:51:37 - InstagramDMTask - INFO - [模拟器4] 任务完成后清理工作完成
2025-07-28 12:51:37 - MainWindowV2 - INFO - 模拟器4停止成功
2025-07-28 12:51:37 - InstagramTaskThread - INFO - [模拟器4] Instagram任务执行成功: Instagram私信任务执行完成
2025-07-28 12:51:37 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=stop, 结果={'result': '停止成功', 'message': ''}
2025-07-28 12:51:37 - WindowArrangementManager - INFO - 跳过非启动任务或失败任务: 任务类型=stop, 结果=停止成功
2025-07-28 12:51:39 - Emulator - INFO - Android系统启动完成 | emulator_id: 5 | elapsed_time: 10.0秒
2025-07-28 12:51:39 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器5状态变化: 启动中 -> 运行中
2025-07-28 12:51:39 - InstagramTaskManager - INFO - 启动模拟器5的Instagram私信任务线程 - 当前并发: 3/2
2025-07-28 12:51:39 - Emulator - INFO - 模拟器状态变化 | emulator_id: 5 | old_state: 启动中 | new_state: 运行中
2025-07-28 12:51:39 - Emulator - INFO - 模拟器启动成功 | emulator_id: 5 | running_count: 1
2025-07-28 12:51:39 - TaskActivityHeartbeatManager - INFO - 模拟器 5 已添加到任务活动监控，失败计数: 0
2025-07-28 12:51:39 - MainWindowV2 - INFO - 任务完成: 模拟器5, 任务start
2025-07-28 12:51:39 - MainWindowV2 - INFO - 模拟器5启动成功
2025-07-28 12:51:39 - InstagramTaskThread - INFO - [模拟器5] 开始等待启动完成
2025-07-28 12:51:39 - InstagramTaskThread - INFO - [模拟器5] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 12:51:39 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=5, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 12:51:39 - InstagramTaskThread - INFO - [模拟器5] 开始窗口排列
2025-07-28 12:51:39 - WindowArrangementManager - INFO - 模拟器5启动完成，立即触发窗口排列
2025-07-28 12:51:39 - MainWindowV2 - WARNING - 未找到模拟器5，无法更新状态
2025-07-28 12:51:39 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 12:51:39 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 0 | starting: 0 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 1 | max_concurrent: 2
2025-07-28 12:51:39 - MainWindowV2 - INFO - 启动进度: 排队0个, 启动中0个, 运行1个, 失败0个 (并发槽位:1/2)
2025-07-28 12:51:39 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中, PID: 22512
2025-07-28 12:51:39 - MainWindowV2 - INFO - 模拟器5: 启动中 -> 运行中
2025-07-28 12:51:40 - StartupManager - INFO - 调度器已停止
2025-07-28 12:51:41 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 12:51:41 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 12:51:41 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 12:51:41 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 12:51:41 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 12:51:41 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 12:51:41 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 12:51:41 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 12:51:41 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 12:51:41 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-5' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 12:51:41 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 12:51:41 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 12:51:44 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 12:51:44 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 12:51:44 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 5
2025-07-28 12:51:44 - MainWindowV2 - WARNING - 模拟器5心跳状态更新未产生变化
2025-07-28 12:51:44 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 12:51:45 - InstagramDMUI - INFO - 刷新Instagram私信任务状态
2025-07-28 12:51:47 - InstagramTaskThread - INFO - [模拟器5] 窗口排列完成
2025-07-28 12:51:47 - InstagramTaskThread - INFO - [模拟器5] 开始执行Instagram私信任务
2025-07-28 12:51:47 - InstagramDMTask - INFO - [模拟器5] 雷电模拟器API初始化成功
2025-07-28 12:51:47 - InstagramDMTask - INFO - [模拟器5] 模拟器路径: G:/leidian/LDPlayer9
2025-07-28 12:51:47 - InstagramDMTask - INFO - [模拟器5] 共享路径: C:\Users\<USER>\Documents\leidian9\Pictures
2025-07-28 12:51:47 - InstagramDMTask - INFO - [模拟器5] 已设置ld.emulator_id = 5
2025-07-28 12:51:47 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置加载完成: 私信数量=25, 延迟=2-200ms
2025-07-28 12:51:47 - InstagramDMTask - INFO - [模拟器5] Instagram任务配置热加载观察者已注册
2025-07-28 12:51:47 - InstagramDMTask - INFO - [模拟器5] Instagram私信任务执行器初始化完成
2025-07-28 12:51:47 - InstagramTaskThread - INFO - [模拟器5] 任务超时计时已从线程启动开始: 666秒
2025-07-28 12:51:47 - InstagramDMTask - INFO - [模拟器5] 开始执行Instagram私信任务
2025-07-28 12:51:47 - InstagramDMTask - INFO - [模拟器5] 任务超时设置: 666秒，已运行: 8.01秒
2025-07-28 12:51:47 - InstagramDMTask - INFO - [模拟器5] 模拟器Android系统运行正常，桌面稳定
2025-07-28 12:51:47 - InstagramDMTask - INFO - [模拟器5] 开始检查应用安装状态
2025-07-28 12:51:48 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray已安装，版本: 1.1.12
2025-07-28 12:51:48 - InstagramDMTask - INFO - [模拟器5] ✅ Instagram已安装，版本: 278.0.0.21.117
2025-07-28 12:51:48 - InstagramDMTask - INFO - [模拟器5] 📊 应用安装状态检测结果:
2025-07-28 12:51:48 - InstagramDMTask - INFO - [模拟器5] ✅ 所有必要应用已安装
2025-07-28 12:51:48 - InstagramDMTask - INFO - [模拟器5] 开始启动V2Ray应用
2025-07-28 12:51:48 - InstagramDMTask - INFO - [模拟器5] V2Ray启动命令执行成功，等待应用加载
2025-07-28 12:51:48 - InstagramDMTask - INFO - [模拟器5] V2Ray应用启动结果: 成功
2025-07-28 12:51:51 - InstagramDMTask - INFO - [模拟器5] ✅ V2Ray应用启动成功
2025-07-28 12:51:51 - InstagramDMTask - INFO - [模拟器5] 开始检查V2Ray节点列表状态
2025-07-28 12:51:52 - InstagramDMTask - INFO - [模拟器5] ✅ 检测到V2Ray节点列表，节点已存在
2025-07-28 12:51:52 - InstagramDMTask - INFO - [模拟器5] 开始连接V2Ray节点
2025-07-28 12:51:53 - InstagramDMTask - INFO - [模拟器5] 当前连接状态: 未连接
2025-07-28 12:51:53 - InstagramDMTask - INFO - [模拟器5] V2Ray节点未连接，开始连接
2025-07-28 12:51:54 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 12:51:55 - __main__ - INFO - 开始清理资源...
2025-07-28 12:51:55 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 12:51:55 - __main__ - INFO - 配置已保存
2025-07-28 12:51:55 - __main__ - ERROR - 异步桥梁关闭失败: 'FixedAsyncBridge' object has no attribute 'shutdown'
2025-07-28 12:51:55 - __main__ - INFO - 应用程序已退出
