# -*- coding: utf-8 -*-
"""
⚡ 快速打包脚本 - 简化版
功能：快速打包Instagram任务管理器，跳过复杂的测试步骤
"""

import os
import sys
import subprocess
from pathlib import Path

def quick_package():
    """快速打包"""
    print("⚡ Instagram任务管理器 - 快速打包")
    print("=" * 40)
    
    project_root = Path(__file__).parent
    
    # 1. 检查主程序文件
    main_file = project_root / "main.py"
    if not main_file.exists():
        print("❌ 找不到main.py文件")
        return False
    
    print("✅ 找到main.py文件")
    
    # 2. 安装PyInstaller
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
    except ImportError:
        print("📦 正在安装PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("✅ PyInstaller安装完成")
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False
    
    # 3. 执行快速打包
    print("🔨 开始快速打包...")
    
    # 基础打包命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # 隐藏控制台窗口
        "--name=Instagram任务管理器",    # 设置程序名称
        "--add-data=img;img",           # 添加图片目录
        "--add-data=config;config",     # 添加配置目录
        "--add-data=data;data",         # 添加数据目录
        "--add-data=app_config.json;.", # 添加配置文件
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui",
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=psutil",
        "--hidden-import=sqlite3",
        "main.py"
    ]
    
    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 打包完成")
            
            # 检查输出文件
            exe_file = project_root / "dist" / "Instagram任务管理器.exe"
            if exe_file.exists():
                size_mb = exe_file.stat().st_size / (1024 * 1024)
                print(f"📦 输出文件: {exe_file}")
                print(f"📏 文件大小: {size_mb:.1f} MB")
                
                # 询问是否测试
                test = input("\n是否测试运行? (y/n): ").strip().lower()
                if test == 'y':
                    print("🚀 启动测试...")
                    if os.name == 'nt':
                        subprocess.Popen([str(exe_file)], shell=True)
                    else:
                        subprocess.Popen([str(exe_file)])
                    print("💡 程序已启动，请测试功能")
                
                return True
            else:
                print("❌ 未找到输出文件")
                return False
        else:
            print("❌ 打包失败")
            print(f"错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return False

def main():
    """主函数"""
    try:
        success = quick_package()
        
        if success:
            print("\n🎉 快速打包完成！")
            print("💡 提示: 如需完整测试，请运行 package_and_test.py")
        else:
            print("\n💥 快速打包失败")
            print("💡 建议: 运行 package_and_test.py 进行详细诊断")
        
        input("\n按Enter键退出...")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n💥 程序出错: {e}")

if __name__ == "__main__":
    main()
