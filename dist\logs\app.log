2025-07-28 10:56:20 - root - INFO - 简化日志系统初始化完成
2025-07-28 10:56:20 - main - INFO - 应用程序启动
2025-07-28 10:56:20 - __main__ - INFO - Qt应用程序已创建
2025-07-28 10:56:20 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 10:56:20 - UnifiedConfigManager - INFO - 创建默认配置: app_config.json
2025-07-28 10:56:20 - __main__ - INFO - 统一配置管理器已创建
2025-07-28 10:56:20 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-28 10:56:20 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-28 10:56:20 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\dist\emulator_system.db
2025-07-28 10:56:20 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-28 10:56:20 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-28 10:56:20 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-28 10:56:20 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-28 10:56:20 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 10秒
2025-07-28 10:56:20 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-28 10:56:20 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-28 10:56:21 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 10:56:21 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-28 10:56:21 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-28 10:56:21 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-28 10:56:21 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-28 10:56:21 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-28 10:56:21 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-28 10:56:21 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-28 10:56:21 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-28 10:56:21 - __main__ - INFO - UI主窗口已创建
2025-07-28 10:56:21 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-28 10:56:21 - __main__ - INFO - 主窗口已显示
2025-07-28 10:56:21 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-28 10:56:21 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-28 10:56:21 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-28 10:56:21 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-28 10:56:21 - __main__ - INFO - UI层和业务层已连接
2025-07-28 10:56:21 - __main__ - INFO - 启动Qt事件循环
2025-07-28 10:56:22 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-28 10:56:22 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-28 10:56:22 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-28 10:56:22 - __main__ - INFO - 后台服务已启动
2025-07-28 10:56:22 - __main__ - INFO - 延迟启动服务完成
