# -*- coding: utf-8 -*-
"""
🔍 验证dist目录
功能：快速检查打包结果是否包含所有必要文件
"""

from pathlib import Path

def verify_dist():
    """验证dist目录"""
    print("🔍 验证dist目录")
    print("=" * 40)
    
    project_root = Path(__file__).parent
    dist_dir = project_root / "dist"
    
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    print(f"✅ dist目录存在: {dist_dir}")
    
    # 检查必要文件
    required_items = {
        "ins雷电中控.exe": "主程序文件",
        "img": "图片资源目录",
        "app_config.json": "配置文件"
    }
    
    all_present = True
    
    for item_name, description in required_items.items():
        item_path = dist_dir / item_name
        
        if item_path.exists():
            if item_path.is_file():
                size_mb = item_path.stat().st_size / (1024 * 1024)
                print(f"  ✅ {description}: {item_name} ({size_mb:.1f}MB)")
            else:
                sub_count = len(list(item_path.iterdir()))
                print(f"  ✅ {description}: {item_name}/ ({sub_count}个文件)")
        else:
            print(f"  ❌ {description}: {item_name} (缺失)")
            all_present = False
    
    # 详细检查img目录
    img_dir = dist_dir / "img"
    if img_dir.exists():
        png_files = list(img_dir.glob("*.png"))
        print(f"\n📸 img目录详情:")
        for png_file in png_files:
            size_kb = png_file.stat().st_size / 1024
            print(f"    📷 {png_file.name} ({size_kb:.1f}KB)")
    
    # 显示完整目录结构
    print(f"\n📁 完整目录结构:")
    for item in sorted(dist_dir.iterdir()):
        if item.is_file():
            size_mb = item.stat().st_size / (1024 * 1024)
            print(f"  📄 {item.name} ({size_mb:.1f}MB)")
        else:
            sub_count = len(list(item.iterdir())) if item.is_dir() else 0
            print(f"  📁 {item.name}/ ({sub_count}个文件)")
    
    return all_present

def main():
    """主函数"""
    success = verify_dist()
    
    if success:
        print("\n✅ 验证通过！所有必要文件都存在")
        print("💡 图色识别功能应该正常工作")
    else:
        print("\n❌ 验证失败！存在缺失文件")
        print("💡 建议运行: python package_fixed.py")
    
    input("\n按Enter键退出...")

if __name__ == "__main__":
    main()
