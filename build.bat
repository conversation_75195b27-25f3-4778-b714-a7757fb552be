@echo off
chcp 65001 >nul
echo 🚀 Instagram任务管理器 - 自动打包脚本
echo ================================================

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

:: 检查PyInstaller是否安装
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo 📦 正在安装PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
)

:: 执行打包
echo 🔨 开始打包...
python build_config.py

if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
) else (
    echo ✅ 打包成功!
    echo 📁 输出文件在 dist 目录中
    
    :: 询问是否打开输出目录
    set /p choice="是否打开输出目录? (y/n): "
    if /i "%choice%"=="y" (
        explorer dist
    )
)

pause
