(['e:\\VScodexiangmu\\insleidian2\\main.py'],
 ['e:\\VScodexiangmu\\insleidian2'],
 ['PyQt6.QtCore',
  'PyQt6.QtGui',
  'PyQt6.QtWidgets',
  'requests',
  'psutil',
  'sqlite3'],
 [('E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('E:\\python-3.12.9\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('E:\\python-3.12.9\\Lib\\site-packages\\_pyinstaller_hooks_contrib', -1000)],
 {},
 ['matplotlib',
  'pandas',
  'scipy',
  'sklearn',
  'tensorflow',
  'torch',
  'jupyter',
  'IPython',
  'notebook',
  'sympy',
  'statsmodels',
  'seaborn',
  'plotly',
  'bokeh',
  'PyQt6.QtWebEngine',
  'PyQt6.QtWebEngineWidgets',
  'PyQt6.QtWebEngineCore',
  'PyQt6.QtMultimedia',
  'PyQt6.QtMultimediaWidgets',
  'PyQt6.Qt3D',
  'PyQt6.QtCharts',
  'PyQt6.QtDataVisualization',
  'PyQt6.QtDesigner',
  'PyQt6.QtHelp',
  'PyQt6.QtLocation',
  'PyQt6.QtNfc',
  'PyQt6.QtPositioning',
  'PyQt6.QtQuick',
  'PyQt6.QtQuickWidgets',
  'PyQt6.QtRemoteObjects',
  'PyQt6.QtSensors',
  'PyQt6.QtSerialPort',
  'PyQt6.QtSql',
  'PyQt6.QtSvg',
  'PyQt6.QtTest',
  'PyQt6.QtWebChannel',
  'PyQt6.QtWebSockets',
  'cv2.aruco',
  'cv2.bgsegm',
  'cv2.bioinspired',
  'cv2.ccalib',
  'cv2.datasets',
  'cv2.dnn_superres',
  'cv2.face',
  'cv2.freetype',
  'cv2.fuzzy',
  'cv2.hdf',
  'cv2.hfs',
  'cv2.img_hash',
  'cv2.intensity_transform',
  'cv2.line_descriptor',
  'cv2.mcc',
  'cv2.optflow',
  'cv2.phase_unwrapping',
  'cv2.plot',
  'cv2.quality',
  'cv2.rapid',
  'cv2.reg',
  'cv2.rgbd',
  'cv2.saliency',
  'cv2.stereo',
  'cv2.structured_light',
  'cv2.superres',
  'cv2.surface_matching',
  'cv2.text',
  'cv2.tracking',
  'cv2.videoio',
  'cv2.videostab',
  'cv2.wechat_qrcode',
  'cv2.xfeatures2d',
  'cv2.ximgproc',
  'cv2.xobjdetect',
  'cv2.xphoto',
  'numpy.f2py',
  'numpy.distutils',
  'numpy.testing',
  '__main__'],
 [],
 False,
 {},
 2,
 [],
 [('app_config.json',
   'e:\\VScodexiangmu\\insleidian2\\app_config.json',
   'DATA'),
  ('img\\V2.png', 'e:\\VScodexiangmu\\insleidian2\\img\\V2.png', 'DATA'),
  ('img\\chehui.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\chehui.png',
   'DATA'),
  ('img\\chehui2.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\chehui2.png',
   'DATA'),
  ('img\\douyin.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\douyin.png',
   'DATA'),
  ('img\\faxiaoxi.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\faxiaoxi.png',
   'DATA'),
  ('img\\ins.png', 'e:\\VScodexiangmu\\insleidian2\\img\\ins.png', 'DATA')],
 '3.12.9 (tags/v3.12.9:fdb8142, Feb  4 2025, 15:27:58) [MSC v.1942 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('main', 'E:\\VScodexiangmu\\insleidian2\\main.py', 'PYSOURCE-2')],
 [('_pyi_rth_utils.qt',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE-2'),
  ('importlib', 'E:\\python-3.12.9\\Lib\\importlib\\__init__.py', 'PYMODULE-2'),
  ('importlib._bootstrap_external',
   'E:\\python-3.12.9\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE-2'),
  ('importlib.metadata',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE-2'),
  ('typing', 'E:\\python-3.12.9\\Lib\\typing.py', 'PYMODULE-2'),
  ('importlib.abc', 'E:\\python-3.12.9\\Lib\\importlib\\abc.py', 'PYMODULE-2'),
  ('importlib.resources.abc',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE-2'),
  ('importlib.resources',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE-2'),
  ('importlib.resources._legacy',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE-2'),
  ('importlib.resources._common',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE-2'),
  ('importlib.resources._adapters',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE-2'),
  ('tempfile', 'E:\\python-3.12.9\\Lib\\tempfile.py', 'PYMODULE-2'),
  ('random', 'E:\\python-3.12.9\\Lib\\random.py', 'PYMODULE-2'),
  ('_strptime', 'E:\\python-3.12.9\\Lib\\_strptime.py', 'PYMODULE-2'),
  ('datetime', 'E:\\python-3.12.9\\Lib\\datetime.py', 'PYMODULE-2'),
  ('_pydatetime', 'E:\\python-3.12.9\\Lib\\_pydatetime.py', 'PYMODULE-2'),
  ('calendar', 'E:\\python-3.12.9\\Lib\\calendar.py', 'PYMODULE-2'),
  ('argparse', 'E:\\python-3.12.9\\Lib\\argparse.py', 'PYMODULE-2'),
  ('copy', 'E:\\python-3.12.9\\Lib\\copy.py', 'PYMODULE-2'),
  ('gettext', 'E:\\python-3.12.9\\Lib\\gettext.py', 'PYMODULE-2'),
  ('struct', 'E:\\python-3.12.9\\Lib\\struct.py', 'PYMODULE-2'),
  ('statistics', 'E:\\python-3.12.9\\Lib\\statistics.py', 'PYMODULE-2'),
  ('decimal', 'E:\\python-3.12.9\\Lib\\decimal.py', 'PYMODULE-2'),
  ('_pydecimal', 'E:\\python-3.12.9\\Lib\\_pydecimal.py', 'PYMODULE-2'),
  ('contextvars', 'E:\\python-3.12.9\\Lib\\contextvars.py', 'PYMODULE-2'),
  ('fractions', 'E:\\python-3.12.9\\Lib\\fractions.py', 'PYMODULE-2'),
  ('numbers', 'E:\\python-3.12.9\\Lib\\numbers.py', 'PYMODULE-2'),
  ('hashlib', 'E:\\python-3.12.9\\Lib\\hashlib.py', 'PYMODULE-2'),
  ('bisect', 'E:\\python-3.12.9\\Lib\\bisect.py', 'PYMODULE-2'),
  ('shutil', 'E:\\python-3.12.9\\Lib\\shutil.py', 'PYMODULE-2'),
  ('tarfile', 'E:\\python-3.12.9\\Lib\\tarfile.py', 'PYMODULE-2'),
  ('gzip', 'E:\\python-3.12.9\\Lib\\gzip.py', 'PYMODULE-2'),
  ('_compression', 'E:\\python-3.12.9\\Lib\\_compression.py', 'PYMODULE-2'),
  ('lzma', 'E:\\python-3.12.9\\Lib\\lzma.py', 'PYMODULE-2'),
  ('bz2', 'E:\\python-3.12.9\\Lib\\bz2.py', 'PYMODULE-2'),
  ('fnmatch', 'E:\\python-3.12.9\\Lib\\fnmatch.py', 'PYMODULE-2'),
  ('importlib._abc',
   'E:\\python-3.12.9\\Lib\\importlib\\_abc.py',
   'PYMODULE-2'),
  ('importlib.machinery',
   'E:\\python-3.12.9\\Lib\\importlib\\machinery.py',
   'PYMODULE-2'),
  ('importlib.metadata._itertools',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.metadata._functools',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE-2'),
  ('importlib.metadata._collections',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE-2'),
  ('importlib.metadata._meta',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE-2'),
  ('importlib.metadata._adapters',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.metadata._text',
   'E:\\python-3.12.9\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE-2'),
  ('email.message', 'E:\\python-3.12.9\\Lib\\email\\message.py', 'PYMODULE-2'),
  ('email.policy', 'E:\\python-3.12.9\\Lib\\email\\policy.py', 'PYMODULE-2'),
  ('email.contentmanager',
   'E:\\python-3.12.9\\Lib\\email\\contentmanager.py',
   'PYMODULE-2'),
  ('email.quoprimime',
   'E:\\python-3.12.9\\Lib\\email\\quoprimime.py',
   'PYMODULE-2'),
  ('string', 'E:\\python-3.12.9\\Lib\\string.py', 'PYMODULE-2'),
  ('email.headerregistry',
   'E:\\python-3.12.9\\Lib\\email\\headerregistry.py',
   'PYMODULE-2'),
  ('email._header_value_parser',
   'E:\\python-3.12.9\\Lib\\email\\_header_value_parser.py',
   'PYMODULE-2'),
  ('urllib', 'E:\\python-3.12.9\\Lib\\urllib\\__init__.py', 'PYMODULE-2'),
  ('email.iterators',
   'E:\\python-3.12.9\\Lib\\email\\iterators.py',
   'PYMODULE-2'),
  ('email.generator',
   'E:\\python-3.12.9\\Lib\\email\\generator.py',
   'PYMODULE-2'),
  ('email._encoded_words',
   'E:\\python-3.12.9\\Lib\\email\\_encoded_words.py',
   'PYMODULE-2'),
  ('base64', 'E:\\python-3.12.9\\Lib\\base64.py', 'PYMODULE-2'),
  ('getopt', 'E:\\python-3.12.9\\Lib\\getopt.py', 'PYMODULE-2'),
  ('email.charset', 'E:\\python-3.12.9\\Lib\\email\\charset.py', 'PYMODULE-2'),
  ('email.encoders',
   'E:\\python-3.12.9\\Lib\\email\\encoders.py',
   'PYMODULE-2'),
  ('email.base64mime',
   'E:\\python-3.12.9\\Lib\\email\\base64mime.py',
   'PYMODULE-2'),
  ('email._policybase',
   'E:\\python-3.12.9\\Lib\\email\\_policybase.py',
   'PYMODULE-2'),
  ('email.header', 'E:\\python-3.12.9\\Lib\\email\\header.py', 'PYMODULE-2'),
  ('email.errors', 'E:\\python-3.12.9\\Lib\\email\\errors.py', 'PYMODULE-2'),
  ('email.utils', 'E:\\python-3.12.9\\Lib\\email\\utils.py', 'PYMODULE-2'),
  ('email._parseaddr',
   'E:\\python-3.12.9\\Lib\\email\\_parseaddr.py',
   'PYMODULE-2'),
  ('urllib.parse', 'E:\\python-3.12.9\\Lib\\urllib\\parse.py', 'PYMODULE-2'),
  ('ipaddress', 'E:\\python-3.12.9\\Lib\\ipaddress.py', 'PYMODULE-2'),
  ('socket', 'E:\\python-3.12.9\\Lib\\socket.py', 'PYMODULE-2'),
  ('selectors', 'E:\\python-3.12.9\\Lib\\selectors.py', 'PYMODULE-2'),
  ('quopri', 'E:\\python-3.12.9\\Lib\\quopri.py', 'PYMODULE-2'),
  ('inspect', 'E:\\python-3.12.9\\Lib\\inspect.py', 'PYMODULE-2'),
  ('token', 'E:\\python-3.12.9\\Lib\\token.py', 'PYMODULE-2'),
  ('dis', 'E:\\python-3.12.9\\Lib\\dis.py', 'PYMODULE-2'),
  ('opcode', 'E:\\python-3.12.9\\Lib\\opcode.py', 'PYMODULE-2'),
  ('ast', 'E:\\python-3.12.9\\Lib\\ast.py', 'PYMODULE-2'),
  ('contextlib', 'E:\\python-3.12.9\\Lib\\contextlib.py', 'PYMODULE-2'),
  ('textwrap', 'E:\\python-3.12.9\\Lib\\textwrap.py', 'PYMODULE-2'),
  ('zipfile', 'E:\\python-3.12.9\\Lib\\zipfile\\__init__.py', 'PYMODULE-2'),
  ('zipfile._path',
   'E:\\python-3.12.9\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE-2'),
  ('zipfile._path.glob',
   'E:\\python-3.12.9\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE-2'),
  ('py_compile', 'E:\\python-3.12.9\\Lib\\py_compile.py', 'PYMODULE-2'),
  ('threading', 'E:\\python-3.12.9\\Lib\\threading.py', 'PYMODULE-2'),
  ('_threading_local',
   'E:\\python-3.12.9\\Lib\\_threading_local.py',
   'PYMODULE-2'),
  ('importlib.util',
   'E:\\python-3.12.9\\Lib\\importlib\\util.py',
   'PYMODULE-2'),
  ('email', 'E:\\python-3.12.9\\Lib\\email\\__init__.py', 'PYMODULE-2'),
  ('email.parser', 'E:\\python-3.12.9\\Lib\\email\\parser.py', 'PYMODULE-2'),
  ('email.feedparser',
   'E:\\python-3.12.9\\Lib\\email\\feedparser.py',
   'PYMODULE-2'),
  ('csv', 'E:\\python-3.12.9\\Lib\\csv.py', 'PYMODULE-2'),
  ('importlib.readers',
   'E:\\python-3.12.9\\Lib\\importlib\\readers.py',
   'PYMODULE-2'),
  ('importlib.resources.readers',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE-2'),
  ('importlib.resources._itertools',
   'E:\\python-3.12.9\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE-2'),
  ('tokenize', 'E:\\python-3.12.9\\Lib\\tokenize.py', 'PYMODULE-2'),
  ('importlib._bootstrap',
   'E:\\python-3.12.9\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE-2'),
  ('_pyi_rth_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE-2'),
  ('subprocess', 'E:\\python-3.12.9\\Lib\\subprocess.py', 'PYMODULE-2'),
  ('signal', 'E:\\python-3.12.9\\Lib\\signal.py', 'PYMODULE-2'),
  ('multiprocessing.spawn',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_tracker',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE-2'),
  ('multiprocessing.util',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\util.py',
   'PYMODULE-2'),
  ('multiprocessing.forkserver',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.connection',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\connection.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_sharer',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE-2'),
  ('xmlrpc.client', 'E:\\python-3.12.9\\Lib\\xmlrpc\\client.py', 'PYMODULE-2'),
  ('xmlrpc', 'E:\\python-3.12.9\\Lib\\xmlrpc\\__init__.py', 'PYMODULE-2'),
  ('xml.parsers.expat',
   'E:\\python-3.12.9\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE-2'),
  ('xml.parsers',
   'E:\\python-3.12.9\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE-2'),
  ('xml', 'E:\\python-3.12.9\\Lib\\xml\\__init__.py', 'PYMODULE-2'),
  ('xml.sax.expatreader',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE-2'),
  ('xml.sax.saxutils',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE-2'),
  ('urllib.request',
   'E:\\python-3.12.9\\Lib\\urllib\\request.py',
   'PYMODULE-2'),
  ('getpass', 'E:\\python-3.12.9\\Lib\\getpass.py', 'PYMODULE-2'),
  ('nturl2path', 'E:\\python-3.12.9\\Lib\\nturl2path.py', 'PYMODULE-2'),
  ('ftplib', 'E:\\python-3.12.9\\Lib\\ftplib.py', 'PYMODULE-2'),
  ('netrc', 'E:\\python-3.12.9\\Lib\\netrc.py', 'PYMODULE-2'),
  ('mimetypes', 'E:\\python-3.12.9\\Lib\\mimetypes.py', 'PYMODULE-2'),
  ('http.cookiejar',
   'E:\\python-3.12.9\\Lib\\http\\cookiejar.py',
   'PYMODULE-2'),
  ('http', 'E:\\python-3.12.9\\Lib\\http\\__init__.py', 'PYMODULE-2'),
  ('ssl', 'E:\\python-3.12.9\\Lib\\ssl.py', 'PYMODULE-2'),
  ('urllib.response',
   'E:\\python-3.12.9\\Lib\\urllib\\response.py',
   'PYMODULE-2'),
  ('urllib.error', 'E:\\python-3.12.9\\Lib\\urllib\\error.py', 'PYMODULE-2'),
  ('xml.sax', 'E:\\python-3.12.9\\Lib\\xml\\sax\\__init__.py', 'PYMODULE-2'),
  ('xml.sax.handler',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\handler.py',
   'PYMODULE-2'),
  ('xml.sax._exceptions',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE-2'),
  ('xml.sax.xmlreader',
   'E:\\python-3.12.9\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE-2'),
  ('http.client', 'E:\\python-3.12.9\\Lib\\http\\client.py', 'PYMODULE-2'),
  ('hmac', 'E:\\python-3.12.9\\Lib\\hmac.py', 'PYMODULE-2'),
  ('multiprocessing.context',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\context.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_win32',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_forkserver',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_posix',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_fork',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE-2'),
  ('multiprocessing.sharedctypes',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE-2'),
  ('multiprocessing.heap',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\heap.py',
   'PYMODULE-2'),
  ('ctypes', 'E:\\python-3.12.9\\Lib\\ctypes\\__init__.py', 'PYMODULE-2'),
  ('ctypes.wintypes',
   'E:\\python-3.12.9\\Lib\\ctypes\\wintypes.py',
   'PYMODULE-2'),
  ('ctypes._endian',
   'E:\\python-3.12.9\\Lib\\ctypes\\_endian.py',
   'PYMODULE-2'),
  ('multiprocessing.pool',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\pool.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy.connection',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE-2'),
  ('queue', 'E:\\python-3.12.9\\Lib\\queue.py', 'PYMODULE-2'),
  ('multiprocessing.queues',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\queues.py',
   'PYMODULE-2'),
  ('multiprocessing.synchronize',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE-2'),
  ('multiprocessing.managers',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\managers.py',
   'PYMODULE-2'),
  ('multiprocessing.shared_memory',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE-2'),
  ('secrets', 'E:\\python-3.12.9\\Lib\\secrets.py', 'PYMODULE-2'),
  ('multiprocessing.reduction',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE-2'),
  ('pickle', 'E:\\python-3.12.9\\Lib\\pickle.py', 'PYMODULE-2'),
  ('pprint', 'E:\\python-3.12.9\\Lib\\pprint.py', 'PYMODULE-2'),
  ('dataclasses', 'E:\\python-3.12.9\\Lib\\dataclasses.py', 'PYMODULE-2'),
  ('_compat_pickle', 'E:\\python-3.12.9\\Lib\\_compat_pickle.py', 'PYMODULE-2'),
  ('multiprocessing.process',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\process.py',
   'PYMODULE-2'),
  ('runpy', 'E:\\python-3.12.9\\Lib\\runpy.py', 'PYMODULE-2'),
  ('pkgutil', 'E:\\python-3.12.9\\Lib\\pkgutil.py', 'PYMODULE-2'),
  ('zipimport', 'E:\\python-3.12.9\\Lib\\zipimport.py', 'PYMODULE-2'),
  ('multiprocessing',
   'E:\\python-3.12.9\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE-2'),
  ('sqlite3', 'E:\\python-3.12.9\\Lib\\sqlite3\\__init__.py', 'PYMODULE-2'),
  ('sqlite3.dump', 'E:\\python-3.12.9\\Lib\\sqlite3\\dump.py', 'PYMODULE-2'),
  ('sqlite3.__main__',
   'E:\\python-3.12.9\\Lib\\sqlite3\\__main__.py',
   'PYMODULE-2'),
  ('code', 'E:\\python-3.12.9\\Lib\\code.py', 'PYMODULE-2'),
  ('codeop', 'E:\\python-3.12.9\\Lib\\codeop.py', 'PYMODULE-2'),
  ('__future__', 'E:\\python-3.12.9\\Lib\\__future__.py', 'PYMODULE-2'),
  ('sqlite3.dbapi2',
   'E:\\python-3.12.9\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE-2'),
  ('psutil',
   'E:\\python-3.12.9\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE-2'),
  ('psutil._pswindows',
   'E:\\python-3.12.9\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE-2'),
  ('psutil._common',
   'E:\\python-3.12.9\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE-2'),
  ('requests',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE-2'),
  ('requests.status_codes',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE-2'),
  ('requests.structures',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE-2'),
  ('requests.compat',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE-2'),
  ('http.cookies', 'E:\\python-3.12.9\\Lib\\http\\cookies.py', 'PYMODULE-2'),
  ('json', 'E:\\python-3.12.9\\Lib\\json\\__init__.py', 'PYMODULE-2'),
  ('json.encoder', 'E:\\python-3.12.9\\Lib\\json\\encoder.py', 'PYMODULE-2'),
  ('json.decoder', 'E:\\python-3.12.9\\Lib\\json\\decoder.py', 'PYMODULE-2'),
  ('json.scanner', 'E:\\python-3.12.9\\Lib\\json\\scanner.py', 'PYMODULE-2'),
  ('requests.models',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE-2'),
  ('idna',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE-2'),
  ('idna.package_data',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE-2'),
  ('idna.intranges',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE-2'),
  ('idna.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE-2'),
  ('idna.uts46data',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE-2'),
  ('idna.idnadata',
   'E:\\python-3.12.9\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE-2'),
  ('requests.hooks',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE-2'),
  ('requests.cookies',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE-2'),
  ('requests.auth',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE-2'),
  ('requests._internal_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE-2'),
  ('urllib3.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.util.wait',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE-2'),
  ('urllib3.util.url',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE-2'),
  ('urllib3.util.util',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE-2'),
  ('urllib3.util.timeout',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE-2'),
  ('urllib3.util.ssl_',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE-2'),
  ('urllib3.util.ssltransport',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE-2'),
  ('typing_extensions',
   'E:\\python-3.12.9\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE-2'),
  ('asyncio.coroutines',
   'E:\\python-3.12.9\\Lib\\asyncio\\coroutines.py',
   'PYMODULE-2'),
  ('asyncio', 'E:\\python-3.12.9\\Lib\\asyncio\\__init__.py', 'PYMODULE-2'),
  ('asyncio.unix_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\unix_events.py',
   'PYMODULE-2'),
  ('asyncio.log', 'E:\\python-3.12.9\\Lib\\asyncio\\log.py', 'PYMODULE-2'),
  ('asyncio.windows_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\windows_events.py',
   'PYMODULE-2'),
  ('asyncio.windows_utils',
   'E:\\python-3.12.9\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE-2'),
  ('asyncio.selector_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\selector_events.py',
   'PYMODULE-2'),
  ('asyncio.proactor_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE-2'),
  ('asyncio.base_subprocess',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE-2'),
  ('asyncio.threads',
   'E:\\python-3.12.9\\Lib\\asyncio\\threads.py',
   'PYMODULE-2'),
  ('asyncio.taskgroups',
   'E:\\python-3.12.9\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE-2'),
  ('asyncio.subprocess',
   'E:\\python-3.12.9\\Lib\\asyncio\\subprocess.py',
   'PYMODULE-2'),
  ('asyncio.streams',
   'E:\\python-3.12.9\\Lib\\asyncio\\streams.py',
   'PYMODULE-2'),
  ('asyncio.queues',
   'E:\\python-3.12.9\\Lib\\asyncio\\queues.py',
   'PYMODULE-2'),
  ('asyncio.runners',
   'E:\\python-3.12.9\\Lib\\asyncio\\runners.py',
   'PYMODULE-2'),
  ('asyncio.base_events',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_events.py',
   'PYMODULE-2'),
  ('concurrent.futures',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE-2'),
  ('concurrent.futures.thread',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE-2'),
  ('concurrent.futures.process',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE-2'),
  ('concurrent.futures._base',
   'E:\\python-3.12.9\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE-2'),
  ('concurrent',
   'E:\\python-3.12.9\\Lib\\concurrent\\__init__.py',
   'PYMODULE-2'),
  ('asyncio.trsock',
   'E:\\python-3.12.9\\Lib\\asyncio\\trsock.py',
   'PYMODULE-2'),
  ('asyncio.staggered',
   'E:\\python-3.12.9\\Lib\\asyncio\\staggered.py',
   'PYMODULE-2'),
  ('asyncio.timeouts',
   'E:\\python-3.12.9\\Lib\\asyncio\\timeouts.py',
   'PYMODULE-2'),
  ('asyncio.tasks', 'E:\\python-3.12.9\\Lib\\asyncio\\tasks.py', 'PYMODULE-2'),
  ('asyncio.base_tasks',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE-2'),
  ('asyncio.locks', 'E:\\python-3.12.9\\Lib\\asyncio\\locks.py', 'PYMODULE-2'),
  ('asyncio.mixins',
   'E:\\python-3.12.9\\Lib\\asyncio\\mixins.py',
   'PYMODULE-2'),
  ('asyncio.sslproto',
   'E:\\python-3.12.9\\Lib\\asyncio\\sslproto.py',
   'PYMODULE-2'),
  ('asyncio.transports',
   'E:\\python-3.12.9\\Lib\\asyncio\\transports.py',
   'PYMODULE-2'),
  ('asyncio.protocols',
   'E:\\python-3.12.9\\Lib\\asyncio\\protocols.py',
   'PYMODULE-2'),
  ('asyncio.futures',
   'E:\\python-3.12.9\\Lib\\asyncio\\futures.py',
   'PYMODULE-2'),
  ('asyncio.base_futures',
   'E:\\python-3.12.9\\Lib\\asyncio\\base_futures.py',
   'PYMODULE-2'),
  ('asyncio.exceptions',
   'E:\\python-3.12.9\\Lib\\asyncio\\exceptions.py',
   'PYMODULE-2'),
  ('asyncio.events',
   'E:\\python-3.12.9\\Lib\\asyncio\\events.py',
   'PYMODULE-2'),
  ('asyncio.format_helpers',
   'E:\\python-3.12.9\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE-2'),
  ('asyncio.constants',
   'E:\\python-3.12.9\\Lib\\asyncio\\constants.py',
   'PYMODULE-2'),
  ('urllib3.util.retry',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE-2'),
  ('urllib3.response',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE-2'),
  ('urllib3.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE-2'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE-2'),
  ('urllib3._version',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE-2'),
  ('urllib3.http2.probe',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE-2'),
  ('urllib3.http2',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.http2.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE-2'),
  ('urllib3._collections',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE-2'),
  ('urllib3._base_connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE-2'),
  ('urllib3.connectionpool',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE-2'),
  ('urllib3.util.proxy',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE-2'),
  ('urllib3._request_methods',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE-2'),
  ('urllib3.util.response',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE-2'),
  ('urllib3.util.request',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE-2'),
  ('urllib3.util.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE-2'),
  ('urllib3.filepost',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE-2'),
  ('urllib3.fields',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE-2'),
  ('requests.api',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE-2'),
  ('requests.sessions',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE-2'),
  ('requests.adapters',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE-2'),
  ('urllib3.contrib.socks',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE-2'),
  ('socks', 'E:\\python-3.12.9\\Lib\\site-packages\\socks.py', 'PYMODULE-2'),
  ('urllib3.poolmanager',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE-2'),
  ('requests.__version__',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE-2'),
  ('requests.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE-2'),
  ('requests.certs',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE-2'),
  ('certifi',
   'E:\\python-3.12.9\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE-2'),
  ('certifi.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE-2'),
  ('requests.packages',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE-2'),
  ('urllib3.exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE-2'),
  ('urllib3.contrib.pyopenssl',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE-2'),
  ('urllib3.contrib',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE-2'),
  ('charset_normalizer',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE-2'),
  ('charset_normalizer.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE-2'),
  ('charset_normalizer.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE-2'),
  ('charset_normalizer.constant',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE-2'),
  ('charset_normalizer.models',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE-2'),
  ('charset_normalizer.cd',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE-2'),
  ('charset_normalizer.legacy',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE-2'),
  ('charset_normalizer.api',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE-2'),
  ('requests.exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE-2'),
  ('urllib3',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.response',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.request',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE-2'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\python-3.12.9\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE-2'),
  ('PyQt6',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE-2'),
  ('tracemalloc', 'E:\\python-3.12.9\\Lib\\tracemalloc.py', 'PYMODULE-2'),
  ('stringprep', 'E:\\python-3.12.9\\Lib\\stringprep.py', 'PYMODULE-2'),
  ('_py_abc', 'E:\\python-3.12.9\\Lib\\_py_abc.py', 'PYMODULE-2'),
  ('ui.main_window_v2',
   'e:\\VScodexiangmu\\insleidian2\\ui\\main_window_v2.py',
   'PYMODULE-2'),
  ('ui', 'e:\\VScodexiangmu\\insleidian2\\ui\\__init__.py', 'PYMODULE-2'),
  ('ui.instagram_follow_ui',
   'e:\\VScodexiangmu\\insleidian2\\ui\\instagram_follow_ui.py',
   'PYMODULE-2'),
  ('ui.style_manager',
   'e:\\VScodexiangmu\\insleidian2\\ui\\style_manager.py',
   'PYMODULE-2'),
  ('ui.instagram_dm_ui',
   'e:\\VScodexiangmu\\insleidian2\\ui\\instagram_dm_ui.py',
   'PYMODULE-2'),
  ('ui.basic_config_ui',
   'e:\\VScodexiangmu\\insleidian2\\ui\\basic_config_ui.py',
   'PYMODULE-2'),
  ('ui.settings_ui',
   'e:\\VScodexiangmu\\insleidian2\\ui\\settings_ui.py',
   'PYMODULE-2'),
  ('core.heartbeat_manager',
   'e:\\VScodexiangmu\\insleidian2\\core\\heartbeat_manager.py',
   'PYMODULE-2'),
  ('core', '-', 'PYMODULE-2'),
  ('core.screenshot_manager',
   'e:\\VScodexiangmu\\insleidian2\\core\\screenshot_manager.py',
   'PYMODULE-2'),
  ('core.native',
   'e:\\VScodexiangmu\\insleidian2\\core\\native\\__init__.py',
   'PYMODULE-2'),
  ('core.native.screenshot_engine',
   'e:\\VScodexiangmu\\insleidian2\\core\\native\\screenshot_engine.py',
   'PYMODULE-2'),
  ('PIL.ImageGrab',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE-2'),
  ('PIL.BmpImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE-2'),
  ('PIL._binary',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE-2'),
  ('PIL.ImagePalette',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE-2'),
  ('PIL.PaletteFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE-2'),
  ('PIL.ImageColor',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE-2'),
  ('colorsys', 'E:\\python-3.12.9\\Lib\\colorsys.py', 'PYMODULE-2'),
  ('PIL.GimpPaletteFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE-2'),
  ('PIL.GimpGradientFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE-2'),
  ('PIL.ImageFile',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE-2'),
  ('PIL.TiffImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TiffTags',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE-2'),
  ('PIL.ImageOps',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE-2'),
  ('PIL._typing',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE-2'),
  ('numpy.typing',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core._dtype_ctypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE-2'),
  ('numpy.strings',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core.strings',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE-2'),
  ('numpy._core.umath',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE-2'),
  ('numpy._core.overrides',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE-2'),
  ('numpy._utils._inspect',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE-2'),
  ('numpy._utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE-2'),
  ('numpy._utils._convertions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE-2'),
  ('numpy._core.multiarray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE-2'),
  ('numpy.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE-2'),
  ('numpy.core._utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE-2'),
  ('numpy.char',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core.defchararray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE-2'),
  ('numpy._core.numeric',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE-2'),
  ('numpy._core._asarray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE-2'),
  ('numpy._core.arrayprint',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE-2'),
  ('numpy._core.fromnumeric',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE-2'),
  ('numpy._core._methods',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE-2'),
  ('numpy._core._exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE-2'),
  ('numpy._core._ufunc_config',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE-2'),
  ('numpy._core.shape_base',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE-2'),
  ('numpy._core.numerictypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE-2'),
  ('numpy._core._dtype',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE-2'),
  ('numpy._core._type_aliases',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE-2'),
  ('numpy._core._string_helpers',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE-2'),
  ('numpy.rec',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core.records',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE-2'),
  ('numpy.matlib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE-2'),
  ('numpy.matrixlib.defmatrix',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE-2'),
  ('numpy.exceptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE-2'),
  ('numpy.ctypeslib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE-2'),
  ('sysconfig', 'E:\\python-3.12.9\\Lib\\sysconfig.py', 'PYMODULE-2'),
  ('_aix_support', 'E:\\python-3.12.9\\Lib\\_aix_support.py', 'PYMODULE-2'),
  ('numpy._core._internal',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE-2'),
  ('numpy.ma',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE-2'),
  ('numpy.ma.extras',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE-2'),
  ('numpy.lib.array_utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE-2'),
  ('numpy.lib._array_utils_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE-2'),
  ('numpy.ma.core',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE-2'),
  ('numpy.polynomial',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE-2'),
  ('numpy.polynomial._polybase',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE-2'),
  ('numpy.polynomial.laguerre',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite_e',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE-2'),
  ('numpy.polynomial.legendre',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.chebyshev',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polynomial',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polyutils',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE-2'),
  ('numpy.random',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE-2'),
  ('numpy.random._pickle',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE-2'),
  ('numpy.dtypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE-2'),
  ('numpy.fft',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE-2'),
  ('numpy.fft.helper',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE-2'),
  ('numpy.fft._helper',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE-2'),
  ('numpy.fft._pocketfft',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE-2'),
  ('numpy.linalg',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE-2'),
  ('numpy.linalg.linalg',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE-2'),
  ('numpy.linalg._linalg',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE-2'),
  ('numpy._typing._ufunc',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE-2'),
  ('numpy._array_api_info',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE-2'),
  ('numpy.matrixlib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.lib._index_tricks_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib.stride_tricks',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE-2'),
  ('numpy.lib._npyio_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE-2'),
  ('numpy.ma.mrecords',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE-2'),
  ('numpy.lib._iotools',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE-2'),
  ('numpy.lib._datasource',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE-2'),
  ('numpy.lib.format',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE-2'),
  ('numpy.lib._polynomial_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._stride_tricks_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._utils_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE-2'),
  ('pydoc', 'E:\\python-3.12.9\\Lib\\pydoc.py', 'PYMODULE-2'),
  ('webbrowser', 'E:\\python-3.12.9\\Lib\\webbrowser.py', 'PYMODULE-2'),
  ('shlex', 'E:\\python-3.12.9\\Lib\\shlex.py', 'PYMODULE-2'),
  ('http.server', 'E:\\python-3.12.9\\Lib\\http\\server.py', 'PYMODULE-2'),
  ('socketserver', 'E:\\python-3.12.9\\Lib\\socketserver.py', 'PYMODULE-2'),
  ('html', 'E:\\python-3.12.9\\Lib\\html\\__init__.py', 'PYMODULE-2'),
  ('html.entities', 'E:\\python-3.12.9\\Lib\\html\\entities.py', 'PYMODULE-2'),
  ('pydoc_data.topics',
   'E:\\python-3.12.9\\Lib\\pydoc_data\\topics.py',
   'PYMODULE-2'),
  ('pydoc_data',
   'E:\\python-3.12.9\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE-2'),
  ('tty', 'E:\\python-3.12.9\\Lib\\tty.py', 'PYMODULE-2'),
  ('platform', 'E:\\python-3.12.9\\Lib\\platform.py', 'PYMODULE-2'),
  ('numpy.lib._arraypad_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._ufunclike_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arraysetops_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._type_check_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE-2'),
  ('numpy._core.getlimits',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE-2'),
  ('numpy._core._machar',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE-2'),
  ('numpy.lib._shape_base_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._twodim_base_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._function_base_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._nanfunctions_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._histograms_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE-2'),
  ('numpy.lib.scimath',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE-2'),
  ('numpy.lib._scimath_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE-2'),
  ('numpy.lib',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core.function_base',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE-2'),
  ('numpy.lib._version',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE-2'),
  ('numpy.lib._arrayterator_impl',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE-2'),
  ('numpy.lib.npyio',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE-2'),
  ('numpy.lib.mixins',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE-2'),
  ('numpy.lib.introspect',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE-2'),
  ('numpy._core.printoptions',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE-2'),
  ('numpy._core.memmap',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE-2'),
  ('numpy._core',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs_scalars',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE-2'),
  ('numpy._core.einsumfunc',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE-2'),
  ('numpy.__config__',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE-2'),
  ('yaml',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE-2'),
  ('yaml.cyaml',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE-2'),
  ('yaml.resolver',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE-2'),
  ('yaml.representer',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE-2'),
  ('yaml.serializer',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE-2'),
  ('yaml.constructor',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE-2'),
  ('yaml.dumper',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE-2'),
  ('yaml.emitter',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE-2'),
  ('yaml.loader',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE-2'),
  ('yaml.composer',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE-2'),
  ('yaml.parser',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE-2'),
  ('yaml.scanner',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE-2'),
  ('yaml.reader',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE-2'),
  ('yaml.nodes',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE-2'),
  ('yaml.events',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE-2'),
  ('yaml.tokens',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE-2'),
  ('yaml.error',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE-2'),
  ('numpy._distributor_init',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE-2'),
  ('numpy.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE-2'),
  ('numpy._expired_attrs_2_0',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE-2'),
  ('numpy._globals',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE-2'),
  ('numpy._pytesttester',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE-2'),
  ('numpy._typing._add_docstring',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE-2'),
  ('numpy._typing._array_like',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE-2'),
  ('numpy._typing._shape',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE-2'),
  ('numpy._typing._nested_sequence',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit_base',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE-2'),
  ('numpy._typing',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy._typing._dtype_like',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE-2'),
  ('numpy._typing._scalars',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE-2'),
  ('numpy._typing._char_codes',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE-2'),
  ('PIL._util',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE-2'),
  ('PIL._deprecate',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE-2'),
  ('PIL.ExifTags',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE-2'),
  ('PIL.PngImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImageSequence',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE-2'),
  ('PIL.ImageChops',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE-2'),
  ('PIL.ImageWin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE-2'),
  ('PIL.Image',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE-2'),
  ('PIL.XpmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XbmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XVThumbImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.WmfImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.WebPImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TgaImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SunImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SpiderImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImageTk',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE-2'),
  ('PIL.SgiImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.QoiImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PsdImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PixarImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PdfImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.features',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE-2'),
  ('PIL.PdfParser',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE-2'),
  ('PIL.PcxImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PcdImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PalmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MspImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MpoImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MpegImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MicImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.McIdasImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Jpeg2KImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IptcImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImtImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IcoImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IcnsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Hdf5StubImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GribStubImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GbrImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FtexImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FpxImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FliImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FitsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.EpsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.DdsImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.DcxImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.CurImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BufrStubImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BlpImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.AvifImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImageShow',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE-2'),
  ('PIL.ImageCms',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE-2'),
  ('PIL.PpmImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.JpegImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.JpegPresets',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE-2'),
  ('PIL.GifImagePlugin',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImageMath',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE-2'),
  ('PIL.ImageQt',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE-2'),
  ('PIL.ImageFilter',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE-2'),
  ('xml.etree.ElementTree',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE-2'),
  ('xml.etree.cElementTree',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE-2'),
  ('xml.etree.ElementInclude',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE-2'),
  ('xml.etree.ElementPath',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE-2'),
  ('xml.etree',
   'E:\\python-3.12.9\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE-2'),
  ('PIL.ImageMode',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE-2'),
  ('PIL',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE-2'),
  ('PIL._version',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE-2'),
  ('core.native.base_api',
   'e:\\VScodexiangmu\\insleidian2\\core\\native\\base_api.py',
   'PYMODULE-2'),
  ('core.native.image_recognition_engine',
   'e:\\VScodexiangmu\\insleidian2\\core\\native\\image_recognition_engine.py',
   'PYMODULE-2'),
  ('numpy.core.multiarray',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE-2'),
  ('win32con',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE-2'),
  ('core.unified_emulator_manager',
   'e:\\VScodexiangmu\\insleidian2\\core\\unified_emulator_manager.py',
   'PYMODULE-2'),
  ('data.models.emulator_model',
   'e:\\VScodexiangmu\\insleidian2\\data\\models\\emulator_model.py',
   'PYMODULE-2'),
  ('data.models',
   'e:\\VScodexiangmu\\insleidian2\\data\\models\\__init__.py',
   'PYMODULE-2'),
  ('data', 'e:\\VScodexiangmu\\insleidian2\\data\\__init__.py', 'PYMODULE-2'),
  ('data.repositories.emulator_repository',
   'e:\\VScodexiangmu\\insleidian2\\data\\repositories\\emulator_repository.py',
   'PYMODULE-2'),
  ('data.repositories',
   'e:\\VScodexiangmu\\insleidian2\\data\\repositories\\__init__.py',
   'PYMODULE-2'),
  ('data.database_manager',
   'e:\\VScodexiangmu\\insleidian2\\data\\database_manager.py',
   'PYMODULE-2'),
  ('core.status_converter',
   'e:\\VScodexiangmu\\insleidian2\\core\\status_converter.py',
   'PYMODULE-2'),
  ('ui.styled_widgets',
   'e:\\VScodexiangmu\\insleidian2\\ui\\styled_widgets.py',
   'PYMODULE-2'),
  ('ui.ui_service_layer',
   'e:\\VScodexiangmu\\insleidian2\\ui\\ui_service_layer.py',
   'PYMODULE-2'),
  ('core.async_bridge',
   'e:\\VScodexiangmu\\insleidian2\\core\\async_bridge.py',
   'PYMODULE-2'),
  ('core.instagram_follow_task',
   'e:\\VScodexiangmu\\insleidian2\\core\\instagram_follow_task.py',
   'PYMODULE-2'),
  ('core.instagram_task',
   'e:\\VScodexiangmu\\insleidian2\\core\\instagram_task.py',
   'PYMODULE-2'),
  ('core.leidianapi.雷电一键找图',
   'e:\\VScodexiangmu\\insleidian2\\core\\leidianapi\\雷电一键找图.py',
   'PYMODULE-2'),
  ('core.leidianapi', '-', 'PYMODULE-2'),
  ('aiofiles',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\__init__.py',
   'PYMODULE-2'),
  ('aiofiles.tempfile',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\tempfile\\__init__.py',
   'PYMODULE-2'),
  ('aiofiles.tempfile.temptypes',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\tempfile\\temptypes.py',
   'PYMODULE-2'),
  ('aiofiles.threadpool.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\utils.py',
   'PYMODULE-2'),
  ('aiofiles.threadpool.text',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\text.py',
   'PYMODULE-2'),
  ('aiofiles.threadpool.binary',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\binary.py',
   'PYMODULE-2'),
  ('aiofiles.base',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\base.py',
   'PYMODULE-2'),
  ('aiofiles.threadpool',
   'E:\\python-3.12.9\\Lib\\site-packages\\aiofiles\\threadpool\\__init__.py',
   'PYMODULE-2'),
  ('core.leidianapi.LeiDian_Reorganized',
   'e:\\VScodexiangmu\\insleidian2\\core\\leidianapi\\LeiDian_Reorganized.py',
   'PYMODULE-2'),
  ('core.config_hot_reload',
   'e:\\VScodexiangmu\\insleidian2\\core\\config_hot_reload.py',
   'PYMODULE-2'),
  ('core.window_arrangement_manager',
   'e:\\VScodexiangmu\\insleidian2\\core\\window_arrangement_manager.py',
   'PYMODULE-2'),
  ('core.simple_config',
   'e:\\VScodexiangmu\\insleidian2\\core\\simple_config.py',
   'PYMODULE-2'),
  ('logging', 'E:\\python-3.12.9\\Lib\\logging\\__init__.py', 'PYMODULE-2'),
  ('core.logger_manager',
   'e:\\VScodexiangmu\\insleidian2\\core\\logger_manager.py',
   'PYMODULE-2'),
  ('pathlib', 'E:\\python-3.12.9\\Lib\\pathlib.py', 'PYMODULE-2')],
 [('python312.dll', 'E:\\python-3.12.9\\python312.dll', 'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qwindowsvistastyle.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'BINARY'),
  ('_decimal.pyd', 'E:\\python-3.12.9\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'E:\\python-3.12.9\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'E:\\python-3.12.9\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'E:\\python-3.12.9\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'E:\\python-3.12.9\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'E:\\python-3.12.9\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'E:\\python-3.12.9\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'E:\\python-3.12.9\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'E:\\python-3.12.9\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'E:\\python-3.12.9\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'E:\\python-3.12.9\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'E:\\python-3.12.9\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_sqlite3.pyd', 'E:\\python-3.12.9\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_overlapped.pyd', 'E:\\python-3.12.9\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'E:\\python-3.12.9\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\charset_normalizer\\md.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'E:\\python-3.12.9\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('yaml\\_yaml.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\yaml\\_yaml.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'E:\\python-3.12.9\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\cv2.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'E:\\python-3.12.9\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'E:\\python-3.12.9\\VCRUNTIME140.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'E:\\python-3.12.9\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'E:\\python-3.12.9\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'E:\\python-3.12.9\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'E:\\python-3.12.9\\DLLs\\libffi-8.dll', 'BINARY'),
  ('sqlite3.dll', 'E:\\python-3.12.9\\DLLs\\sqlite3.dll', 'BINARY'),
  ('python3.dll', 'E:\\python-3.12.9\\python3.dll', 'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes312.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\pywin32_system32\\pywintypes312.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY')],
 [],
 [],
 [('app_config.json',
   'e:\\VScodexiangmu\\insleidian2\\app_config.json',
   'DATA'),
  ('img\\V2.png', 'e:\\VScodexiangmu\\insleidian2\\img\\V2.png', 'DATA'),
  ('img\\chehui.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\chehui.png',
   'DATA'),
  ('img\\chehui2.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\chehui2.png',
   'DATA'),
  ('img\\douyin.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\douyin.png',
   'DATA'),
  ('img\\faxiaoxi.png',
   'e:\\VScodexiangmu\\insleidian2\\img\\faxiaoxi.png',
   'DATA'),
  ('img\\ins.png', 'e:\\VScodexiangmu\\insleidian2\\img\\ins.png', 'DATA'),
  ('certifi\\py.typed',
   'E:\\python-3.12.9\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\python-3.12.9\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'E:\\python-3.12.9\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('cv2\\config.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\config.py',
   'DATA'),
  ('cv2\\config-3.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('cv2\\__init__.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA'),
  ('base_library.zip',
   'e:\\VScodexiangmu\\insleidian2\\build\\ins雷电中控_优化版\\base_library.zip',
   'DATA')],
 [('heapq', 'E:\\python-3.12.9\\Lib\\heapq.py', 'PYMODULE'),
  ('reprlib', 'E:\\python-3.12.9\\Lib\\reprlib.py', 'PYMODULE'),
  ('locale', 'E:\\python-3.12.9\\Lib\\locale.py', 'PYMODULE'),
  ('collections.abc',
   'E:\\python-3.12.9\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'E:\\python-3.12.9\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('codecs', 'E:\\python-3.12.9\\Lib\\codecs.py', 'PYMODULE'),
  ('warnings', 'E:\\python-3.12.9\\Lib\\warnings.py', 'PYMODULE'),
  ('posixpath', 'E:\\python-3.12.9\\Lib\\posixpath.py', 'PYMODULE'),
  ('enum', 'E:\\python-3.12.9\\Lib\\enum.py', 'PYMODULE'),
  ('types', 'E:\\python-3.12.9\\Lib\\types.py', 'PYMODULE'),
  ('re._parser', 'E:\\python-3.12.9\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'E:\\python-3.12.9\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'E:\\python-3.12.9\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'E:\\python-3.12.9\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('re', 'E:\\python-3.12.9\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('ntpath', 'E:\\python-3.12.9\\Lib\\ntpath.py', 'PYMODULE'),
  ('copyreg', 'E:\\python-3.12.9\\Lib\\copyreg.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'E:\\python-3.12.9\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'E:\\python-3.12.9\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'E:\\python-3.12.9\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'E:\\python-3.12.9\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'E:\\python-3.12.9\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'E:\\python-3.12.9\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'E:\\python-3.12.9\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'E:\\python-3.12.9\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'E:\\python-3.12.9\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'E:\\python-3.12.9\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'E:\\python-3.12.9\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'E:\\python-3.12.9\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem', 'E:\\python-3.12.9\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'E:\\python-3.12.9\\Lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'E:\\python-3.12.9\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'E:\\python-3.12.9\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'E:\\python-3.12.9\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'E:\\python-3.12.9\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'E:\\python-3.12.9\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'E:\\python-3.12.9\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'E:\\python-3.12.9\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'E:\\python-3.12.9\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'E:\\python-3.12.9\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'E:\\python-3.12.9\\Lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'E:\\python-3.12.9\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'E:\\python-3.12.9\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'E:\\python-3.12.9\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312',
   'E:\\python-3.12.9\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'E:\\python-3.12.9\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'E:\\python-3.12.9\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'E:\\python-3.12.9\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'E:\\python-3.12.9\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'E:\\python-3.12.9\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'E:\\python-3.12.9\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'E:\\python-3.12.9\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'E:\\python-3.12.9\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'E:\\python-3.12.9\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'E:\\python-3.12.9\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'E:\\python-3.12.9\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'E:\\python-3.12.9\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'E:\\python-3.12.9\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'E:\\python-3.12.9\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'E:\\python-3.12.9\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'E:\\python-3.12.9\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'E:\\python-3.12.9\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'E:\\python-3.12.9\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'E:\\python-3.12.9\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'E:\\python-3.12.9\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'E:\\python-3.12.9\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'E:\\python-3.12.9\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'E:\\python-3.12.9\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'E:\\python-3.12.9\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'E:\\python-3.12.9\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'E:\\python-3.12.9\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'E:\\python-3.12.9\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'E:\\python-3.12.9\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'E:\\python-3.12.9\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'E:\\python-3.12.9\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'E:\\python-3.12.9\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'E:\\python-3.12.9\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'E:\\python-3.12.9\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'E:\\python-3.12.9\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'E:\\python-3.12.9\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'E:\\python-3.12.9\\Lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'E:\\python-3.12.9\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'E:\\python-3.12.9\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'E:\\python-3.12.9\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings', 'E:\\python-3.12.9\\Lib\\encodings\\__init__.py', 'PYMODULE'),
  ('sre_compile', 'E:\\python-3.12.9\\Lib\\sre_compile.py', 'PYMODULE'),
  ('weakref', 'E:\\python-3.12.9\\Lib\\weakref.py', 'PYMODULE'),
  ('abc', 'E:\\python-3.12.9\\Lib\\abc.py', 'PYMODULE'),
  ('linecache', 'E:\\python-3.12.9\\Lib\\linecache.py', 'PYMODULE'),
  ('stat', 'E:\\python-3.12.9\\Lib\\stat.py', 'PYMODULE'),
  ('genericpath', 'E:\\python-3.12.9\\Lib\\genericpath.py', 'PYMODULE'),
  ('operator', 'E:\\python-3.12.9\\Lib\\operator.py', 'PYMODULE'),
  ('os', 'E:\\python-3.12.9\\Lib\\os.py', 'PYMODULE'),
  ('keyword', 'E:\\python-3.12.9\\Lib\\keyword.py', 'PYMODULE'),
  ('_collections_abc',
   'E:\\python-3.12.9\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('_weakrefset', 'E:\\python-3.12.9\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('sre_constants', 'E:\\python-3.12.9\\Lib\\sre_constants.py', 'PYMODULE'),
  ('io', 'E:\\python-3.12.9\\Lib\\io.py', 'PYMODULE'),
  ('sre_parse', 'E:\\python-3.12.9\\Lib\\sre_parse.py', 'PYMODULE'),
  ('functools', 'E:\\python-3.12.9\\Lib\\functools.py', 'PYMODULE'),
  ('traceback', 'E:\\python-3.12.9\\Lib\\traceback.py', 'PYMODULE'),
  ('cv2',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\__init__.py',
   'PYMODULE'),
  ('cv2.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\version.py',
   'PYMODULE'),
  ('cv2.utils',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\utils\\__init__.py',
   'PYMODULE'),
  ('cv2.typing',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\typing\\__init__.py',
   'PYMODULE'),
  ('cv2.dnn', '-', 'PYMODULE'),
  ('cv2.gapi.wip.draw', '-', 'PYMODULE'),
  ('cv2.gapi.wip', '-', 'PYMODULE'),
  ('cv2.misc.version',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\misc\\version.py',
   'PYMODULE'),
  ('cv2.misc',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\misc\\__init__.py',
   'PYMODULE'),
  ('cv2.mat_wrapper',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'PYMODULE'),
  ('cv2.gapi',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\gapi\\__init__.py',
   'PYMODULE'),
  ('cv2.data',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\data\\__init__.py',
   'PYMODULE'),
  ('cv2.config-3',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\config-3.py',
   'PYMODULE'),
  ('cv2.config',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\config.py',
   'PYMODULE'),
  ('cv2.load_config_py3',
   'E:\\python-3.12.9\\Lib\\site-packages\\cv2\\load_config_py3.py',
   'PYMODULE')])
