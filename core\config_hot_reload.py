#!/usr/bin/env python3
"""
🎯 配置热加载服务 - 长期优化
功能：
- 监控配置文件变化8
- 运行时配置更新
- 配置变更通知
- 配置验证和回滚
"""

import os
import json
import threading
from typing import Dict, Any, Callable, Optional, List
from datetime import datetime

from PyQt6.QtCore import QTimer, QObject, pyqtSignal

from .logger_manager import log_info, log_error
from .simple_config import get_config_manager

class ConfigHotReloadService(QObject):
    """🎯 配置热加载服务 - 运行时配置更新，使用Qt定时器避免阻塞"""

    # 🎯 信号定义
    config_reloaded = pyqtSignal(str)  # 配置文件路径
    config_error = pyqtSignal(str, str)  # 文件路径, 错误信息

    def __init__(self):
        super().__init__()
        self.config_manager = get_config_manager()

        # 🎯 监控配置 - 统一监控主配置文件
        self.config_files = [
            "app_config.json"  # 主配置文件，包含心跳监控等所有配置
        ]

        # 🎯 使用Qt定时器替代阻塞线程
        self._monitor_timer = QTimer()
        self._monitor_timer.timeout.connect(self._check_file_changes)
        self.check_interval = 2000  # 检查间隔（毫秒）

        # 🎯 文件监控状态
        self._file_timestamps = {}

        # 🎯 配置变更回调
        self._change_callbacks: List[Callable[[str, Dict[str, Any]], None]] = []

        # 🎯 配置备份（用于回滚）
        self._config_backup = {}

        # 🎯 统计信息
        self.stats = {
            'reload_count': 0,
            'error_count': 0,
            'last_reload_time': None,
            'monitored_files': len(self.config_files)
        }

        # 初始化文件时间戳
        self._update_file_timestamps()

        log_info("配置热加载服务初始化完成（Qt定时器模式）", component="ConfigHotReloadService")
    
    def start(self):
        """🎯 启动配置热加载监控"""
        if self._monitor_timer.isActive():
            log_info("配置热加载服务已在运行", component="ConfigHotReloadService")
            return

        self._monitor_timer.start(self.check_interval)
        log_info("配置热加载服务已启动（Qt定时器模式）", component="ConfigHotReloadService")

    def stop(self):
        """🎯 停止配置热加载监控"""
        if self._monitor_timer.isActive():
            self._monitor_timer.stop()
            log_info("配置热加载服务已停止", component="ConfigHotReloadService")
    
    def add_change_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """🎯 添加配置变更回调"""
        self._change_callbacks.append(callback)
        log_info(f"添加配置变更回调: {callback.__name__}", component="ConfigHotReloadService")
    
    def remove_change_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """🎯 移除配置变更回调"""
        if callback in self._change_callbacks:
            self._change_callbacks.remove(callback)
            log_info(f"移除配置变更回调: {callback.__name__}", component="ConfigHotReloadService")
    
    # 🎯 删除阻塞的监控循环，使用Qt定时器的_check_file_changes方法
    
    def _check_file_changes(self):
        """🎯 检查配置文件变化"""
        for config_file in self.config_files:
            try:
                if not os.path.exists(config_file):
                    continue
                
                current_mtime = os.path.getmtime(config_file)
                last_mtime = self._file_timestamps.get(config_file, 0)
                
                if current_mtime > last_mtime:
                    log_info(f"检测到配置文件变化: {config_file}", component="ConfigHotReloadService")
                    self._reload_config_file(config_file)
                    self._file_timestamps[config_file] = current_mtime
                    
            except Exception as e:
                log_error(f"检查配置文件{config_file}失败: {e}", component="ConfigHotReloadService")
    
    def _reload_config_file(self, config_file: str):
        """🎯 重新加载配置文件 - 简化逻辑，使用统一配置管理器"""
        try:
            log_info(f"检测到配置文件变更，重新加载: {config_file}", component="ConfigHotReloadService")

            # 🎯 使用统一配置管理器重新加载，观察者模式会自动通知所有监听者
            if self.config_manager.load():
                # 更新统计
                self.stats['reload_count'] += 1
                self.stats['last_reload_time'] = datetime.now().isoformat()

                log_info(f"配置文件{config_file}热加载成功", component="ConfigHotReloadService")

                # 通知配置变更（可选，主要用于统计）
                self._notify_config_change(config_file, {})
            else:
                log_error(f"配置文件{config_file}加载失败", component="ConfigHotReloadService")
                self.stats['error_count'] += 1

        except Exception as e:
            self.stats['error_count'] += 1
            log_error(f"重新加载配置文件{config_file}失败: {e}", component="ConfigHotReloadService")
    
    # 🎯 移除重复的配置验证和应用逻辑，统一使用 simple_config.py 的配置管理
    
    def _notify_config_change(self, config_file: str, new_config: Dict[str, Any]):
        """🎯 通知配置变更"""
        for callback in self._change_callbacks:
            try:
                callback(config_file, new_config)
            except Exception as e:
                log_error(f"配置变更回调{callback.__name__}执行失败: {e}", component="ConfigHotReloadService")
    
    def _rollback_config(self, config_file: str):
        """🎯 回滚配置"""
        try:
            if config_file in self._config_backup:
                backup_config = self._config_backup[config_file]
                self._apply_config_changes(config_file, backup_config)
                log_info(f"配置文件{config_file}已回滚", component="ConfigHotReloadService")
            
        except Exception as e:
            log_error(f"配置回滚失败: {e}", component="ConfigHotReloadService")
    
    def _update_file_timestamps(self):
        """🎯 更新文件时间戳"""
        for config_file in self.config_files:
            try:
                if os.path.exists(config_file):
                    self._file_timestamps[config_file] = os.path.getmtime(config_file)
            except Exception as e:
                log_error(f"获取文件{config_file}时间戳失败: {e}", component="ConfigHotReloadService")
    
    def get_service_stats(self) -> Dict[str, Any]:
        """🎯 获取服务统计信息"""
        return {
            'service_running': self._running,
            'check_interval': self.check_interval,
            'monitored_files': self.config_files,
            'file_timestamps': self._file_timestamps,
            'callback_count': len(self._change_callbacks),
            **self.stats
        }
    
    def force_reload_all(self):
        """🎯 强制重新加载所有配置文件"""
        log_info("强制重新加载所有配置文件", component="ConfigHotReloadService")
        
        for config_file in self.config_files:
            if os.path.exists(config_file):
                self._reload_config_file(config_file)

# 🎯 全局配置热加载服务实例
_hot_reload_service = None

def get_config_hot_reload_service() -> ConfigHotReloadService:
    """获取全局配置热加载服务实例"""
    global _hot_reload_service
    if _hot_reload_service is None:
        _hot_reload_service = ConfigHotReloadService()
    return _hot_reload_service
