# -*- coding: utf-8 -*-
"""
🔍 验证打包文件完整性
功能：检查打包后的程序是否包含所有必要的文件，特别是图色识别相关的img文件
"""

import os
import sys
from pathlib import Path

class PackageFileVerifier:
    """打包文件验证器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.dist_dir = self.project_root / "dist"
        
        # 必需的文件和目录
        self.required_files = {
            "程序文件": [
                "Instagram任务管理器.exe"
            ],
            "配置文件": [
                "app_config.json"
            ],
            "图片资源": [
                "img/V2.png",      # V2Ray应用检测
                "img/ins.png",     # Instagram应用检测
                "img/chehui.png",  # 撤回按钮识别
                "img/chehui2.png", # 撤回按钮备用
                "img/douyin.png",  # 抖音相关
                "img/faxiaoxi.png" # 发消息相关
            ],
            "数据目录": [
                "data",
                "logs", 
                "screenshots"
            ]
        }
    
    def check_dist_directory(self):
        """检查dist目录是否存在"""
        print("🔍 检查dist目录")
        print("=" * 40)
        
        if not self.dist_dir.exists():
            print("❌ dist目录不存在，请先运行打包")
            return False
        
        print(f"✅ dist目录存在: {self.dist_dir}")
        
        # 列出dist目录内容
        items = list(self.dist_dir.iterdir())
        print(f"📁 dist目录包含 {len(items)} 个项目:")
        for item in items:
            if item.is_file():
                size_mb = item.stat().st_size / (1024 * 1024)
                print(f"  📄 {item.name} ({size_mb:.1f}MB)")
            else:
                print(f"  📁 {item.name}/")
        
        return True
    
    def verify_required_files(self):
        """验证必需文件"""
        print("\n🔍 验证必需文件")
        print("=" * 40)
        
        all_files_present = True
        
        for category, files in self.required_files.items():
            print(f"\n📋 {category}:")
            
            for file_path in files:
                full_path = self.dist_dir / file_path
                
                if full_path.exists():
                    if full_path.is_file():
                        size_kb = full_path.stat().st_size / 1024
                        print(f"  ✅ {file_path} ({size_kb:.1f}KB)")
                    else:
                        print(f"  ✅ {file_path}/ (目录)")
                else:
                    print(f"  ❌ {file_path} (缺失)")
                    all_files_present = False
        
        return all_files_present
    
    def check_image_files_detail(self):
        """详细检查图片文件"""
        print("\n🔍 详细检查图片文件")
        print("=" * 40)
        
        img_dir = self.dist_dir / "img"
        
        if not img_dir.exists():
            print("❌ img目录不存在 - 图色识别功能将无法工作！")
            print("💡 解决方案:")
            print("   1. 检查项目根目录是否有img文件夹")
            print("   2. 重新运行打包，确保--add-data=img;img参数正确")
            return False
        
        print(f"✅ img目录存在: {img_dir}")
        
        # 检查所有png文件
        png_files = list(img_dir.glob("*.png"))
        print(f"📸 找到 {len(png_files)} 个PNG图片文件:")
        
        for png_file in png_files:
            size_kb = png_file.stat().st_size / 1024
            print(f"  📷 {png_file.name} ({size_kb:.1f}KB)")
        
        # 检查关键图片
        critical_images = ["V2.png", "ins.png", "chehui.png"]
        missing_critical = []
        
        for img_name in critical_images:
            img_path = img_dir / img_name
            if not img_path.exists():
                missing_critical.append(img_name)
        
        if missing_critical:
            print(f"\n⚠️  缺少关键图片: {', '.join(missing_critical)}")
            print("💡 这些图片对以下功能至关重要:")
            print("   - V2.png: V2Ray应用启动检测")
            print("   - ins.png: Instagram应用状态检测")
            print("   - chehui.png: 消息撤回功能")
            return False
        else:
            print("\n✅ 所有关键图片文件都存在")
            return True
    
    def test_image_loading(self):
        """测试图片加载"""
        print("\n🔍 测试图片加载")
        print("=" * 40)
        
        try:
            import cv2
            img_dir = self.dist_dir / "img"
            
            if not img_dir.exists():
                print("❌ img目录不存在，跳过加载测试")
                return False
            
            test_images = ["V2.png", "ins.png", "chehui.png"]
            load_success = 0
            
            for img_name in test_images:
                img_path = img_dir / img_name
                if img_path.exists():
                    try:
                        img = cv2.imread(str(img_path))
                        if img is not None:
                            h, w = img.shape[:2]
                            print(f"  ✅ {img_name} 加载成功 ({w}x{h})")
                            load_success += 1
                        else:
                            print(f"  ❌ {img_name} 加载失败 (文件损坏)")
                    except Exception as e:
                        print(f"  ❌ {img_name} 加载异常: {e}")
                else:
                    print(f"  ⏭️  {img_name} 不存在，跳过")
            
            print(f"\n📊 图片加载测试结果: {load_success}/{len(test_images)} 成功")
            return load_success == len([img for img in test_images if (img_dir / img).exists()])
            
        except ImportError:
            print("⚠️  OpenCV未安装，跳过图片加载测试")
            return True
        except Exception as e:
            print(f"❌ 图片加载测试失败: {e}")
            return False
    
    def generate_fix_suggestions(self):
        """生成修复建议"""
        print("\n💡 修复建议")
        print("=" * 40)
        
        img_dir = self.dist_dir / "img"
        
        if not img_dir.exists():
            print("🎯 img文件夹缺失的解决方案:")
            print("   1. 检查项目根目录是否有img文件夹")
            print("   2. 重新运行打包命令:")
            print("      python quick_package.py")
            print("   3. 或者手动复制img文件夹到dist目录")
            print("   4. 验证打包配置中的--add-data=img;img参数")
        else:
            print("✅ img文件夹存在，图色识别功能应该正常工作")
        
        print("\n🔧 如果问题仍然存在:")
        print("   1. 删除dist和build目录")
        print("   2. 重新运行: python quick_package.py")
        print("   3. 运行验证: python verify_package_files.py")
    
    def run_verification(self):
        """运行完整验证"""
        print("🔍 打包文件完整性验证")
        print("目的: 确保所有必要文件都被正确打包，特别是图色识别相关文件")
        print("=" * 70)
        
        # 执行验证步骤
        steps = [
            ("检查dist目录", self.check_dist_directory),
            ("验证必需文件", self.verify_required_files),
            ("检查图片文件", self.check_image_files_detail),
            ("测试图片加载", self.test_image_loading),
        ]
        
        all_passed = True
        
        for step_name, step_func in steps:
            success = step_func()
            if not success:
                all_passed = False
        
        # 生成建议
        self.generate_fix_suggestions()
        
        # 显示最终结果
        print("\n" + "=" * 70)
        if all_passed:
            print("🎉 验证通过！所有必要文件都已正确打包")
            print("✅ 图色识别功能应该正常工作")
            print("💡 可以安全分发打包后的程序")
        else:
            print("❌ 验证失败！存在缺失文件")
            print("⚠️  图色识别功能可能无法正常工作")
            print("💡 请按照上述建议修复后重新打包")
        
        return all_passed

def main():
    """主函数"""
    try:
        verifier = PackageFileVerifier()
        success = verifier.run_verification()
        
        input("\n按Enter键退出...")
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        return 1
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
