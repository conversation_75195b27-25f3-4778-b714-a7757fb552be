# -*- coding: utf-8 -*-
"""
📦 Instagram任务管理器 - 超级优化打包程序
功能：极致压缩，目标20-30MB
"""

import os
import sys
import subprocess
from pathlib import Path

def package_app_optimized():
    """超级优化打包程序"""
    print("🚀 ins雷电中控 - 超级优化打包")
    print("=" * 50)

    # 询问是否清理旧的构建文件
    clean_build = input("是否清理旧的构建文件? (y/n, 默认y): ").strip().lower()
    if clean_build != 'n':
        import shutil
        build_dir = Path(__file__).parent / "build"
        dist_dir = Path(__file__).parent / "dist"

        if build_dir.exists():
            shutil.rmtree(build_dir)
            print("🗑️  已清理build目录")

        if dist_dir.exists():
            shutil.rmtree(dist_dir)
            print("🗑️  已清理dist目录")
    
    project_root = Path(__file__).parent
    
    # 1. 检查必要文件
    print("🔍 检查必要文件...")
    
    required_files = {
        "main.py": "主程序文件",
        "img": "图片资源目录（图色识别必需）",
        "app_config.json": "配置文件"
    }
    
    missing_files = []
    for file_path, description in required_files.items():
        full_path = project_root / file_path
        if full_path.exists():
            if file_path == "img":
                png_count = len(list(full_path.glob("*.png")))
                print(f"  ✅ {description} ({png_count}个图片)")
            else:
                print(f"  ✅ {description}")
        else:
            print(f"  ❌ {description} - 缺失")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ 缺少必要文件: {', '.join(missing_files)}")
        return False
    
    # 2. 安装PyInstaller
    print("\n📦 检查PyInstaller...")
    try:
        import PyInstaller
        print("  ✅ PyInstaller已安装")
    except ImportError:
        print("  📥 正在安装PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("  ✅ PyInstaller安装完成")
        except subprocess.CalledProcessError:
            print("  ❌ PyInstaller安装失败")
            return False
    
    # 3. 超级优化打包
    print("\n🚀 开始超级优化打包...")

    # 使用绝对路径确保文件被正确包含
    img_abs_path = str(project_root / "img")
    app_config_abs_path = str(project_root / "app_config.json")

    print(f"📁 img绝对路径: {img_abs_path}")

    cmd = [
        "pyinstaller",
        "--onefile",                              # 单文件
        "--windowed",                             # 隐藏控制台
        "--clean",                                # 清理缓存
        "--strip",                                # 🎯 去除调试信息
        "--name=ins雷电中控_优化版",               # 程序名
        f"--add-data={img_abs_path};img",         # 图片资源
        f"--add-data={app_config_abs_path};.",    # 配置文件
        
        # 🎯 精确导入核心模块
        "--hidden-import=PyQt6.QtCore",
        "--hidden-import=PyQt6.QtGui", 
        "--hidden-import=PyQt6.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=psutil",
        "--hidden-import=sqlite3",
        
        # 🎯 排除大型不必要模块
        "--exclude-module=matplotlib",
        "--exclude-module=pandas", 
        "--exclude-module=scipy",
        "--exclude-module=sklearn",
        "--exclude-module=tensorflow",
        "--exclude-module=torch",
        "--exclude-module=jupyter",
        "--exclude-module=IPython",
        "--exclude-module=notebook",
        "--exclude-module=sympy",
        "--exclude-module=statsmodels",
        "--exclude-module=seaborn",
        "--exclude-module=plotly",
        "--exclude-module=bokeh",
        
        # 🎯 排除PyQt6不必要组件
        "--exclude-module=PyQt6.QtWebEngine",
        "--exclude-module=PyQt6.QtWebEngineWidgets", 
        "--exclude-module=PyQt6.QtWebEngineCore",
        "--exclude-module=PyQt6.QtMultimedia",
        "--exclude-module=PyQt6.QtMultimediaWidgets",
        "--exclude-module=PyQt6.Qt3D",
        "--exclude-module=PyQt6.QtCharts",
        "--exclude-module=PyQt6.QtDataVisualization",
        "--exclude-module=PyQt6.QtDesigner",
        "--exclude-module=PyQt6.QtHelp",
        "--exclude-module=PyQt6.QtLocation",
        "--exclude-module=PyQt6.QtNfc",
        "--exclude-module=PyQt6.QtPositioning",
        "--exclude-module=PyQt6.QtQuick",
        "--exclude-module=PyQt6.QtQuickWidgets",
        "--exclude-module=PyQt6.QtRemoteObjects",
        "--exclude-module=PyQt6.QtSensors",
        "--exclude-module=PyQt6.QtSerialPort",
        "--exclude-module=PyQt6.QtSql",
        "--exclude-module=PyQt6.QtSvg",
        "--exclude-module=PyQt6.QtTest",
        "--exclude-module=PyQt6.QtWebChannel",
        "--exclude-module=PyQt6.QtWebSockets",
        
        # 🎯 排除OpenCV不必要组件
        "--exclude-module=cv2.aruco",
        "--exclude-module=cv2.bgsegm", 
        "--exclude-module=cv2.bioinspired",
        "--exclude-module=cv2.ccalib",
        "--exclude-module=cv2.datasets",
        "--exclude-module=cv2.dnn_superres",
        "--exclude-module=cv2.face",
        "--exclude-module=cv2.freetype",
        "--exclude-module=cv2.fuzzy",
        "--exclude-module=cv2.hdf",
        "--exclude-module=cv2.hfs",
        "--exclude-module=cv2.img_hash",
        "--exclude-module=cv2.intensity_transform",
        "--exclude-module=cv2.line_descriptor",
        "--exclude-module=cv2.mcc",
        "--exclude-module=cv2.optflow",
        "--exclude-module=cv2.phase_unwrapping",
        "--exclude-module=cv2.plot",
        "--exclude-module=cv2.quality",
        "--exclude-module=cv2.rapid",
        "--exclude-module=cv2.reg",
        "--exclude-module=cv2.rgbd",
        "--exclude-module=cv2.saliency",
        "--exclude-module=cv2.stereo",
        "--exclude-module=cv2.structured_light",
        "--exclude-module=cv2.superres",
        "--exclude-module=cv2.surface_matching",
        "--exclude-module=cv2.text",
        "--exclude-module=cv2.tracking",
        "--exclude-module=cv2.videoio",
        "--exclude-module=cv2.videostab",
        "--exclude-module=cv2.wechat_qrcode",
        "--exclude-module=cv2.xfeatures2d",
        "--exclude-module=cv2.ximgproc",
        "--exclude-module=cv2.xobjdetect",
        "--exclude-module=cv2.xphoto",
        
        # 🎯 排除NumPy不必要组件
        "--exclude-module=numpy.f2py",
        "--exclude-module=numpy.distutils",
        "--exclude-module=numpy.testing",
        
        # 🎯 优化选项
        "--optimize=2",                           # Python字节码优化
        "--noupx",                               # 禁用UPX（可能导致兼容性问题）
        
        "main.py"
    ]
    
    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 超级优化打包完成")
            
            # 检查输出文件
            exe_file = project_root / "dist" / "ins雷电中控_优化版.exe"
            if exe_file.exists():
                size_mb = exe_file.stat().st_size / (1024 * 1024)
                print(f"📦 输出文件: {exe_file}")
                print(f"📏 文件大小: {size_mb:.1f} MB")
                
                # 与原版本对比
                original_exe = project_root / "dist" / "ins雷电中控.exe"
                if original_exe.exists():
                    original_size_mb = original_exe.stat().st_size / (1024 * 1024)
                    reduction = ((original_size_mb - size_mb) / original_size_mb) * 100
                    print(f"📉 相比原版减少: {reduction:.1f}% ({original_size_mb:.1f}MB → {size_mb:.1f}MB)")
                
                # 验证关键文件
                print("\n🔍 验证打包结果...")
                dist_img = project_root / "dist" / "img"
                if dist_img.exists():
                    png_count = len(list(dist_img.glob("*.png")))
                    print(f"  ✅ img目录已包含 ({png_count}个图片)")
                else:
                    print("  ❌ img目录缺失 - 图色识别将无法工作")
                
                dist_config = project_root / "dist" / "app_config.json"
                if dist_config.exists():
                    print("  ✅ 配置文件已包含")
                else:
                    print("  ❌ 配置文件缺失")
                
                return True
            else:
                print("❌ 未找到输出文件")
                return False
        else:
            print("❌ 打包失败")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return False

def main():
    """主函数"""
    try:
        success = package_app_optimized()
        
        if success:
            print("\n🎉 超级优化打包成功！")
            print("💡 输出文件: dist/ins雷电中控_优化版.exe")
            print("💡 文件大小应该在20-30MB范围内")
            print("💡 可以将exe文件分发给用户使用")
        else:
            print("\n💥 打包失败")
            print("💡 请检查错误信息并重试")
        
        input("\n按Enter键退出...")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n💥 程序出错: {e}")

if __name__ == "__main__":
    main()
