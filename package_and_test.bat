@echo off
chcp 65001 >nul
echo 🚀 Instagram任务管理器 - 完整打包和测试流程
echo ================================================

:: 步骤1：环境检查
echo 📋 步骤1：检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)
echo ✅ Python环境正常

:: 步骤2：安装依赖
echo.
echo 📋 步骤2：安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

:: 步骤3：功能测试
echo.
echo 📋 步骤3：运行功能测试...
python test_package.py
if errorlevel 1 (
    echo ❌ 功能测试失败，请检查错误信息
    pause
    exit /b 1
)
echo ✅ 功能测试通过

:: 步骤4：执行打包
echo.
echo 📋 步骤4：开始打包程序...
python build_config.py
if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)
echo ✅ 打包完成

:: 步骤5：验证打包结果
echo.
echo 📋 步骤5：验证打包结果...
if exist "dist\Instagram任务管理器.exe" (
    echo ✅ 找到打包文件: dist\Instagram任务管理器.exe
    
    :: 显示文件大小
    for %%I in ("dist\Instagram任务管理器.exe") do (
        set size=%%~zI
        set /a size_mb=!size!/1024/1024
        echo 📏 文件大小: !size_mb! MB
    )
    
    :: 询问是否测试运行
    echo.
    set /p test_run="是否测试运行打包后的程序? (y/n): "
    if /i "!test_run!"=="y" (
        echo 🧪 启动测试运行...
        echo ⚠️  注意: 这将启动打包后的程序，请手动测试各项功能
        echo ⚠️  测试完成后请关闭程序窗口
        start "" "dist\Instagram任务管理器.exe"
        echo 💡 程序已启动，请在程序窗口中测试各项功能
    )
    
    :: 询问是否打开输出目录
    echo.
    set /p open_dir="是否打开输出目录? (y/n): "
    if /i "!open_dir!"=="y" (
        explorer dist
    )
    
) else (
    echo ❌ 未找到打包文件，打包可能失败
    pause
    exit /b 1
)

echo.
echo 🎉 打包流程完成！
echo 📁 输出文件: dist\Instagram任务管理器.exe
echo 💡 提示: 
echo    - 可以将exe文件复制到其他电脑测试
echo    - 确保目标电脑有必要的运行环境
echo    - 建议在不同系统版本上测试兼容性

pause
